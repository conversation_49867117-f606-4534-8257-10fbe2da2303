package upgrade

import (
	"distributor/application"
	"distributor/model"
	"distributor/service"
	"distributor/user"
	"go.uber.org/zap"
	orderModel "order/model"
	"yz-go/component/log"
	"yz-go/source"
)

// 通过订单升级
func OrderHandle(userId uint) (err error) {
	log.Log().Error("分销升级开始OrderHandle", zap.Any("userId", userId))
	// 查询分销商
	var distributor model.Distributor
	err, distributor = service.GetDistributorByUserId(userId)
	if err != nil {
		log.Log().Error("分销升级失败:查询分销商失败", zap.Any("err", err))
		return
	}
	var levels []model.DistributorLevel
	// 获取等级[]
	if distributor.ID == 0 {
		err, levels = getLevelsByWeight(0)
	} else {
		err, levels = getLevelsByWeight(distributor.LevelInfo.Weight)
	}
	if err != nil {
		log.Log().Error("分销升级失败:获取等级失败", zap.Any("err", err))
		return
	}
	if len(levels) == 0 {
		log.Log().Error("分销升级失败:没有可升级的等级")
		return
	}
	// 要升级的等级id
	var updateLevelId uint
	// 是否可以升级
	var res bool
	for _, level := range levels {
		if level.UpgradeCode == model.UpgradeCodeOR {
			log.Log().Error("分销升级开始:升级方式为或", zap.Any("userId", userId), zap.Any("level", level))
			// 升级条件为:或
			err, res = or(userId, level)
		} else {
			log.Log().Error("分销升级开始:升级方式为与", zap.Any("userId", userId), zap.Any("level", level))
			// 升级条件为:与
			err, res = with(userId, level)
		}
		if err != nil {
			log.Log().Error("分销升级失败:升级条件检查失败", zap.Any("err", err))
			return
		}
		if res == true {
			log.Log().Error("分销升级成功:满足升级条件", zap.Any("userId", userId), zap.Any("level", level))
			updateLevelId = level.ID
			break
		} else {
			continue
		}
	}
	if updateLevelId == 0 {
		log.Log().Error("分销升级失败:没有可升级的等级")
		return
	}
	if distributor.ID == 0 {
		log.Log().Error("分销升级失败:分销商不存在,成为分销商", zap.Any("userId", userId), zap.Any("updateLevelId", updateLevelId))
		// 成为
		distributor.Uid = userId
		distributor.LevelID = updateLevelId
		err = source.DB().Create(&distributor).Error
	} else {
		log.Log().Error("分销升级成功:分销商存在,升级分销商", zap.Any("distributorId", distributor.ID), zap.Any("updateLevelId", updateLevelId))
		// 升级
		err = updateDistributorLevelIdById(distributor.ID, updateLevelId)
	}
	if err != nil {
		log.Log().Error("分销升级失败:更新分销商等级失败", zap.Any("err", err))
		return
	}
	return
}

// 通过采购会员升级
func ApplicationHandle(userId uint) (err error) {
	var parentId uint
	err, parentId = user.GetParentIdByUserId(userId)
	// 查询分销商
	var distributor model.Distributor
	err, distributor = service.GetDistributorByUserId(parentId)
	if err != nil {
		return
	}
	var levels []model.DistributorLevel
	// 获取等级[]
	if distributor.ID == 0 {
		err, levels = getLevelsByWeight(0)
	} else {
		err, levels = getLevelsByWeight(distributor.LevelInfo.Weight)
	}
	if err != nil {
		return
	}
	if len(levels) == 0 {
		return
	}
	// 要升级的等级id
	var updateLevelId uint
	// 是否可以升级
	var res bool
	for _, level := range levels {
		if level.UpgradeCode == model.UpgradeCodeOR {
			// 升级条件为:或
			err, res = or(parentId, level)
		} else {
			// 升级条件为:与
			err, res = with(parentId, level)
		}
		if err != nil {
			return
		}
		if res == true {
			updateLevelId = level.ID
			break
		} else {
			continue
		}
	}
	if updateLevelId == 0 {
		//log.Log().Info("没有可升级的等级,返回")
		return
	}
	if distributor.ID == 0 {
		// 成为
		distributor.Uid = parentId
		distributor.LevelID = updateLevelId
		err = source.DB().Create(&distributor).Error
	} else {
		// 升级
		err = updateDistributorLevelIdById(distributor.ID, updateLevelId)
	}
	if err != nil {
		return
	}
	return
}

// 升级方式:与
func with(userId uint, level model.DistributorLevel) (err error, res bool) {
	// 等级没有勾选升级条件,不能升级
	if level.OrderAmountSwitch != 1 && level.OrderCountSwitch != 1 && level.RecommendUserSwitch != 1 && level.RecommendOrderAmountSwitch != 1 && level.RecommendOrderCountSwitch != 1 && level.BuyProductSwitch != 1 {
		res = false
		return
	}
	err, res = oneselfOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = oneselfOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = oneselfProductAmong(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = recommendUserTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = recommendOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	err, res = recommendOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == false {
		return
	}
	return
}

// 升级方式:或
func or(userId uint, level model.DistributorLevel) (err error, res bool) {
	err, res = oneselfOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = oneselfOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = oneselfProductAmong(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = recommendUserTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = recommendOrderCountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	err, res = recommendOrderAmountTotal(userId, level)
	if err != nil {
		return
	}
	if res == true {
		return
	}
	return
}

// 自己购买订单数量
func oneselfOrderCountTotal(userId uint, level model.DistributorLevel) (err error, res bool) {
	// 默认等于假
	res = false
	if level.OrderCountSwitch == 1 {
		log.Log().Error("oneselfOrderCountTotal() OrderCountSwitch == 1")
		var resTotal int64
		err, resTotal = getOrderCountTotalByUserId(userId)
		if err != nil {
			log.Log().Error("获取订单数量失败", zap.Any("err", err))
			return
		}
		log.Log().Error("oneselfOrderCountTotal() resTotal", zap.Any("resTotal", resTotal), zap.Any("level.OrderCountTotal", level.OrderCountTotal))
		if resTotal >= int64(level.OrderCountTotal) {
			log.Log().Error("oneselfOrderCountTotal() res = true")
			res = true
		}
	}
	return
}

// 自己购买订单总额
func oneselfOrderAmountTotal(userId uint, level model.DistributorLevel) (err error, res bool) {
	// 默认等于假
	res = false
	if level.OrderAmountSwitch == 1 {
		log.Log().Error("oneselfOrderAmountTotal() OrderAmountSwitch == 1")
		var resTotal int
		err, resTotal = getOrderAmountTotalByUserId(userId)
		if err != nil {
			log.Log().Error("获取订单总额失败", zap.Any("err", err))
			return
		}
		log.Log().Error("oneselfOrderAmountTotal() resTotal", zap.Any("resTotal", resTotal), zap.Any("level.OrderAmountTotal", level.OrderAmountTotal))
		if resTotal >= level.OrderAmountTotal {
			log.Log().Error("oneselfOrderAmountTotal() res = true")
			res = true
		}
	}
	return
}

// 自己购买指定商品之一
func oneselfProductAmong(userId uint, level model.DistributorLevel) (err error, res bool) {
	// 默认等于假
	res = false
	if level.BuyProductSwitch == 1 {
		log.Log().Error("oneselfProductAmong() BuyProductSwitch == 1")
		var productIds []uint
		err, productIds = getLevelUpdateProductIds(level.ID)
		if err != nil {
			log.Log().Error("获取升级商品失败", zap.Any("err", err))
			return
		}
		log.Log().Error("oneselfProductAmong() productIds", zap.Any("productIds", productIds))
		err, res = isBoughtProduct(productIds, userId)
		if err != nil {
			log.Log().Error("查询是否购买过升级商品失败", zap.Any("err", err))
			return
		}
		log.Log().Error("oneselfProductAmong() res", zap.Any("res", res))
	}
	return
}

// 推荐采购会员数量
func recommendUserTotal(userId uint, level model.DistributorLevel) (err error, res bool) {
	// 默认等于假
	res = false
	if level.RecommendUserSwitch == 1 {
		log.Log().Error("recommendUserTotal() RecommendUserSwitch == 1")
		var childIds []uint
		err, childIds = user.GetChildIdsByUserId(userId)
		if err != nil {
			log.Log().Error("获取下级用户失败", zap.Any("err", err))
			return
		}
		log.Log().Error("recommendUserTotal() childIds", zap.Any("childIds", childIds))
		var total int64
		err, total = application.GetTotalByUserIds(childIds)
		if err != nil {
			log.Log().Error("获取下级用户采购总数失败", zap.Any("err", err))
			return
		}
		log.Log().Error("recommendUserTotal() total", zap.Any("total", total), zap.Any("level.RecommendUserTotal", level.RecommendUserTotal))
		if int(total) >= level.RecommendUserTotal {
			res = true
		}
		log.Log().Error("recommendUserTotal() res", zap.Any("res", res))
	}
	return
}

// 推荐采购订单数量
func recommendOrderCountTotal(userId uint, level model.DistributorLevel) (err error, res bool) {
	// 默认等于假
	res = false
	if level.RecommendOrderCountSwitch == 1 {
		log.Log().Error("recommendOrderCountTotal() RecommendOrderCountSwitch == 1")
		var childIds []uint
		err, childIds = user.GetChildIdsByUserId(userId)
		if err != nil {
			log.Log().Error("获取下级用户失败", zap.Any("err", err))
			return
		}
		log.Log().Error("recommendOrderCountTotal() childIds", zap.Any("childIds", childIds))
		var total int64
		err, total = getOrderCountTotalByUserIds(childIds)
		if err != nil {
			log.Log().Error("获取下级用户采购订单总数失败", zap.Any("err", err))
			return
		}
		log.Log().Error("recommendOrderCountTotal() total", zap.Any("total", total), zap.Any("level.RecommendOrderCountTotal", level.RecommendOrderCountTotal))
		if int(total) >= level.RecommendOrderCountTotal {
			res = true
		}
		log.Log().Error("recommendOrderCountTotal() res", zap.Any("res", res))
	}
	return
}

// 推荐采购订单总额
func recommendOrderAmountTotal(userId uint, level model.DistributorLevel) (err error, res bool) {
	// 默认等于假
	res = false
	if level.RecommendOrderAmountSwitch == 1 {
		log.Log().Error("recommendOrderAmountTotal() RecommendOrderAmountSwitch == 1")
		var childIds []uint
		err, childIds = user.GetChildIdsByUserId(userId)
		if err != nil {
			log.Log().Error("获取下级用户失败", zap.Any("err", err))
			return
		}
		log.Log().Error("recommendOrderAmountTotal() childIds", zap.Any("childIds", childIds))
		var total int
		err, total = getOrderAmountTotalByUserIds(childIds)
		if err != nil {
			log.Log().Error("获取下级用户采购订单总额失败", zap.Any("err", err))
			return
		}
		log.Log().Error("recommendOrderAmountTotal() total", zap.Any("total", total), zap.Any("level.RecommendOrderAmountTotal", level.RecommendOrderAmountTotal))
		if total >= level.RecommendOrderAmountTotal {
			res = true
		}
		log.Log().Error("recommendOrderAmountTotal() res", zap.Any("res", res))
	}
	return
}

type Order struct {
	source.Model
	CreatedAt            *source.LocalTime      `json:"created_at" gorm:"index;"`
	UpdatedAt            *source.LocalTime      `json:"updated_at" gorm:"index;"`
	OrderSN              uint                   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`                                                 // 编号
	ThirdOrderSN         string                 `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"`                               // 编号
	Key                  string                 `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(255);size:255;"`                                     // 标识
	Title                string                 `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`                               // 标题
	Status               orderModel.OrderStatus `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;"`                        // 订单状态
	Amount               uint                   `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"`                                                 // 订单总金额
	ItemAmount           uint                   `json:"item_amount" form:"item_amount" gorm:"column:item_amount;comment:商品市场价(分);"`                                  // 商品市场价
	SupplyAmount         uint                   `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(分);"`                             // 供货金额
	CostAmount           uint                   `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(分);"`                                   // 成本金额
	Freight              uint                   `json:"freight" form:"freight" gorm:"column:freight;comment:运费(分);"`                                                 // 运费(单位:分)
	ServiceFee           uint                   `json:"service_fee" form:"service_fee" gorm:"column:service_fee;comment:服务费(分);"`                                    // 服务费
	GoodsCount           uint                   `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:商品总数;"`                            // 商品总数
	TechnicalServicesFee uint                   `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"` // 技术服务费
	UserID               uint                   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`
	ApplicationID        uint                   `json:"application_id" form:"application_id" gorm:"column:application_id;comment:应用id;index;"`
	OrderItems           OrderItems             `json:"order_items"`
	GatherSupplyID       uint                   `json:"gather_supply_id" form:"gather_supply_id" gorm:"column:gather_supply_id;comment:供应链id;index;"`
	PayTypeID            int                    `json:"pay_type_id" form:"pay_type_id"` // 订单支付方式
}

type OrderItems []OrderItem
type OrderItem struct {
	source.Model
	Key             string `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(255);size:255;"`                               // 标识
	Title           string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(255);size:255;"`                         // 名
	SkuTitle        string `json:"sku_title" form:"sku_title" gorm:"column:sku_title;comment:sku标题;type:varchar(255);size:255;"`          // 规格名
	Unit            string `json:"unit" form:"unit" gorm:"column:unit;comment:单位;type:varchar(20);size:20;"`                              // 单位
	Qty             uint   `json:"qty" form:"qty" gorm:"column:qty;comment:商品数量;"`                                                        // 商品数量
	Amount          uint   `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`                                                 // 总价(元)
	PaymentAmount   uint   `json:"payment_amount" form:"payment_amount" gorm:"column:payment_amount;comment:均摊支付金额(元);"`                  // 均摊支付金额(元)
	DiscountAmount  uint   `json:"discount_amount" form:"discount_amount" gorm:"column:discount_amount;comment:优惠金额(元);"`                 // 优惠金额(元)
	DeductionAmount uint   `json:"deduction_amount" form:"deduction_amount" gorm:"column:deduction_amount;comment:抵扣金额(元);"`              // 抵扣金额(元)
	CostAmount      uint   `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(元);"`                             // 成本金额(元)
	SupplyAmount    uint   `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(元);"`                       // 供货金额(元)
	ImageUrl        string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`           // 图片地址
	SendStatus      int    `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态;type:smallint;size:3;"` // 发货状态

	TradeID    uint `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`           // 交易id
	SupplierID uint `json:"supplier_id" form:"supplier_id" gorm:"column:supplier_id;comment:供应商id;"` // 供应商id
	UserID     uint `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`              // 用户id
	OrderID    uint `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`           // 订单id
	ProductID  uint `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;"`     // 产品id
	SkuID      uint `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku id;"`               // sku id
}

// 通过订单id获取订单
func GetOrderById(id uint) (err error, order Order) {
	err = source.DB().Preload("OrderItems").Where("id = ?", id).First(&order).Error
	return
}

// 通过会员id获取订单总数
func getOrderCountTotalByUserId(userId uint) (err error, total int64) {
	err = source.DB().Model(&Order{}).Where("user_id = ? AND status >= ?", userId, orderModel.WaitSend).Count(&total).Error
	return
}

// 通过会员ids获取订单总数
func getOrderCountTotalByUserIds(userIds []uint) (err error, total int64) {
	err = source.DB().Model(&Order{}).Where("user_id IN ? AND status >= ?", userIds, orderModel.WaitSend).Count(&total).Error
	return
}

// 通过会员id获取订单总额
func getOrderAmountTotalByUserId(userId uint) (err error, total int) {
	err = source.DB().Model(&Order{}).Where("user_id = ? AND status >= ?", userId, orderModel.WaitSend).Pluck("COALESCE(SUM(amount), 0) as amount1", &total).Error
	return
}

// 通过会员ids获取订单总额
func getOrderAmountTotalByUserIds(userIds []uint) (err error, total int) {
	err = source.DB().Model(&Order{}).Where("user_id IN ? AND status >= ?", userIds, orderModel.WaitSend).Pluck("COALESCE(SUM(amount), 0) as amount1", &total).Error
	return
}

// 通过等级id获取升级需要购买的商品ids
func getLevelUpdateProductIds(levelId uint) (err error, productIds []uint) {
	err = source.DB().Model(&model.DistributorLevelUpgradeProduct{}).Where("distributor_level_id = ?", levelId).Pluck("product_id", &productIds).Error
	return
}

// 通过等级权重获取等级[]
func getLevelsByWeight(weight int) (err error, levels []model.DistributorLevel) {
	err = source.DB().Where("weight > ?", weight).Order("weight DESC").Find(&levels).Error
	return
}

// 验证是否购买过可以升级的商品
func isBoughtProduct(productIds []uint, userId uint) (err error, res bool) {
	var orderIds []uint
	err = source.DB().Model(&Order{}).Where("user_id = ? AND status >= ?", userId, orderModel.WaitSend).Pluck("id", &orderIds).Error
	if err != nil {
		return
	}
	var total int64
	err = source.DB().Model(&OrderItem{}).Where("order_id IN ? AND product_id IN ?", orderIds, productIds).Count(&total).Error
	if total > 0 {
		res = true
	}
	return
}

// 通过分销id修改分销商等级
func updateDistributorLevelIdById(distributorId uint, levelId uint) (err error) {
	err = source.DB().Model(&model.Distributor{}).Where("id = ?", distributorId).Update("level_id", levelId).Error
	return
}
