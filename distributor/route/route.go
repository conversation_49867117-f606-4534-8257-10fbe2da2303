package route

import (
	fv1 "distributor/api/f/v1"
	v1 "distributor/api/v1"
	"github.com/gin-gonic/gin"
)

func InitAdminPublicRouter(Router *gin.RouterGroup) {
	IRouter := Router.Group("distributor")
	{
		IRouter.POST("fixUpgrade", v1.FixUpgrade)
		IRouter.POST("fix", v1.Fix)
		IRouter.POST("fixMq", v1.FixMq)
		IRouter.POST("compensate", v1.Compensate)
		IRouter.POST("compensateSettle", v1.CompensateSettle)
	}
}

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	DistributorRouter := Router.Group("distributor")
	{
		// 基础设置
		DistributorRouter.GET("findSetting", v1.FindSetting)     // 查询Setting
		DistributorRouter.PUT("updateSetting", v1.UpdateSetting) // 更新Setting
		// 分销等级
		DistributorRouter.POST("createLevel", v1.CreateLevel)   // 新建分销等级
		DistributorRouter.DELETE("deleteLevel", v1.DeleteLevel) // 删除分销等级
		DistributorRouter.PUT("updateLevel", v1.UpdateLevel)    // 更新分销等级
		DistributorRouter.GET("findLevel", v1.FindLevel)        // 根据ID获取分销等级
		DistributorRouter.GET("getLevelList", v1.GetLevelList)  // 分页获取分销等级列表
		DistributorRouter.GET("getAllLevel", v1.GetAllLevel)    // 获取全部分销等级无分页
		// 分销商
		DistributorRouter.PUT("updateDistributor", v1.UpdateDistributor)         // 更新分销商
		DistributorRouter.GET("findDistributor", v1.FindDistributor)             // 根据ID获取分销商
		DistributorRouter.POST("addOrEditDistributor", v1.AddOrEditDistributor)  // 创建或修改分销商
		DistributorRouter.GET("getDistributorList", v1.GetDistributorList)       // 分页获取分销商列表
		DistributorRouter.GET("exportDistributorList", v1.ExportDistributorList) // 导出分销商
		DistributorRouter.POST("blacklist", v1.Blacklist)                        // 黑名单
		// 分销分成
		DistributorRouter.GET("getAwardList", v1.GetAwardList)       // 分页获取分销分成列表
		DistributorRouter.GET("exportAwardList", v1.ExportAwardList) // 导出分销分成
		// 分销商开通记录
		DistributorRouter.GET("getPurchaseRecordList", v1.GetPurchaseRecordList)
	}
}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {
	DistributorRouter := Router.Group("distributor")
	{
		// 验证会员是否为分销商
		DistributorRouter.GET("verifyIdentity", fv1.VerifyIdentity)
		// 获取分销数据
		DistributorRouter.GET("getCenterInfo", fv1.GetCenterInfo)
		// 获取可以续费和升级的分销商等级列表-无分页
		DistributorRouter.GET("getRenewLevels", fv1.GetRenewLevels)
		// 获取开通记录
		DistributorRouter.GET("getPurchaseRecord", fv1.GetPurchaseRecord)
		// 付费开通
		DistributorRouter.POST("purchase", fv1.Purchase)
	}
}
