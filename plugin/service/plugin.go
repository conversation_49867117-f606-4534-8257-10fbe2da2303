package service

import (
	"gin-vue-admin/cmd/gva"
	"github.com/chenhg5/collection"
	"plugin/model"
	"yz-go/source"
	"yz-go/utils"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetApplicationInfoList
//@description: 分页获取Application记录
//@param: info request.ApplicationSearch
//@return: err error, list interface{}, total int64

func GetPluginList() (err error, list []model.Plugin, total int64) {
	// 插件的图片现在保存在gin-vue-admin/web/src/assets/plugin_icon

	listPlugin := []model.Plugin{
		{
			Model: source.Model{ID: 1},
			Name:  "区域分红",
			Desc:  "线上实现行政区域代理渠道，依据地址进行分红",
			Logo:  "area_dividend.png",
			Path:  "bonusSetting",
		},
		{
			Model: source.Model{ID: 2},
			Name:  "分销",
			Desc:  "推荐采购商获得流水分成",
			Logo:  "commission.png",
			Path:  "distributorLog",
		},
		{
			Model: source.Model{ID: 3},
			Name:  "招商",
			Desc:  "推荐供应商入驻获得流水分成",
			Logo:  "merchant.png",
			Path:  "merchantBaseIndex",
		},
		{
			Model: source.Model{ID: 9},
			Name:  "共享选品专辑",
			Desc:  "",
			Logo:  "share_album.png",
			Path:  "shareAlbumListIndex",
		},
		{
			Model: source.Model{ID: 17},
			Name:  "小商店",
			Desc:  "",
			Logo:  "micro.png",
			Path:  "shopOrderIndex",
		}, {
			Model: source.Model{ID: 34},
			Name:  "价格权限",
			Desc:  "价格权限",
			Logo:  "lease_toy.png",
			Path:  "userLevelPriceAuthBaseSetting",
		},
		{
			Model: source.Model{ID: 35},
			Name:  "大昌行ERP",
			Desc:  "大昌行ERP",
			Logo:  "dahangErp.png",
			Path:  "dahangBaseIndex",
		},
		{
			Model: source.Model{ID: 41},
			Name:  "海报",
			Desc:  "分享海报，锁客裂变！",
			Logo:  "share_album.png",
			Path:  "posterManageList",
		},
		{
			Model: source.Model{ID: 42},
			Name:  "代理",
			Desc:  "代理",
			Logo:  "share_album.png",
			Path:  "agentBaseIndex",
		},
		//{
		//	Model: source.Model{ID: 4},
		//	Name:  "必应鸟卡券",
		//	Desc:  "对接必应鸟平台品牌卡券充值资源!",
		//	Logo:  "@/assets/plugin_icon/marketing/commission.png",
		//	Path:  "brandCardBaseindex",
		//},
		//{
		//	Model: source.Model{ID: 5},
		//	Name:  "福禄",
		//	Desc:  "福禄!",
		//	Logo:  "@/assets/plugin_icon/marketing/commission.png",
		//	Path:  "fuluBaseIndex",
		//},
	}

	for _, item := range listPlugin {
		if utils.LocalEnv() != true { //本地环境
			list = append(list, item)
		} else {
			if collection.Collect(gva.GlobalAuth.MarketingPlugin).Contains(item.ID) == true {
				list = append(list, item)
			}

		}

	}
	total = int64(len(list))
	return
}

func GetResourcePluginList() (err error, list []model.Plugin, total int64) {
	// 插件的图片现在保存在gin-vue-admin/web/src/assets/plugin_icon

	//if gva.DomainName == "yx.gz.cn" {
	//list = append(list, model.Plugin{
	//	Model: source.Model{ID: 5},
	//	Name:  "福禄",
	//	Desc:  "福禄!",
	//	Logo:  "@/assets/plugin_icon/marketing/commission.png",
	//	Path:  "fuluBaseIndex",
	//})
	//total = 1
	//}
	listPlugin := []model.Plugin{}

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 5},
		Name:  "数字权益商品",
		Desc:  "数字权益商品!",
		Logo:  "fulu.png",
		Path:  "fuluOrderManageIndex",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 8},
		Name:  "商品转移",
		Desc:  "商品转移",
		Logo:  "transfer_product.png",
		Path:  "selectedTransferProductIndex",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 7},
		Name:  "阿里巴巴",
		Desc:  "阿里巴巴",
		Logo:  "commission.png",
		Path:  "alibabaList",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 10},
		Name:  "联联周边游",
		Desc:  "联联周边游",
		Logo:  "lianlian.png",
		Path:  "lianlianOrderIndex",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 23},
		Name:  "电影票",
		Desc:  "百米电影票",
		Logo:  "cinema_ticket.png",
		Path:  "cinemaTicketBaseIndex",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 14},
		Name:  "电商cps",
		Desc:  "电商cps",
		Logo:  "ec_cps.png",
		Path:  "tikTokCPSOrderManage",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 15},
		Name:  "蛋糕叔叔",
		Desc:  "蛋糕叔叔",
		Logo:  "uncle_cake.png",
		Path:  "uncleCakeBase",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 18},
		Name:  "课程分发",
		Desc:  "课程分发",
		Logo:  "manage_course.png",
		Path:  "manageCourse",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 19},
		Name:  "会员权益",
		Desc:  "会员权益!",
		Logo:  "commission.png",
		Path:  "userEquityOrderManageIndex",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 20},
		Name:  "聚推联盟",
		Desc:  "聚合推广cps",
		Logo:  "poly_push.png",
		Path:  "polyPushBaseIndex",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 25},
		Name:  "短视频分发",
		Desc:  "短视频分发",
		Logo:  "video_distribute.png",
		Path:  "videoDistributeManageList",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 27},
		Name:  "素材分发",
		Desc:  "素材分发",
		Logo:  "material_distribution.png",
		Path:  "materialDistributionManageList",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 29},
		Name:  "话术分发",
		Desc:  "统一上传话术库，同步商城企业微信话术库",
		Logo:  "material_distribution.png",
		Path:  "scriptDistributionManageList",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 13},
		Name:  "知识库分发",
		Desc:  "知识库!",
		Logo:  "knowledge.png",
		Path:  "kownledgeBaseIndex",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 30},
		Name:  "共享直播",
		Desc:  "共享直播",
		Logo:  "share_live.png",
		Path:  "shareLiveBaseIndex",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 38},
		Name:  "美团分销联盟",
		Desc:  "美团分销联盟",
		Logo:  "poly_push.png",
		Path:  "meituanSetting",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 39},
		Name:  "团购活动分发",
		Desc:  "团购活动分发",
		Logo:  "group_event.png",
		Path:  "groupEventSetting",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 43},
		Name:  "抖音团购",
		Desc:  "抖音团购",
		Logo:  "tiktok_groupon.png",
		Path:  "tiktokGrouponOrderManage",
	})

	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 44},
		Name:  "本地生活",
		Desc:  "品牌入驻，项目销售!",
		Logo:  "fulu.png",
		Path:  "localLifeMBIndex",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 45},
		Name:  "广电售卡",
		Desc:  "广电售卡",
		Logo:  "tiktok_groupon.png",
		Path:  "base",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 46},
		Name:  "电商cps中控",
		Desc:  "电商cps中控",
		Logo:  "ec_cps_ctrl.png",
		Path:  "eCommerceCPSBase",
	})
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 21},
		Name:  "租赁",
		Desc:  "租赁",
		Logo:  "lease_toy.png",
		Path:  "leaseBaseSettingIndex",
	})

	for _, item := range listPlugin {
		if utils.LocalEnv() != true { //本地环境
			list = append(list, item)
		} else {
			if collection.Collect(gva.GlobalAuth.ResourcesPlugin).Contains(item.ID) == true {
				list = append(list, item)
			}
		}

	}

	total = int64(len(list))
	return
}

var ToolsPluginList []model.Plugin

func GetToolsPluginList() (err error, list []model.Plugin, total int64) {

	var ToolsPlugin []model.Plugin
	ToolsPlugin = append(ToolsPlugin, ToolsPluginList...)

	// 插件的图片现在保存在gin-vue-admin/web/src/assets/plugin_icon
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 6},
		Name:  "发票管理",
		Desc:  "发票管理!",
		Logo:  "commission.png",
		Path:  "billBaseIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 22},
		Name:  "电子面单",
		Desc:  "电子面单",
		Logo:  "print.png",
		Path:  "printSettingIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 12},
		Name:  "专题",
		Desc:  "专题!",
		Logo:  "supply_subject.png",
		Path:  "supplySubjectBase",
	})

	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 11},
		Name:  "短信通知",
		Desc:  "短信通知!",
		Logo:  "sms.png",
		Path:  "SMSFinanceIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 16},
		Name:  "采购账户",
		Desc:  "自定义资金账户定向采购!",
		Logo:  "purchase_account.png",
		Path:  "purchaseAccountList",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 24},
		Name:  "订单导出",
		Desc:  "订单导出",
		Logo:  "print.png",
		Path:  "orderExportList",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 26},
		Name:  "聚水潭",
		Desc:  "聚水潭!",
		Logo:  "jushuitan.png",
		Path:  "jushuitanBaseIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 28},
		Name:  "采购端库存",
		Desc:  "采购端库存!",
		Logo:  "virtual_stock.png",
		Path:  "virtualStockIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 36},
		Name:  "分销同步",
		Desc:  "分销同步!",
		Logo:  "print.png",
		Path:  "distributionSynchronizationBaseIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 37},
		Name:  "工猫提现",
		Desc:  "工猫提现",
		Logo:  "print.png",
		Path:  "withdrawSetting",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 40},
		Name:  "商品池专辑",
		Desc:  "商品池专辑",
		Logo:  "print.png",
		Path:  "thousandPriceMangeIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 47},
		Name:  "ai助手",
		Desc:  "ai助手",
		Logo:  "print.png",
		Path:  "basicSettingIndex",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 33},
		Name:  "数据通系统",
		Desc:  "数据通系统",
		Logo:  "serviceProvider.png",
		Path:  "serviceProviderSystemBase",
	})
	ToolsPlugin = append(ToolsPlugin, model.Plugin{
		Model: source.Model{ID: 48},
		Name:  "聚合支付分账",
		Desc:  "聚合支付分账",
		Logo:  "print.png",
		Path:  "baseIndex",
	})

	for _, item := range ToolsPlugin {
		if utils.LocalEnv() != true { //本地环境
			list = append(list, item)
		} else {
			if collection.Collect(gva.GlobalAuth.ToolsPlugin).Contains(item.ID) == true {
				list = append(list, item)
			}
		}

	}

	total = int64(len(list))
	return
}
func GetIndustryPluginList() (err error, list []model.Plugin, total int64) {

	listPlugin := append(list)

	for _, item := range listPlugin {
		if utils.LocalEnv() != true { //本地环境
			list = append(list, item)
		} else {
			if collection.Collect(gva.GlobalAuth.IndustryPlugin).Contains(item.ID) == true {
				list = append(list, item)
			}
		}

	}

	total = int64(len(list))
	return
}

func GetChannelPluginList() (err error, list []model.Plugin, total int64) {
	var listPlugin []model.Plugin
	listPlugin = append(listPlugin, model.Plugin{
		Model: source.Model{ID: 31},
		Name:  "视频号小店",
		Desc:  "小商店对接视频号小店!",
		Logo:  "shop_assistant.png",
		Path:  "smallShopVideoManageList",
	})
	for _, item := range listPlugin {
		if utils.LocalEnv() != true { //本地环境
			list = append(list, item)
		} else {
			if collection.Collect(gva.GlobalAuth.ChannelPlugin).Contains(item.ID) == true {
				list = append(list, item)
			}
		}

	}

	total = int64(len(list))
	return
}

func GetAllPluginList() (err error, list []model.Plugin, total int64) {
	err, toolsPluginList, toolstotal := GetToolsPluginList()
	list = append(list, toolsPluginList...)
	total += toolstotal
	err, channelPluginList, channgetotal := GetChannelPluginList()
	list = append(list, channelPluginList...)
	total += channgetotal
	err, resourcePluginList, resourcetotal := GetResourcePluginList()
	list = append(list, resourcePluginList...)
	total += resourcetotal
	err, pluginList, ptotal := GetPluginList()
	list = append(list, pluginList...)
	total += ptotal
	return
}
