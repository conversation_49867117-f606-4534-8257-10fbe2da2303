package notify

import (
	"errors"
	"go.uber.org/zap"
	tOrder "order/order"
	"payment"
	"payment/model"
	paymentpay "payment/pay"
	serviceProviderSystemCommon "service-provider-system/common"
	smallshopmodel "small-shop/model"
	smallshoppayment "small-shop/payment"
	smallshoporder "small-shop/small-shop-order"
	"strconv"
	"yz-go/component/log"
	"yz-go/source"
)

type AggregatePaymentPayNotifyRequest struct {
	Status           int                                `json:"status"`
	Attach           serviceProviderSystemCommon.Attach `json:"attach"`
	PaymentStoreCode string                             `json:"payment_store_code"`
	OrderCode        string                             `json:"order_code"`
	ThirdOrderCode   string                             `json:"third_order_code"`
	RequestType      string                             `json:"request_type"`
	PayPrice         float64                            `json:"pay_price"`
	StatusDesc       string                             `json:"status_desc"`
	ErrMsg           string                             `json:"err_msg"`
	AppKey           string                             `json:"app_key"`
	PassBack         string                             `json:"pass_back"`
	PayType          string                             `json:"pay_type"`
	Sign             string                             `json:"sign"`
}

// 微信公众号支付回调
func AggregatePaymentPayNotify(aggregatePaymentPayNotifyRequest AggregatePaymentPayNotifyRequest) (err error) {
	log.Log().Error("收到数据通支付支付回调", zap.Any("aggregatePaymentPayNotifyRequest", aggregatePaymentPayNotifyRequest))
	var weChatNotify serviceProviderSystemCommon.Attach
	////attach,_ := json.Marshal(plaintext.Attach)
	//_ = json.Unmarshal([]byte(aggregatePaymentPayNotifyRequest.Attach), &weChatNotify)
	weChatNotify = aggregatePaymentPayNotifyRequest.Attach
	//小商店支付  0平台 1小商店
	if weChatNotify.PayCode == 1 {
		var payInfo smallshopmodel.SmallShopPayInfo
		err = source.DB().Where("pay_sn = ?", weChatNotify.PaySN).First(&payInfo).Error
		if err != nil {
			log.Log().Error("数据通-聚合支付-小商店回调:获取支付数据错误", zap.Any("err", err.Error()))
			err = errors.New("数据通-聚合支付-小商店回调:获取支付数据错误" + err.Error())
			return
		}
		if payInfo.Status != 0 {
			return
		}
		payInfo.TransactionId = aggregatePaymentPayNotifyRequest.ThirdOrderCode
		payInfo.Type = model.AGGREGATE_PAYMENT
		if payInfo.SplitCreateStatus == 0 {
			payInfo.SplitCreateStatus = 1
		}
		err = source.DB().Save(&payInfo).Error
		if err != nil {
			err = errors.New("数据通-聚合支付-小商店填写微信支付单号出错" + err.Error())
			return
		}
		err = smallshoppayment.Pay(strconv.Itoa(int(payInfo.PaySN)), weChatNotify.PayType)
		if err != nil {
			log.Log().Error(strconv.Itoa(int(payInfo.PaySN))+"数据通-聚合支付-小商店:支付状态变更失败!", zap.Any("info", err))
			err = errors.New("数据通-聚合支付-小商店:支付状态变更失败" + err.Error())
			return
		}
		var orderList []smallshopmodel.SmallShopOrderPayInfo
		err = source.DB().Where("small_shop_pay_info_id =  ?", payInfo.ID).Find(&orderList).Error
		if err != nil {
			log.Log().Error("数据通-聚合支付-小商店处理出错!", zap.Any("info", err))
			err = errors.New("数据通-聚合支付-小商店处理出错" + err.Error())
			return
		}
		for _, item := range orderList {
			err = smallshoporder.Pay(item.SmallShopOrderID, weChatNotify.PayType, item.SmallShopPayInfoID)
			if err != nil {
				log.Log().Error("数据通-聚合支付：订单支付出错!", zap.Any("err", err.Error()))
				err = errors.New("数据通-聚合支付：订单支付出错" + err.Error())
				return
			}
		}
	} else {
		switch weChatNotify.Type {
		case 1: //充值
			var balance paymentpay.StationBalance
			var purBalance model.RechargeBalance

			err = source.DB().First(&purBalance, "pay_sn=?", weChatNotify.PaySN).Error
			if err != nil {
				log.Log().Error("微信支付站内会员充值失败~!", zap.Any("err", err))
			}
			if purBalance.Status == 1 {
				log.Log().Error("数据通-聚合支付-站内会员充值已完成无需再次~!", zap.Any("purBalance", purBalance))
				return nil
			}
			purBalance.Status = 1
			purBalance.Type = model.AGGREGATE_PAYMENT
			source.DB().Updates(purBalance)

			balance.StationBalanceRecharge.Uid = purBalance.Uid
			balance.StationBalanceRecharge.Amount = purBalance.Amount
			balance.StationBalanceRecharge.Action = 1
			if err, _ = paymentpay.Recharge(&balance); err != nil {
				log.Log().Error("数据通-聚合支付-站内会员充值失败!", zap.Any("err", err))
				//yzResponse.FailWithMessage("会员充值失败", c)
				return
			}
			break
		default: //支付
			var payInfo model.PayInfo
			err = source.DB().Where("pay_sn = ?", aggregatePaymentPayNotifyRequest.OrderCode).First(&payInfo).Error
			if err != nil {
				log.Log().Error("数据通-聚合支付:获取支付数据错误", zap.Any("err", err.Error()))
				err = errors.New("数据通-聚合支付:获取支付数据错误" + err.Error())
				return
			}
			if payInfo.Status != 0 {
				return
			}
			payInfo.TransactionId = aggregatePaymentPayNotifyRequest.ThirdOrderCode
			payInfo.Type = model.AGGREGATE_PAYMENT
			payInfo.Desc = weChatNotify.WxOpenid
			err = source.DB().Save(&payInfo).Error
			if err != nil {
				err = errors.New("数据通-聚合支付:填写数据通单号出错" + err.Error())
				return
			}
			err = payment.Pay(strconv.Itoa(int(payInfo.PaySN)), weChatNotify.PayType)
			if err != nil {
				log.Log().Error(strconv.Itoa(int(payInfo.PaySN))+"数据通-聚合支付:支付状态变更失败!", zap.Any("info", err))
				err = errors.New("数据通-聚合支付:支付状态变更失败" + err.Error())
				return
			}
			var orderList []model.OrderPayInfo
			err = source.DB().Where("pay_info_id =  ?", payInfo.ID).Find(&orderList).Error
			if err != nil {
				log.Log().Error("数据通-聚合支付处理出错!", zap.Any("info", err))
				err = errors.New("数据通-聚合支付处理出错" + err.Error())
				return
			}

			for _, item := range orderList {
				err = tOrder.Pay(item.OrderID, weChatNotify.PayType, item.PayInfoID)
				if err != nil {
					log.Log().Error("数据通-聚合支付：订单支付出错!", zap.Any("err", err.Error()))
					err = errors.New("数据通-聚合支付：订单支付出错" + err.Error())
					return
				}
			}
			break
		}
	}

	return
}
