package service

import (
	"errors"
	model2 "finance/model"
	"gorm.io/gorm"
	"promotion/model"
	"promotion/request"
	"yz-go/source"
)

func GetDistributorAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.DistributorAward{})
	dbStatistic := source.DB().Model(&model.DistributorAward{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("uid = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("child_uid = ?", 0)
		} else {
			db.Where("child_uid in ?", userIds)
		}
	}
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			req.SettleStatus = 0
		} else if req.SettleStatus == 2 {
			req.SettleStatus = 1
		}
		db.Where("status = ?", req.SettleStatus)
	}
	var awards []model.DistributorAward
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("status = ?", 1).First(&amountTotal).Error
	err = db.Preload("Order").Preload("Order.OrderItems").Preload("Order.User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetAreaAgencyAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.AreaAgencyAward{})
	dbStatistic := source.DB().Model(&model.AreaAgencyAward{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("uid = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		var orderIDs []uint
		err = source.DB().Model(&model.Order{}).Where("order_sn = ?", req.OrderSN).Pluck("id", &orderIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(orderIDs) == 0 {
			db.Where("order_id = ?", 0)
		} else {
			db.Where("order_id in ?", orderIDs)
		}
	}
	if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("order_id = ?", 0)
		} else {
			var orderIDs []uint
			err = source.DB().Model(&model.Order{}).Where("user_id in ?", userIds).Pluck("id", &orderIDs).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
			if len(orderIDs) == 0 {
				db.Where("order_id = ?", 0)
			} else {
				db.Where("order_id in ?", orderIDs)
			}
		}
	}
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:失效
		if req.SettleStatus == 1 {
			req.SettleStatus = 0
		} else if req.SettleStatus == 2 {
			req.SettleStatus = 1
		}
		db.Where("status = ?", req.SettleStatus)
	}
	var awards []model.AreaAgencyAward
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("status = ?", 1).First(&amountTotal).Error
	err = db.Preload("Order").Preload("Order.OrderItems").Preload("Order.User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetMerchantAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.MerchantAward{})
	dbStatistic := source.DB().Model(&model.MerchantAward{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("uid = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("order_id = ?", 0)
		} else {
			var orderIDs []uint
			err = source.DB().Model(&model.Order{}).Where("user_id in ?", userIds).Pluck("id", &orderIDs).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
			if len(orderIDs) == 0 {
				db.Where("order_id = ?", 0)
			} else {
				db.Where("order_id in ?", orderIDs)
			}
		}
	}
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			req.SettleStatus = 0
		} else if req.SettleStatus == 2 {
			req.SettleStatus = 1
		}
		db.Where("status = ?", req.SettleStatus)
	}
	var awards []model.MerchantAward
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("status = ?", 1).First(&amountTotal).Error
	err = db.Preload("Order").Preload("Order.OrderItems").Preload("Order.User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetSmallShopAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.SmallShopAward{})
	dbStatistic := source.DB().Model(&model.SmallShopAward{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("suid = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		var orderIDs []uint
		err = source.DB().Model(&model.SmallShopOrder{}).Where("order_sn = ?", req.OrderSN).Pluck("id", &orderIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(orderIDs) == 0 {
			db.Where("order_id = ?", 0)
		} else {
			db.Where("order_id in ?", orderIDs)
		}
	}
	if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.SmallShopUser{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("uid = ?", 0)
		} else {
			db.Where("uid in ?", userIds)
		}
	}
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			req.SettleStatus = 0
		} else if req.SettleStatus == 2 {
			req.SettleStatus = 1
		} else if req.SettleStatus == 3 {
			req.SettleStatus = 2
		}
		db.Where("status = ?", req.SettleStatus)
	}
	var awards []model.SmallShopAward
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("status = ?", 1).First(&amountTotal).Error
	err = db.Preload("SmallShopOrder").Preload("SmallShopOrder.SmallShopOrderItems").Preload("ShopUserInfo").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetCinemaTicketAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.CinemaTicketOrder{})
	dbStatistic := source.DB().Model(&model.UserIncomeDetails{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("user_id = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	/*if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.SmallShopUser{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("uid = ?", 0)
		} else {
			db.Where("uid in ?", userIds)
		}
	}*/
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			db.Where("complete_at is null")
		} else if req.SettleStatus == 2 {
			db.Where("complete_at is not null")
		}

	}
	var awards []model.CinemaTicketOrder
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("income_type = ?", model2.CinemaTicket).First(&amountTotal).Error
	err = db.Preload("User").Preload("UserIncomeDetails").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetCpsAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.CpsOrderModel{})
	dbStatistic := source.DB().Model(&model.CpsOrderModel{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("user_id = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	/*if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.SmallShopUser{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("uid = ?", 0)
		} else {
			db.Where("uid in ?", userIds)
		}
	}*/
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			db.Where("status = ?", 0)
		} else if req.SettleStatus == 2 {
			db.Where("status = ?", 1)
		}

	}
	var awards []model.CpsOrderModel
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(award_amount), 0)").Where("status = ?", 1).First(&amountTotal).Error
	err = db.Preload("User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetJhCpsAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.JhCpsOrderModel{})
	dbStatistic := source.DB().Model(&model.UserIncomeDetails{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("user_id = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	/*if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.SmallShopUser{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("uid = ?", 0)
		} else {
			db.Where("uid in ?", userIds)
		}
	}*/
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			db.Where("status != ? and status != ?", "settled", "refunded")
		} else if req.SettleStatus == 2 {
			db.Where("status = ?", "settled")
		} else {
			db.Where("status = ?", "refunded")
		}
	}
	var awards []model.JhCpsOrderModel
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("income_type = ?", model2.JhCps).First(&amountTotal).Error
	err = db.Preload("User").Preload("UserIncomeDetails").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetMeituanDistributorAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.MeituanDistributorOrder{})
	dbStatistic := source.DB().Model(&model.MeituanDistributorOrder{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("user_id = ?", req.Uid)
	}
	if req.OrderSN != 0 {
		db.Where("uniqueItemId = ?", req.OrderSN)
	}
	/*if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.SmallShopUser{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("uid = ?", 0)
		} else {
			db.Where("uid in ?", userIds)
		}
	}*/
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			db.Where("settled = ?", 0)
		} else if req.SettleStatus == 2 {
			db.Where("settled = ?", 2)
		} else {
			// 没有已失效
			db.Where("settled = ?", 3)
		}
	}
	var awards []model.MeituanDistributorOrder
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(app_commission_price), 0)").Where("settled = ?", 1).First(&amountTotal).Error
	err = db.Preload("User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetCourseAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.LecturerDivided{})
	dbStatistic := source.DB().Model(&model.LecturerDivided{})

	// 查询讲师id
	var lecturer model.Lecturer
	err = source.DB().Model(&model.Lecturer{}).Where("uid = ?", req.Uid).First(&lecturer).Error
	if err != nil && lecturer.ID == 0 {
		return
	}

	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("lecturer_id = ?", lecturer.ID)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("order_sn = ?", 1)
		} else {
			var orderSns []uint
			err = source.DB().Model(&model.Order{}).Where("uid in ?", userIds).Pluck("order_sn", &orderSns).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
			if len(orderSns) == 0 {
				db.Where("order_sn = ?", 1)
			} else {
				db.Where("order_sn in ?", orderSns)
			}
		}
	}
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			req.SettleStatus = 0
		} else if req.SettleStatus == 2 {
			req.SettleStatus = 1
		} else {
			req.SettleStatus = 3
		}
		db.Where("status = ?", req.SettleStatus)
	}
	var awards []model.LecturerDivided
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("status = ?", 1).First(&amountTotal).Error
	err = db.Preload("Order").Preload("Order.OrderItems").Preload("Order.User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetInstitutionAwards(req request.InstitutionAwardRecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.InstitutionAward{})
	dbStatistic := source.DB().Model(&model.InstitutionAward{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("uid = ?", req.Uid)
	}
	// 小商店名称
	if req.SmallShopName != "" {
		var smallShop model.SmallShop
		err = source.DB().Model(&model.SmallShop{}).Where("title like ?", "%"+req.SmallShopName+"%").First(&smallShop).Error
		if err != nil && smallShop.ID == 0 {
			return
		}
		db.Where("sid = ?", smallShop.ID)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	if req.Type > 0 {
		db.Where("type = ?", req.Type)
	}
	if req.Status != -1 {
		db.Where("status = ?", req.Status)
	}
	var awards []model.InstitutionAward
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(amount), 0)").Where("status = ?", 1).First(&amountTotal).Error
	err = db.Preload("SmallShop").Limit(limit).Offset(offset).Order("id desc").Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetLocalLifeBrandAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	// 查询品牌
	var brand model.LocalLifeBrand
	err = source.DB().Model(&model.LocalLifeBrand{}).Where("uid = ?", req.Uid).First(&brand).Error
	if err != nil && brand.ID == 0 {
		return
	}
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.LocalLifeAward{})
	dbStatistic := source.DB().Model(&model.LocalLifeAward{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("brand_id = ?", brand.ID)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("uid = ?", 0)
		} else {
			db.Where("uid in ?", userIds)
		}
	}
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			req.SettleStatus = 0
		} else if req.SettleStatus == 2 {
			req.SettleStatus = 1
		}
		db.Where("settle_status = ?", req.SettleStatus)
	}
	var awards []model.LocalLifeAward
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(brand_settle_amount), 0)").Where("settle_status = ?", 1).First(&amountTotal).Error
	err = db.Preload("OrderInfo").Preload("OrderInfo.OrderItems").Preload("OrderInfo.User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}

func GetLocalLifeStoreAwards(req request.RecordSearch) (err error, list interface{}, total int64, amountTotal uint) {
	// 查询门店
	var store model.LocalLifeStore
	err = source.DB().Model(&model.LocalLifeStore{}).Where("user_id = ?", req.Uid).First(&store).Error
	if err != nil && store.ID == 0 {
		return
	}
	limit := req.PageSize
	offset := req.PageSize * (req.Page - 1)
	db := source.DB().Model(&model.LocalLifeAward{})
	dbStatistic := source.DB().Model(&model.LocalLifeAward{})
	var dbs []*gorm.DB
	dbs = append(dbs, db, dbStatistic)
	for _, g := range dbs {
		g.Where("store_id = ?", store.ID)
	}
	if req.OrderSN != 0 {
		db.Where("order_sn = ?", req.OrderSN)
	}
	if req.Member != "" {
		var userIds []uint
		err = source.DB().Model(&model.User{}).Where("username like ?", "%"+req.Member+"%").Or("mobile like ?", "%"+req.Member+"%").Or("id = ?", req.Member).Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		if len(userIds) == 0 {
			db.Where("uid = ?", 0)
		} else {
			db.Where("uid in ?", userIds)
		}
	}
	if req.SettleStatus != 0 {
		// 1：未结算，2：已结算，-1：已失效
		// 0:未结算 1：已结算 -1:已失效
		if req.SettleStatus == 1 {
			req.SettleStatus = 0
		} else if req.SettleStatus == 2 {
			req.SettleStatus = 1
		}
		db.Where("settle_status = ?", req.SettleStatus)
	}
	var awards []model.LocalLifeAward
	err = db.Count(&total).Error
	// 只查询已结算的数据，并且不会按照搜索条件变动
	err = dbStatistic.Select("COALESCE(SUM(store_settle_amount), 0)").Where("settle_status = ?", 1).First(&amountTotal).Error
	err = db.Preload("OrderInfo").Preload("OrderInfo.OrderItems").Preload("OrderInfo.User").Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total, amountTotal
}
