package model

import (
	productModel "product/model"
	"yz-go/response"
	"yz-go/source"
)

type SmallShopAward struct {
	source.Model
	SID            uint              `json:"sid" form:"sid" gorm:"column:sid;comment:商店id;"`
	SUID           uint              `json:"suid" form:"suid" gorm:"column:suid;comment:店主会员id;"`
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:店铺会员id;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:店铺订单id;"`
	ShopOrderID    uint              `json:"shop_order_id" gorm:"column:shop_order_id;comment:中台订单id;"`
	ShopPayTypeID  int               `json:"shop_pay_type_id" form:"shop_pay_type_id" gorm:"column:shop_pay_type_id;comment:中台订单支付方式;type:smallint;size:3;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;"`
	OrderCompleted int               `json:"order_completed" gorm:"column:order_completed;comment:订单是否完成 0:未完成 1：已完成;"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	SettleAt       *source.LocalTime `json:"settle_at" gorm:"column:settle_at;comment:结算时间;"`
	PaidAt         *source.LocalTime `json:"paid_at" gorm:"column:statement_at;comment:支付时间;"`
	IsSplit        int               `json:"is_split" gorm:"column:is_split;default:0;comment:是否分账0否1是;"`
	SplitStatus    int               `json:"split_status" gorm:"column:split_status;default:0;comment:分账状态 0待结算 1成功;"`
	// 下单会员
	ShopUserInfo SmallShopUser `json:"shop_user_info" gorm:"foreignKey:Uid"`
	// 小商店订单
	SmallShopOrder SmallShopOrder `json:"small_shop_order" gorm:"foreignKey:OrderID"`
}

func (SmallShopAward) TableName() string {
	return "small_shop_awards"
}

type OrderStatus int8

const (
	WaitPay OrderStatus = iota
	WaitSend
	WaitReceive
	Completed
	Closed OrderStatus = -1
)

type RefundStatus int8

const (
	ClosedRefund RefundStatus = -1
	NotRefund    RefundStatus = iota
	Refunding
	RefundComplete
)

type SendStatus int8

const (
	NotSend SendStatus = iota
	Sending
	Sent
)

type SmallShop struct {
	source.Model
	Title string `json:"title" form:"title" gorm:"column:title;comment:小商店名称;type:varchar(500);size:500;"`
	Uid   uint   `json:"uid" form:"uid" gorm:"column:uid;comment:会员id;"`
}

type SmallShopOrder struct {
	source.Model
	OrderSN              uint         `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;index;"`                                                                            // 编号
	Key                  string       `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(100);size:100;"`                                                                      // 标识
	Title                string       `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);size:100;"`                                                                // 标题
	Status               OrderStatus  `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;index;"`                                                   // 订单状态
	Amount               uint         `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"`                                                                              // 订单总金额
	RefundAmount         uint         `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额(分);"`                                                           // 订单总金额
	ItemAmount           uint         `json:"item_amount" form:"item_amount" gorm:"column:item_amount;comment:商品市场价(分);"`                                                               // 商品市场价
	SupplyAmount         uint         `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(分);"`                                                           // 供货金额
	CostAmount           uint         `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(分);"`                                                                 // 成本金额
	Freight              uint         `json:"freight" form:"freight" gorm:"column:freight;comment:运费(分);"`                                                                                 // 运费(单位:分)
	ServiceFee           uint         `json:"service_fee" form:"service_fee" gorm:"column:service_fee;comment:服务费(分);"`                                                                   // 服务费
	GoodsCount           uint         `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:商品总数;"`                                                           // 商品总数
	SendStatus           SendStatus   `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态;type:smallint;size:3;"`                                      // 发货状态
	RefundStatus         RefundStatus `json:"refund_status" form:"refund_status" gorm:"column:refund_status;default:1;comment:退货退款状态 -1关闭 1正常 2售后中 3完成;type:smallint;size:3;"` // 退货退款状态状态
	Remark               string       `json:"remark" form:"remark" gorm:"column:desc;comment:备注（买家留言）;type:varchar(200);size:200;"`                                                     // 备注
	Note                 string       `json:"note" form:"note" gorm:"column:note;comment:商家备注;type:varchar(200);size:200;"`                                                               // 备注
	TechnicalServicesFee uint         `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"`                              // 技术服务费

	PaidAt     *source.LocalTime `json:"paid_at" gorm:"index;"`
	SentAt     *source.LocalTime `json:"sent_at" gorm:"index;"`
	ReceivedAt *source.LocalTime `json:"received_at" gorm:"index;"`
	ClosedAt   *source.LocalTime `json:"closed_at" gorm:"index;"`

	SupplyOrderID           uint                  `json:"supply_order_id" form:"supply_order_id" gorm:"column:supply_order_id;comment:供应链订单id;index;"`                   // 供应链中台订单id
	SmallShopUserID         uint                  `json:"small_shop_user_id" form:"small_shop_user_id" gorm:"column:small_shop_user_id;comment:用户id;index;"`                // 用户id
	TradeID                 uint                  `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`                                                    // 交易id
	PayTypeID               int                   `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"`                // 订单支付方式
	SmallShopPayInfoID      uint                  `json:"small_shop_pay_info_id" form:"small_shop_pay_info_id" gorm:"column:small_shop_pay_info_id;comment:支付记录id;"`      // 支付记录id
	ShippingMethodID        int                   `json:"shipping_method_id" form:"shipping_method_id" gorm:"column:shipping_method_id;comment:配送方式id;"`                  // 配送方式id
	ShippingAddressID       uint                  `json:"shipping_address_id" form:"shipping_address_id" gorm:"column:shipping_address_id;comment:收货地址id;"`               // 收货地址id
	SendTypeID              int                   `json:"send_type_id" form:"send_type_id" gorm:"column:send_type_id;comment:订单发货方式;type:smallint;size:3;"`             // 订单发货方式
	CommentStatus           int                   `json:"comment_status" form:"comment_status" gorm:"column:comment_status;comment:是否评价;default:0;type:smallint;size:3;"` // 是否评价
	CanRefund               int                   `json:"can_refund" form:"can_refund" gorm:"column:can_refund;comment:可以退货（1是0否）;default:0;type:smallint;size:3;"`     // 可以退款（1是，0否）
	Lock                    int                   `json:"lock" form:"lock" gorm:"column:lock;default:0;type:int;"`
	AmountDetail            response.AmountDetail `json:"amount_detail"`
	SmallShopOrderItems     SmallShopOrderItems   `json:"small_shop_order_items"`
	IsUpdateShippingAddress int                   `json:"is_update_shipping_address" form:"is_update_shipping_address" gorm:"column:is_update_shipping_address;comment:可以修改地址（1是，0否）;default:0;type:smallint;size:3;"` // 可以修改地址（1是，0否）
	SmallShopID             uint                  `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;default:0;comment:小商店id;"`
	Shopkeeper              int                   `json:"shopkeeper" form:"shopkeeper" gorm:"column:shopkeeper;default:0;type:int;comment:1:店主自己的订单不产生店主提成;"`
}

type SmallShopOrderItems []SmallShopOrderItem
type SmallShopOrderItem struct {
	source.Model
	Key             string `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(100);size:100;"`                      // 标识
	Title           string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);size:100;"`                // 名
	SkuTitle        string `json:"sku_title" form:"sku_title" gorm:"column:sku_title;comment:sku标题;type:varchar(100);size:100;"` // 规格名
	Unit            string `json:"unit" form:"unit" gorm:"column:unit;comment:单位;type:varchar(20);size:20;"`                     // 单位
	Qty             uint   `json:"qty" form:"qty" gorm:"column:qty;comment:商品数量;"`                                             // 商品数量
	Amount          uint   `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`                                        // 总价(分)
	RefundAmount    uint   `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额;"`               // 退款金额 (分)                           // 总价(元)
	DiscountAmount  uint   `json:"discount_amount" form:"discount_amount" gorm:"column:discount_amount;comment:优惠金额(元);"`     // 优惠金额(分)
	DeductionAmount uint   `json:"deduction_amount" form:"deduction_amount" gorm:"column:deduction_amount;comment:抵扣金额(元);"`  // 抵扣金额(分)

	CostAmount    uint   `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(元);"`                                                      // 成本(分)
	PaymentAmount uint   `json:"payment_amount" form:"payment_amount" gorm:"column:payment_amount;comment:均摊支付金额(元);"`                                         // 均摊支付金额(分)
	SupplyAmount  uint   `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(元);"`                                                // 成本(分)
	ImageUrl      string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`                                     // 图片地址
	SendStatus    int    `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态 0未发货 1已发货 2部分发货;type:smallint;size:3;"` // 发货状态

	RefundStatus RefundStatus `json:"refund_status" form:"refund_status" gorm:"column:refund_status;default:0;comment:退货退款状态状态 1正常 2售后中 3完成;type:smallint;size:3;"` // 退货退款状态状态

	TradeID                uint                  `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`                                                             // 交易id
	SmallShopUserID        uint                  `json:"small_shop_user_id" form:"small_shop_user_id" gorm:"column:small_shop_user_id;comment:用户id;"`                               // 用户id
	SmallShopProductSaleID uint                  `json:"small_shop_product_sale_id" form:"small_shop_product_sale_id" gorm:"column:small_shop_product_sale_id;comment:小商店商品id;"` // 小商店商品id
	SmallShopOrderID       uint                  `json:"small_shop_order_id" form:"small_shop_order_id" gorm:"column:small_shop_order_id;comment:订单id;"`                            // 小商店订单id
	SmallShopID            uint                  `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店id;"`                                            // 小商店id
	ProductID              uint                  `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;"`                                                       // 产品id
	SkuID                  uint                  `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku id;"`                                                                   // sku id
	OriginalSkuID          uint                  `json:"original_sku_id" form:"original_sku_id" gorm:"column:original_sku_id;comment:第三方sku id;"`                                  // 第三方sku id
	CanRefund              uint                  `json:"can_refund" form:"can_refund" gorm:"column:can_refund;default:0;comment:可以退款（1是0否）;type:smallint;size:3;"`              // 可以退款（1是0否）
	CommentStatus          int                   `json:"comment_status" form:"comment_status" gorm:"column:comment_status;comment:是否评价;default:0;type:smallint;size:3;"`          // 是否评价
	AmountDetail           response.AmountDetail `json:"amount_detail"`
	Sku                    productModel.Sku      `json:"sku"`
	Product                productModel.Product  `json:"product"`
}

type SmallShopUser struct {
	source.Model
	Mobile   string `json:"mobile" form:"mobile" gorm:"column:mobile;comment:手机号;type:char(20);size:20;"`
	Avatar   string `json:"avatar" form:"avatar" gorm:"column:avatar;comment:用户头像url;type:varchar(255);size:255;"`
	Username string `json:"username" form:"username" gorm:"comment:用户登录名"`
	NickName string `json:"nick_name" form:"nick_name" gorm:"column:nick_name;comment:用户昵称;type:varchar(50);size:50;"`
}
