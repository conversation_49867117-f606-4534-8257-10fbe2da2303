package request

import yzRequest "yz-go/request"

type RecordSearch struct {
	// 会员id，不用传。下面的正常传
	Uid uint `json:"uid" form:"uid" query:"uid"`
	// 订单号
	OrderSN uint `json:"order_sn" form:"order_sn" query:"order_sn"`
	// 下单会员手机号、id。电影票没有搜索会员，都是自己
	Member string `json:"member" form:"member" query:"member"`
	// 结算状态 1：未结算，2：已结算，3子账户 -1：已失效
	SettleStatus int `json:"settle_status" form:"settle_status" query:"settle_status"`
	yzRequest.PageInfo
}

type InstitutionAwardRecordSearch struct {
	// 会员id，不用传。下面的正常传
	Uid uint `json:"uid" form:"uid" query:"uid"`
	// 小商店名称
	SmallShopName string `json:"small_shop_name" form:"small_shop_name" query:"small_shop_name"`
	// 订单号
	OrderSN uint `json:"order_sn" form:"order_sn" query:"order_sn"`
	// 业务类型 1付费开通小商店2小商店销售产品-1全部
	Type int `json:"type" form:"type" query:"type"`
	// 结算状态 0未结算1已结算2已失效-1全部
	Status int `json:"status" form:"status" query:"status"`
	yzRequest.PageInfo
}
