package order

import (
	"context"
	"errors"
	"fmt"
	"github.com/writethesky/stbz-sdk-golang"
	"go.uber.org/zap"
	maiGerPkgOrder "maiger-supply/maiger-pkg/order"
	maigerPkgToken "maiger-supply/maiger-pkg/token"
	"maiger-supply/service"
	orderModel "order/model"
	productModel "product/model"
	publicSupplyCallback "public-supply/callback"
	publicSupplyCommon "public-supply/common"
	publicSupplyRequest "public-supply/request"
	publicSupplyResponse "public-supply/response"
	"time"
	"yz-go/component/log"
	"yz-go/source"
	"yz-go/utils"
)

type MaiGerSupply struct {
	SysSetting  service.SysSetting
	GatherId    uint
	Host        string
	AccessToken string
}

func (m *MaiGerSupply) UploadGatherSupplySN(request publicSupplyRequest.UpdateData) (err error, data interface{}) {
	//TODO implement me
	panic("implement me")
}

func (m *MaiGerSupply) InitSetting(gatherId uint) (err error) {
	// 查询基础设置
	if m.SysSetting, err = service.GetSetting(gatherId); err != nil {
		return
	}

	if m.SysSetting.BaseInfo.Host == "" || m.SysSetting.BaseInfo.ClientID == "" || m.SysSetting.BaseInfo.ClientSecret == "" {
		err = errors.New("请先填写供应链配置")
		return
	}

	// 当前采集源ID
	m.GatherId = gatherId
	m.Host = m.SysSetting.BaseInfo.Host

	// 生成 access_token
	accessTokenKey := fmt.Sprintf("MAIGER_SUPPLY_ACCESS_TOKEN_%d", gatherId)

	var ctx = context.Background()
	if m.AccessToken, _ = source.Redis().Get(ctx, accessTokenKey).Result(); m.AccessToken == "" {
		var result maigerPkgToken.TokenResult
		if result, err = maigerPkgToken.GetAccessToken(m.SysSetting.BaseInfo.Host, m.SysSetting.BaseInfo.ClientID, m.SysSetting.BaseInfo.ClientSecret); err != nil {
			return
		}

		if err = source.Redis().SetEX(ctx, accessTokenKey, result.AccessToken, 5*24*time.Hour).Err(); err != nil {
			return
		}

		m.AccessToken = result.AccessToken
	}

	return
}

func (m *MaiGerSupply) OrderBeforeCheck(request publicSupplyRequest.RequestSaleBeforeCheck) (err error, result publicSupplyResponse.BeforeCheck) {
	var skuIds []string                   // 校验使用
	var skuInfos []maiGerPkgOrder.SkuInfo // 查询运费使用
	for _, item := range request.LocalSkus {
		// 查询商品信息
		var sku productModel.Sku
		if err = source.DB().Preload("Product").First(&sku, item.Sku.Sku).Error; err != nil {
			return
		}

		// 最小起订量验证
		if item.Number < sku.MinNumber {
			err = fmt.Errorf("商品[%s]最小起订量为%d件", sku.Title, sku.MinNumber)
			return
		}

		// 组装参数
		skuIds = append(skuIds, sku.SourceStrId)
		skuInfos = append(skuInfos, maiGerPkgOrder.SkuInfo{
			SkuId:    sku.SourceStrId,
			Quantity: item.Number,
		})
	}

	// 地址转换
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description

	checkAreaLimitParams := maiGerPkgOrder.CheckAreaLimitParams{
		AccessToken: m.AccessToken,
		Domain:      m.Host,
		Address:     address,
		SkuIds:      skuIds,
	}
	log.Log().Info("迈戈供应链下单前置校验参数：", zap.Any("Request", checkAreaLimitParams))

	var checkAreaLimitResult []maiGerPkgOrder.CheckAreaLimitResult
	if checkAreaLimitResult, err = maiGerPkgOrder.CheckAreaLimit(checkAreaLimitParams); err != nil {
		return
	}

	log.Log().Info("迈戈供应链下单前置校验结果：", zap.Any("result", checkAreaLimitResult))

	for _, item := range checkAreaLimitResult {
		if item.IsAreaRestrict != false {
			err = fmt.Errorf("校验商品失败[%d]", item.SkuId)
			log.Log().Error("迈戈供应链下单前置校验失败：", zap.Any("item", item))
			return
		}
	}

	// 查询运费
	freightParams := maiGerPkgOrder.FreightParams{
		AccessToken: m.AccessToken,
		Domain:      m.Host,
		Address:     address,
		SkuInfos:    skuInfos,
	}

	var freight float64
	if freight, err = maiGerPkgOrder.Freight(freightParams); err != nil {
		return
	}

	// 返回默认值
	result.Code = 1
	result.Freight = uint(freight * 100)

	return
}

func (m *MaiGerSupply) SaleBeforeCheck(request publicSupplyRequest.RequestSaleBeforeCheck) (err error, resData publicSupplyResponse.ResSaleBeforeCheck) {
	var skuIds []string // 校验使用
	skuIdsMap := make(map[string]uint, len(request.LocalSkus))
	for _, item := range request.LocalSkus {
		// 查询商品信息
		var sku productModel.Sku
		if err = source.DB().Preload("Product").First(&sku, item.Sku.Sku).Error; err != nil {
			return
		}

		// 最小起订量验证
		if item.Number < sku.MinNumber {
			err = fmt.Errorf("商品[%s]最小起订量为%d件", sku.Title, sku.MinNumber)
			return
		}

		// 组装参数
		skuIds = append(skuIds, sku.SourceStrId)
		skuIdsMap[sku.SourceStrId] = sku.ID
	}

	// 地址转换
	address := request.Address.Province + request.Address.City + request.Address.Area + request.Address.Street + request.Address.Description

	checkAreaLimitParams := maiGerPkgOrder.CheckAreaLimitParams{
		AccessToken: m.AccessToken,
		Domain:      m.Host,
		Address:     address,
		SkuIds:      skuIds,
	}
	log.Log().Error("迈戈供应链下单前置校验参数：", zap.Any("Request", checkAreaLimitParams))

	var checkAreaLimitResult []maiGerPkgOrder.CheckAreaLimitResult
	if checkAreaLimitResult, err = maiGerPkgOrder.CheckAreaLimit(checkAreaLimitParams); err != nil {
		log.Log().Error("迈戈供应链下单前置请求失败：", zap.Any("err", err.Error()))
		return
	}

	log.Log().Error("迈戈供应链下单前置校验结果：", zap.Any("result", checkAreaLimitResult))

	for _, item := range checkAreaLimitResult {
		//if item.IsAreaRestrict != false {
		//	err = fmt.Errorf("校验商品失败[%d]", item.SkuId)
		//	log.Log().Error("迈戈供应链下单前置校验失败：", zap.Any("item", item))
		//	return
		//}

		// 如果未找到直接跳过：默认不可售
		skuId, ok := skuIdsMap[item.SkuId]
		if !ok {
			continue
		}

		if item.IsAreaRestrict != false {
			log.Log().Error("迈戈供应链下单前置校验失败：", zap.Any("item", item))

			resData.Data.Ban = append(resData.Data.Ban, skuId)
		} else {
			resData.Data.Available = append(resData.Data.Available, skuId)
		}
	}
	resData.IsOriginalSkuID = 1

	return
}

func (m *MaiGerSupply) ConfirmOrder(request publicSupplyRequest.RequestConfirmOrder) (err error, info *stbz.APIResult) {

	// 查询订单
	var order orderModel.Order
	if err = source.DB().Where("order_sn = ?", request.OrderSn.OrderSn).First(&order).Error; err != nil {
		return
	}

	skuIdMap := make(map[string]uint, len(request.LocalSkus))

	var skuItems []maiGerPkgOrder.SkuItem
	for _, item := range request.LocalSkus {
		// 查询商品信息
		var sku productModel.Sku
		if err = source.DB().Preload("Product").First(&sku, item.Sku.Sku).Error; err != nil {
			return
		}

		skuIdMap[sku.SourceStrId] = sku.ID

		skuItems = append(skuItems, maiGerPkgOrder.SkuItem{
			SkuId: sku.SourceStrId,
			Num:   item.Number,
			Price: utils.Decimal(float64(sku.CostPrice) / 100),
		})
	}

	// 提交订单
	params := maiGerPkgOrder.SubmitParams{
		AccessToken: m.AccessToken,
		Domain:      m.Host,
		ThirdOrder:  request.OrderSn.OrderSn,
		Name:        request.Address.Consignee,
		Mobile:      request.Address.Phone,
		Province:    request.Address.Province,
		City:        request.Address.City,
		County:      request.Address.Area,
		Town:        request.Address.Street,
		Address:     request.Address.Description,
		SkuList:     skuItems,
	}
	var submitResult []maiGerPkgOrder.SubmitResult
	if submitResult, err = maiGerPkgOrder.Submit(params); err != nil {
		log.Log().Error("迈戈供应链下单错误", zap.Any("err", err))
		if err = source.DB().Model(&order).Updates(map[string]interface{}{"gather_supply_type": publicSupplyCommon.SUPPLY_MAIGER, "gather_supply_msg": err.Error()}).Error; err != nil {
			return
		}
		return
	}

	// 增加日志，用于拆单优化
	if len(submitResult) > 1 {
		log.Log().Info("迈戈供应链拆单数据", zap.Any("submitResult", submitResult))
	}

	var thirdOrderSn string
	for _, item := range submitResult {
		thirdOrderSn = thirdOrderSn + item.OrderSn + "-"

		for _, itemSku := range item.SkuList {
			skuId, _ := skuIdMap[itemSku.SkuID]

			// 更新 orderItem 表第三方单号
			OrderItem := orderModel.OrderItem{
				OrderItemModel: orderModel.OrderItemModel{
					GatherSupplySN: item.OrderSn,
				},
			}
			source.DB().Where("sku_id = ? and order_id = ?", skuId, order.ID).Updates(&OrderItem)
		}
	}

	thirdOrderSn = thirdOrderSn[:len(thirdOrderSn)-1]
	// 修改第三方订单号
	if err = source.DB().Model(&order).Updates(map[string]interface{}{"gather_supply_type": publicSupplyCommon.SUPPLY_MAIGER, "gather_supply_sn": thirdOrderSn}).Error; err != nil {
		log.Log().Error("迈戈供应链修改第三方订单号错误", zap.Any("err", err))
		if err = source.DB().Model(&order).Updates(map[string]interface{}{"gather_supply_type": publicSupplyCommon.SUPPLY_MAIGER, "gather_supply_msg": err.Error()}).Error; err != nil {
			return
		}
		return
	}

	// 订单支付
	payParams := maiGerPkgOrder.PayParams{
		AccessToken: m.AccessToken,
		Domain:      m.Host,
		ThirdOrder:  request.OrderSn.OrderSn,
	}
	var payResult bool
	if payResult, err = maiGerPkgOrder.Pay(payParams); err != nil {
		log.Log().Error("迈戈供应链支付错误", zap.Any("err", err))
		if err = source.DB().Model(&order).Updates(map[string]interface{}{"gather_supply_msg": err.Error()}).Error; err != nil {
			return
		}
		return
	}

	if payResult == false {
		log.Log().Error("迈戈供应链支付错误", zap.Any("err", err))
		if err = source.DB().Model(&order).Updates(map[string]interface{}{"gather_supply_msg": "支付失败"}).Error; err != nil {
			return
		}
		return
	}

	return
}

func (m *MaiGerSupply) ExpressQuery(request publicSupplyRequest.RequestExpress) (err error, info interface{}) {
	return
}

func (m *MaiGerSupply) AfterSalesBeforeCheck(request publicSupplyRequest.RequestAfterSale) (err error, info interface{}) {
	return
}

func (m *MaiGerSupply) AfterSalesPicture(request publicSupplyRequest.RequestAfterSalePicture) (err error, info interface{}) {
	return
}

func (m *MaiGerSupply) AfterSale(request publicSupplyRequest.AfterSale) (err error, info interface{}) {
	return
}

func (m *MaiGerSupply) OrderDelivery(OrderData publicSupplyCallback.OrderCallBack) (err error) {
	return
}

func (m *MaiGerSupply) GetAllAddress() (err error, data interface{}) {
	return
}
