package common

import (
	"bytes"
	"encoding/base64"
	"encoding/json"
	"errors"
	"go.uber.org/zap"
	"io/ioutil"
	"net/http"
	"service-provider-system/request"
	"service-provider-system/setting"
	"strings"
	"time"
	"yz-go/component/log"
)

type APIResult struct {
	Status    bool        `json:"status"`
	Msg       string      `json:"msg"`
	Data      interface{} `json:"data"`
	ErrorCode int         `json:"error_code"`
	RequestId string      `json:"requestId"`
	Total     int         `json:"total"`
	Pages     int         `json:"pages"`
	PageNum   int         `json:"page_num"`
	NextPage  bool        `json:"next_page"`
	Limit     int         `json:"limit"`
	PageSize  int         `json:"page_size"`
}

type RequestUrl string

type RequestData struct {
	Query     map[string]string  `json:"query"`  //请求query参数
	Body      []byte             `json:"body"`   //请求body参数
	Url       string             `json:"Url"`    //请求链接
	Method    string             `json:"method"` //请求方式
	Config    setting.SysSetting `json:"config"`
	Host      string             `json:"host"`      //请求链接
	Token     string             `json:"token"`     //token
	TokenData Token              `json:"tokenData"` //token
}

const (
	TokenApi            RequestUrl = "server/api/open/plugins/openapi/public/token"        //生成token
	PushStore           RequestUrl = "server/api/open/plugins/openapi/store/multi_create"  //推送门店
	PushStoreStatus     RequestUrl = "server/api/open/plugins/openapi/store/create_result" //获取推送状态
	PushOrder           RequestUrl = "server/api/open/plugins/openapi/order/multi_create"  //推送订单
	PushOrderStatus     RequestUrl = "server/api/open/plugins/openapi/order/create_result" //获取推送订单状态
	LevelApi            RequestUrl = "server/api/open/member/member_level_list"
	IsThirdApi          RequestUrl = "server/api/open/member/is_third"
	UserCreate          RequestUrl = "server/api/open/member/save"
	UserLevelUpdate     RequestUrl = "server/api/open/member/update_member_level"
	UserInfo            RequestUrl = "server/api/open/member/detail"
	SyncMemberLevelList RequestUrl = "server/api/open/member/sync_member_level_list"
	PaymentPreOrderUrl  RequestUrl = "server/api/open/plugins/payment/pre_order"     //聚合主扫
	PaymentMerchantSync RequestUrl = "server/api/open/plugins/payment/merchant/sync" //主商户同步
	PaymentBase         RequestUrl = "server/api/open/plugins/payment/base"          //聚合支付主商家基础信息
	PaymentRefundUrl    RequestUrl = "server/api/open/plugins/payment/refund"        //退款

	MerchantDetail RequestUrl = "/server/api/open/plugins/payment/merchant/detail" //主商家详情

	SubMerList   RequestUrl = "/server/api/open/plugins/payment/sub_mer/sync"   //子商户列表
	SubMerDetail RequestUrl = "/server/api/open/plugins/payment/sub_mer/detail" //子商户详情

	HuifuSubMerAddApi  RequestUrl = "/server/api/open/plugins/payment/huifu_sub_mer/save" //新增汇付子商户
	LaKaLaSubMerAddApi RequestUrl = "/server/api/open/plugins/payment/sub_mer/save"       //拉卡拉新增子商户

	SplitApplyApi      RequestUrl = "/server/api/open/plugins/payment/split/apply"  //分账申请
	SplitModifyApi     RequestUrl = "/server/api/open/plugins/payment/split/modify" //分账修改
	SplitApplyQueryApi RequestUrl = "/server/api/open/plugins/payment/split/query"  //分账审核状态查询用来查询是否审核通过

	SplitBind         RequestUrl = "/server/api/open/plugins/payment/bind"       //绑定或者解绑分账
	SplitBindQueryApi RequestUrl = "/server/api/open/plugins/payment/bind/query" //绑定关系查询

	SplitAmtQuery          RequestUrl = "/server/api/open/plugins/payment/separate/amt_query" //可分账金额查询
	SplitAmtAPI            RequestUrl = "/server/api/open/plugins/payment/separate"           //分账
	SplitAmtStatusQueryAPI RequestUrl = "/server/api/open/plugins/payment/separate/query"     //获取分账状态

)

func Initial() (err error, res RequestData) {
	err, res.Config = setting.GetSysServiceProviderSystemSetting()
	if res.Config.Value.AppSecret == "" || res.Config.Value.AppKey == "" || res.Config.Value.ServiceSystemUrl == "" {
		err = errors.New("基础设置未配置")
		return
	}
	if err != nil {
		return
	}
	if strings.HasSuffix(res.Config.Value.ServiceSystemUrl, "/") == false {
		res.Config.Value.ServiceSystemUrl += "/"
	}
	res.Host = res.Config.Value.ServiceSystemUrl
	err, res.TokenData = res.GetToken() //获取 token
	if err != nil {
		return
	}
	res.Token = res.TokenData.Token
	return
}

type Token struct {
	AppID          int    `json:"app_id"`
	AppKey         string `json:"app_key"`
	AppOrderTypeID int    `json:"app_order_type_id"`
	ExpiresAt      string `json:"expires_at"`
	Token          string `json:"token"`
}

var TokenData *Token

/*
*
获取token
*/
func (rd RequestData) GetToken() (err error, token Token) {
	//如果不存在 或者 时间不在当前时间之后则重新获取

	var t time.Time
	if TokenData != nil {
		layout := "2006-01-02 15:04:05"
		var c, _ = time.LoadLocation("Asia/Shanghai")               //设置时区否则转换的时间因为时区原因会比time.now 获取的时间大
		t, _ = time.ParseInLocation(layout, TokenData.ExpiresAt, c) //转换时间比较大小
	}
	//没有 token 或者 过期时间小于当前时间则重新获取
	if TokenData == nil || TokenData.Token == "" || t.After(time.Now()) == false {
		var post = make(map[string]interface{})
		post["app_key"] = rd.Config.Value.AppKey
		post["app_secret"] = rd.Config.Value.AppSecret
		rd.Body, err = json.Marshal(post)
		if err != nil {
			err = errors.New("参数转json失败:" + err.Error())
			return
		}
		var result *APIResult
		rd.Url = string(TokenApi)
		result, err = rd.ClientPost()

		//res := "{\"status\":true,\"msg\":\"成功\",\"data\":{\"app_id\":1,\"app_key\":\"169156330385292933\",\"app_order_type_id\":1,\"token\":\"e460bb8adc1aeba1d1f4080f643ea3eaeef0abe20f36438c5c5986ade8ac5f75\",\"expires_at\":\"2023-08-22 16:36:40\"},\"error_code\":100000}"

		//err = json.Unmarshal([]byte(res),&result)

		if err != nil {
			err = errors.New("Token请求失败" + err.Error())
			return
		}
		if result.Status == false {
			err = errors.New("Token请求失败" + result.Msg)
			return
		}

		tokenDatajson, _ := json.Marshal(result.Data)
		var tokenData Token
		_ = json.Unmarshal(tokenDatajson, &tokenData)
		TokenData = &tokenData
	}
	token = *TokenData
	return
}

type CreateStoreData struct {
	AppID       string `json:"appId"` //appkey
	StoreID     string `json:"store_id"`
	Name        string `json:"name"`
	ManagerName string `json:"manager_name"`
	ManagerTel  string `json:"manager_tel"`
	ProvinceID  int    `json:"province_id"`
	CityID      int    `json:"city_id"`
	DistrictID  int    `json:"district_id"`
	StreetID    int    `json:"street_id"`
	Address     string `json:"address"`
}

/*
*

	创建门店 -- 最多一次100条
*/
func (rd RequestData) CreateStore(data []CreateStoreData) (result *APIResult, err error) {
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	rd.Url = string(PushStore)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("创建门店请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("创建门店请求失败" + result.Msg)
		return
	}
	return
}

/*
*

	推送订单 -- 最多一次100条
*/
func (rd RequestData) PushOrder(data []PushOrderRequest) (result *APIResult, err error) {
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	rd.Url = string(PushOrder)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("推送订单请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("推送订单请求失败" + result.Msg)
		return
	}
	return
}

/*
*

	获取订单推送状态
*/
func (rd RequestData) GetPushOrderStatus(data []string) (resData []GetStoreStatusData, err error) {
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(PushOrderStatus)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("推送订单请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("推送订单请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

// 门店 /订单 回调以及获取推送状态返回数据
type GetStoreStatusData struct {
	ID          string `json:"id"`
	OrderCode   string `json:"order_code"`
	StoreId     string `json:"store_id"`
	Status      int    `json:"status"` //1代表推送成功
	YqStoreID   int    `json:"yq_store_id"`
	YqShortName string `json:"yq_short_name"`
	YqStoreCode string `json:"yq_store_code"`
	AgentID     int    `json:"agent_id"`
	AgentName   string `json:"agent_name"`
	AgentCode   string `json:"agent_code"`
}

type MemberNotify struct {
	Tel           string `json:"tel"`
	LevelId       int    `json:"level_id"`
	IntroducerTel string `json:"introducer_tel"`
}

/*
*

	创建门店 -- 最多一次100条
*/
func (rd RequestData) GetStoreStatus(data []string) (resData []GetStoreStatusData, err error) {

	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(PushStoreStatus)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取门店推送状态请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取门店推送状态请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type MemberLevel struct {
	Id        int    `json:"id"`
	LevelName string `json:"level_name"`
}

func (rd RequestData) GetMemberLevels() (resData []MemberLevel, err error) {
	var result *APIResult
	rd.Url = string(LevelApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取会员等级列表请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取会员等级列表请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

func (rd RequestData) PushMemberLevelRelations() (err error) {
	var result *APIResult
	rd.Url = string(SyncMemberLevelList)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("同步会员等级列表请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("同步会员等级列表请求失败" + result.Msg)
		return
	}

	return
}

type IsThirdResponse struct {
	IsThird int `json:"is_third"`
}

type UserCreateResponse struct {
	IsExist            int    `json:"is_exist"`
	IntroducerTel      string `json:"introducer_tel"`
	IntroducerAvatar   string `json:"introducer_avatar"`
	IntroducerNickName string `json:"introducer_nick_name"`
	RealName           string `json:"real_name"`
	LevelId            int    `json:"level_id"`
}
type GetUserInfoResponse struct {
	Id            int         `json:"id"`
	Type          int         `json:"type"`
	IntroducerTel string      `json:"introducer_tel"`
	Nickname      string      `json:"nickname"`
	RealName      string      `json:"real_name"`
	AppKey        string      `json:"app_key"`
	Sex           int         `json:"sex"`
	Birthday      interface{} `json:"birthday"`
	Email         interface{} `json:"email"`
	Tel           string      `json:"tel"`
	IdNo          string      `json:"id_no"`
	TypeDesc      string      `json:"type_desc"`
	RegisterTime  string      `json:"register_time"`
	CreatedAt     string      `json:"created_at"`
	UpdatedAt     interface{} `json:"updated_at"`
	LevelId       int         `json:"level_id"`
	LevelName     string      `json:"level_name"`
}

func (rd RequestData) UserCreate() (resData UserCreateResponse, err error) {
	var result *APIResult
	rd.Url = string(UserCreate)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("创建会员请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("创建请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

func (rd RequestData) UserLevelUpdate() (resData UserCreateResponse, err error) {
	var result *APIResult
	rd.Url = string(UserLevelUpdate)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("修改会员等级请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("修改会员等级请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

func (rd RequestData) GetUserInfo() (resData GetUserInfoResponse, err error) {
	var result *APIResult
	rd.Url = string(UserInfo)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取会员信息请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取会员信息请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}
func (rd RequestData) GetIsThird() (resData IsThirdResponse, err error) {
	var result *APIResult
	rd.Url = string(IsThirdApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取是否第三方请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取是否第三方请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type PushOrderRequest struct {
	OrderTypeID      int     `json:"orderTypeId"`        //不用传
	AppID            int     `json:"appId"`              //不用传
	ForeignOrderCode string  `json:"foreign_order_code"` //订单号
	Status           int     `json:"status"`             //订单状态:-1已关闭,0-待付款,1-待发货,2-待收货,9-已完成,99-退货退款。
	ProductID        string  `json:"product_id"`         //商品id
	StoreID          string  `json:"store_id"`           //门店id
	Price            float64 `json:"price"`              //实付金额
	SettlementPrice  float64 `json:"settlement_price"`   //结算金额
	FinishAt         string  `json:"finish_at"`          //订单完成时间
	OrderItemId      uint    `json:"order_item_id"`      //自用 用于获取状态之后改变推送状态
	OrderId          uint    `json:"order_id"`           //同上
}
type MerchantSyncData struct {
	AppID            string `json:"appid" form:"appid" gorm:"column:appid;comment:应用标识"`                                            // 应用标识
	StoreName        string `json:"store_name" form:"store_name" gorm:"column:store_name;comment:商户名称"`                             // 商户名称
	StoreCode        string `json:"store_code" form:"store_code" gorm:"column:store_code;comment:商户编号"`                             // 商户编号
	PaymentStoreCode string `json:"payment_store_code" form:"payment_store_code" gorm:"column:payment_store_code;comment:支付商户编号"` // 支付商户编号
	ChannelType      int    `json:"channel_type" form:"channel_type" gorm:"column:channel_type;comment:渠道类型"`                       // 渠道类型
	ChannelTypeDesc  string `json:"channel_type_desc" form:"channel_type_desc" gorm:"column:channel_type_desc;comment:渠道类型描述"`    // 渠道类型描述
	TermNo           string `json:"term_no" form:"term_no" gorm:"column:term_no;comment:终端号"`                                        // 终端号
	OrgCode          string `json:"org_code" form:"org_code" gorm:"column:org_code;comment:机构代码"`                                   // 机构代码
	WalletID         string `json:"wallet_id" form:"wallet_id" gorm:"column:wallet_id;comment:钱包ID"`                                  // 钱包ID
	SettleType       string `json:"settle_type" form:"settle_type" gorm:"column:settle_type;comment:结算类型"`                          // 结算类型
	SubMerCount      int    `json:"sub_mer_count" form:"sub_mer_count" gorm:"column:sub_mer_count;comment:子商户数量"`                  // 子商户数量
	ApplyCount       int    `json:"apply_count" form:"apply_count" gorm:"column:apply_count;comment:申请次数"`                          // 申请次数
	ReceiverName     string `json:"receiver_name" form:"receiver_name" gorm:"column:receiver_name;comment:收款人姓名"`                  // 收款人姓名
	ReceiverNo       string `json:"receiver_no" form:"receiver_no" gorm:"column:receiver_no;comment:收款人账号"`                        // 收款人账号
	ApplyStatus      string `json:"apply_status" form:"apply_status" gorm:"column:apply_status;comment:申请状态"`
}

func (rd RequestData) GetPaymentMerchantSync(request request.GetPaymentMerchantSync) (resData []MerchantSyncData, nextPage bool, err error) {
	var result *APIResult
	var data = make(map[string]interface{})
	data["page"] = request.Page
	data["page_size"] = request.PageSize
	data["appId"] = rd.TokenData.AppID
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	rd.Url = string(PaymentMerchantSync)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取主商户信息请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取主商户信息请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	nextPage = result.NextPage
	return
}

type SubMerListResponse struct {
	// Channel 支付通道描述
	Channel string `gorm:"column:channel;comment:支付通道描述" json:"channel"`

	// ParentStoreCode 父级商户数据通商户号
	ParentStoreCode string `gorm:"column:parent_store_code;comment:父级商户数据通商户号" json:"parent_store_code"`

	// CreatedAt 创建时间
	CreatedAt string `gorm:"column:created_at;comment:创建时间" json:"created_at"`

	// MerRegName 商户注册名称
	MerRegName string `gorm:"column:mer_reg_name;comment:商户注册名称" json:"mer_reg_name"`

	// MerOptName 商户经营名称
	MerOptName string `gorm:"column:mer_opt_name;comment:商户经营名称" json:"mer_opt_name"`

	// MerCupNo 银联商户号（以822开头）
	MerCupNo string `gorm:"column:mer_cup_no;comment:银联商户号(以822开头)" json:"mer_cup_no"`

	// MerchantNo 商户号
	MerchantNo string `gorm:"column:merchant_no;comment:商户号" json:"merchant_no"`

	// PaymentStoreCode 聚合支付数据通商户号
	PaymentStoreCode string `gorm:"column:payment_store_code;comment:聚合支付数据通商户号" json:"payment_store_code"`

	// MerBlis 营业执照编码
	MerBlis string `gorm:"column:mer_blis;comment:营业执照编码" json:"mer_blis"`

	// MerContactName 商户联系人
	MerContactName string `gorm:"column:mer_contact_name;comment:商户联系人" json:"mer_contact_name"`

	// MerContactMobile 商户联系人手机号
	MerContactMobile string `gorm:"column:mer_contact_mobile;comment:商户联系人手机号" json:"mer_contact_mobile"`

	// ChannelType 支付通道，1-lakala，2-汇付，3-乐刷，4-富友
	ChannelType int `gorm:"column:channel_type;comment:支付通道:1-lakala,2-汇付,3-乐刷,4-富友" json:"channel_type"`

	// TermNo 终端号
	TermNo string `gorm:"column:term_no;comment:终端号" json:"term_no"`

	// ApplyStatus 分账通状态：0关闭 1开启
	ApplyStatus int `gorm:"column:apply_status;comment:分账通状态:0关闭 1开启" json:"apply_status"`

	// SeparateBindStatus 平台分账绑定状态：0未绑定 1绑定
	SeparateBindStatus *int `gorm:"column:separate_bind_status;comment:平台分账绑定状态:0未绑定 1绑定" json:"separate_bind_status"` //不传字段全部 0未绑定1绑定

	// AreaID 地区编码
	AreaID int `gorm:"column:area_id;comment:地区编码" json:"area_id"`

	// AreaName 地区名称
	AreaName string `gorm:"column:area_name;comment:地区名称" json:"area_name"`
}

// 子商户同步
func (rd RequestData) GetSubMerListSync(request request.GetPaymentMerchantSync) (resData []SubMerListResponse, nextPage bool, err error) {
	var result *APIResult
	var data = make(map[string]interface{})
	data["page"] = request.Page
	data["page_size"] = request.PageSize
	data["appId"] = rd.TokenData.AppID
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	rd.Url = string(SubMerList)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取主商户信息请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取主商户信息请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	nextPage = result.NextPage
	return
}

type PaymentBaseData struct {
	Balance          *float64          `json:"balance"`
	ChannelType      int               `json:"channel_type"`
	Channel          string            `json:"channel"`
	PaymentStoreCode string            `json:"payment_store_code"`
	StoreCode        string            `json:"store_code"`
	PayType          string            `json:"pay_type"`
	WalletID         *string           `json:"wallet_id"`
	SplitStatus      int               `json:"split_status"`
	PaymentInfoList  []PaymentInfoData `json:"payment_info_list"` //以这个为准上面的是没用的旧版本
}

type PaymentInfoData struct {
	Balance          float64 `json:"balance"`
	StoreName        string  `json:"store_name"`         //商户名称
	ChannelType      int     `json:"channel_type"`       //1-lakala，2-汇付，3-乐刷，4-富友
	Channel          string  `json:"channel"`            //支付通道描述
	PaymentStoreCode string  `json:"payment_store_code"` //聚合支付数据通商户号
	StoreCode        string  `json:"store_code"`         //数据通商户号
	PayType          string  `json:"pay_type"`           //支付方式描述
	WalletID         string  `json:"wallet_id"`          //钱包ID
	SplitStatus      int     `json:"split_status"`       //分账状态：0关闭 1开启
}

func (rd RequestData) GetPaymentBase() (resData PaymentBaseData, err error) {
	var result *APIResult
	var data = make(map[string]interface{})
	data["appId"] = rd.TokenData.AppID
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	rd.Url = string(PaymentBase)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取聚合支付主商家基础信息请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取聚合支付主商家基础信息请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type PayInfo struct {
	AppID    string `json:"app_id,omitempty"`
	OpenID   string `json:"open_id,omitempty"`
	MemberID string `json:"member_id,omitempty"`
	UserID   string `json:"user_id,omitempty"`
}

type PaymentRequest struct {
	Attach           Attach      `json:"attach"`
	PaymentStoreCode string      `json:"payment_store_code"`
	OutTradeNo       string      `json:"out_trade_no"`
	PayType          string      `json:"pay_type"`
	SettleType       string      `json:"settle_type,omitempty"`
	TotalAmount      string      `json:"total_amount"`
	Subject          string      `json:"subject"`
	SpbillCreateIP   string      `json:"spbill_create_ip,omitempty"`
	NotifyURL        string      `json:"notify_url,omitempty"`
	PayInfo          PayInfo     `json:"pay_info,omitempty"`
	OrderType        int         `json:"order_type,omitempty"`
	RequestType      string      `json:"request_type,omitempty"`
	FrontURL         string      `json:"front_url,omitempty"`
	PassBack         interface{} `json:"pass_back,omitempty"`
	UserIP           string      `json:"user_ip,omitempty"`
	PayScene         string      `json:"pay_scene,omitempty"`
}
type Attach struct {
	PayType             int    `json:"pay_type" form:"pay_type" gorm:"column:pay_type"`
	PaySN               uint   `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;"` // 编号
	Type                int    `json:"type" form:"type" gorm:"column:type;comment:类型;"`       //0支付（默认没有参数也是支付） 1充值
	PayCode             int    `json:"pay_code" form:"pay_code" gorm:"column:pay_code"`         //0正常 1小商店
	WxOpenid            string `json:"wx_openid" form:"wx_openid" gorm:"column:wx_openid;comment:微信openid;type:varchar(255);size:255;"`
	ProfitSharingStatus int    `json:"profit_sharing_status"` //是否分账 1分 0不分
}

type PayResponse struct {
	PaySN  uint   `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;"` // //充值时候返回 中台的充值订单号
	ErrMsg string `json:"err_msg"`                                                 // 错误说明
	Fields Fields `json:"fields"`                                                  // 返回的详细字段
}

type Fields struct {
	Code         string `json:"code"`           // 状态码
	CodeImage    string `json:"code_image"`     // 状态码图片
	PrepayID     string `json:"prepay_id"`      // 预支付ID
	AppID        string `json:"app_id"`         // AppID
	PaySign      string `json:"pay_sign"`       // 支付签名
	TimeStamp    string `json:"time_stamp"`     // 时间戳
	NonceStr     string `json:"nonce_str"`      // 随机字符串
	Package      string `json:"package"`        // 包装参数
	SignType     string `json:"sign_type"`      // 签名类型
	RedirectURL  string `json:"redirect_url"`   // 重定向URL
	BestPayInfo  string `json:"best_pay_info"`  // 最佳支付信息
	CounterURL   string `json:"counter_url"`    // 收银台URL
	MiniAppAppID string `json:"miniapp_app_id"` // 小程序AppID，用于跳转到收银台小程序
	JumpType     int    `json:"jump_type"`      // 跳转方式：1 公众号，2 小程序
	TradeNo      string `json:"trade_no"`       // 支付宝交易号
}

func (rd RequestData) PaymentPreOrder(paymentRequest PaymentRequest) (resData PayResponse, err error) {
	var result *APIResult
	var data = make(map[string]interface{})
	data["appId"] = rd.TokenData.AppID
	data["attach"] = paymentRequest.Attach
	data["payment_store_code"] = paymentRequest.PaymentStoreCode
	data["out_trade_no"] = paymentRequest.OutTradeNo
	data["pay_type"] = paymentRequest.PayType
	data["settle_type"] = paymentRequest.SettleType
	data["total_amount"] = paymentRequest.TotalAmount
	data["subject"] = paymentRequest.Subject
	data["spbill_create_ip"] = paymentRequest.SpbillCreateIP
	data["notify_url"] = paymentRequest.NotifyURL
	if paymentRequest.PayInfo.OpenID != "" || paymentRequest.PayInfo.MemberID != "" {
		data["pay_info"] = paymentRequest.PayInfo
	}

	data["order_type"] = paymentRequest.OrderType
	data["request_type"] = paymentRequest.RequestType
	data["front_url"] = paymentRequest.FrontURL
	data["pass_back"] = paymentRequest.PassBack
	data["user_ip"] = paymentRequest.UserIP
	data["pay_scene"] = paymentRequest.PayScene

	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	rd.Url = string(PaymentPreOrderUrl)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("获取聚合支付主商家基础信息请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取聚合支付主商家基础信息请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type RefundRequest struct {
	OutTradeNo     string `json:"out_trade_no"`     // 原商户订单号
	RefundAmount   string `json:"refund_amount"`    // 退款金额，单位分（不传默认为全额退款）
	RefundReason   string `json:"refund_reason"`    // 退款原因
	SpbillCreateIP string `json:"spbill_create_ip"` // 用户客户端ip
}

// 退款
func (rd RequestData) PaymentRefund(paymentRequest RefundRequest) (err error) {
	var result *APIResult
	var data = make(map[string]interface{})
	data["out_trade_no"] = paymentRequest.OutTradeNo
	data["refund_amount"] = paymentRequest.RefundAmount
	data["refund_reason"] = paymentRequest.RefundReason
	data["spbill_create_ip"] = paymentRequest.SpbillCreateIP

	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	rd.Url = string(PaymentRefundUrl)
	result, err = rd.ClientPost()
	log.Log().Debug("数据通退款记录", zap.Any("返回数据", result), zap.Any("请求数据", data))
	if err != nil {
		err = errors.New("获取聚合支付主商家基础信息请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("获取聚合支付主商家基础信息请求失败" + result.Msg)
		return
	}
	//resultData, _ := json.Marshal(result.Data)
	//_ = json.Unmarshal(resultData, &resData)
	return
}

type MerchantDetailResponse struct {
	// Status 商户状态: VALID有效 INVALID无效
	Status string `gorm:"column:status;comment:商户状态: VALID有效 INVALID无效" json:"status"`

	// MerInnerNo 商户号 以400开头
	MerInnerNo string `gorm:"column:mer_inner_no;comment:商户号(以400开头)" json:"mer_inner_no"`

	// MerRegName 商户注册名称
	MerRegName string `gorm:"column:mer_reg_name;comment:商户注册名称" json:"mer_reg_name"`

	// MerOptName 商户经营名称
	MerOptName string `gorm:"column:mer_opt_name;comment:商户经营名称" json:"mer_opt_name"`

	// MerEname 商户英文名称
	MerEname string `gorm:"column:mer_ename;comment:商户英文名称" json:"mer_ename"`

	// MerBlis 营业执照编号
	MerBlis string `gorm:"column:mer_blis;comment:营业执照编号" json:"mer_blis"`

	// MerBlisExpDt 营业执照有效期
	MerBlisExpDt *string `gorm:"column:mer_blis_exp_dt;comment:营业执照有效期" json:"mer_blis_exp_dt"`

	// MerRegDistCode 商户注册地址区划码
	MerRegDistCode int `gorm:"column:mer_reg_dist_code;comment:商户注册地址区划码" json:"mer_reg_dist_code"`

	// MerRegAddr 商户注册地址
	MerRegAddr string `gorm:"column:mer_reg_addr;comment:商户注册地址" json:"mer_reg_addr"`

	// MerBusiContent 商户经营内容
	MerBusiContent string `gorm:"column:mer_busi_content;comment:商户经营内容" json:"mer_busi_content"`

	// LarName 法人姓名
	LarName string `gorm:"column:lar_name;comment:法人姓名" json:"lar_name"`

	// LarIDCard 法人身份证号
	LarIDCard string `gorm:"column:lar_idcard;comment:法人身份证号" json:"lar_idcard"`

	// LarCertsTypeCode 法人证件类型 17身份证 18护照 19港澳通行证 20台胞证
	LarCertsTypeCode int `gorm:"column:lar_certs_type_code;comment:法人证件类型:17身份证 18护照 19港澳通行证 20台胞证" json:"lar_certs_type_code"`

	// LarCertsTypeCodeDesc 法人证件类型描述
	LarCertsTypeCodeDesc string `gorm:"column:lar_certs_type_code_desc;comment:法人证件类型描述" json:"lar_certs_type_code_desc"`

	// LarIDCardExpDt 法人身份证有效期
	LarIDCardExpDt string `gorm:"column:lar_idcard_exp_dt;comment:法人身份证有效期" json:"lar_idcard_exp_dt"`

	// MerContactName 商户联系人姓名
	MerContactName string `gorm:"column:mer_contact_name;comment:商户联系人姓名" json:"mer_contact_name"`

	// MerContactTel 商户联系电话
	MerContactTel string `gorm:"column:mer_contact_tel;comment:商户联系电话" json:"mer_contact_tel"`

	// MerContactMobile 商户联系人手机号
	MerContactMobile string `gorm:"column:mer_contact_mobile;comment:商户联系人手机号" json:"mer_contact_mobile"`

	// MerGradeCode 商户等级
	MerGradeCode *string `gorm:"column:mer_grade_code;comment:商户等级" json:"mer_grade_code"`

	// MerCityCode 城市编码
	MerCityCode string `gorm:"column:mer_city_code;comment:城市编码" json:"mer_city_code"`

	// MCCCode 商户类型码
	MCCCode string `gorm:"column:mcc_code;comment:商户类型码" json:"mcc_code"`

	// MerCupDistCode 银联区域码（地市）
	MerCupDistCode *int `gorm:"column:mer_cup_dist_code;comment:银联区域码（地市）" json:"mer_cup_dist_code"`

	// MerCupCountyCode 银联区域码（县乡）
	MerCupCountyCode *int `gorm:"column:mer_cup_county_code;comment:银联区域码（县乡）" json:"mer_cup_county_code"`

	// DiscountID 银联优惠标识
	DiscountID *string `gorm:"column:discount_id;comment:银联优惠标识" json:"discount_id"`

	// MerDeveloperOrgID 商户拓展机构
	MerDeveloperOrgID *string `gorm:"column:mer_developer_org_id;comment:商户拓展机构" json:"mer_developer_org_id"`

	// MerCupNo 银联商户号 以822开头
	MerCupNo string `gorm:"column:mer_cup_no;comment:银联商户号(以822开头)" json:"mer_cup_no"`

	// CreateTime 创建时间
	CreateTime string `gorm:"column:create_time;comment:创建时间" json:"create_time"`

	// ModifyLstTm 最后修改时间
	ModifyLstTm *string `gorm:"column:modify_lst_tm;comment:最后修改时间" json:"modify_lst_tm"`

	// VerNo 版本号
	VerNo *string `gorm:"column:ver_no;comment:版本号" json:"ver_no"`

	// SplitStatus 分账状态:VALID启用/INVALID禁用
	SplitStatus string `gorm:"column:split_status;comment:分账状态:VALID启用/INVALID禁用" json:"split_status"`

	// OrgID 分账商户机构号
	OrgID *string `gorm:"column:org_id;comment:分账商户机构号" json:"org_id"`

	// OrgName 分账商户机构名称
	OrgName *string `gorm:"column:org_name;comment:分账商户机构名称" json:"org_name"`

	// SplitLowestRatio 最低分账比例（百分比，支持2位精度）
	SplitLowestRatio string `gorm:"column:split_lowest_ratio;comment:最低分账比例（百分比，支持2位精度）" json:"split_lowest_ratio"`

	// SplitRange 分账范围
	SplitRange string `gorm:"column:split_range;comment:分账范围" json:"split_range"`

	// SepFundSource 分账依据
	SepFundSource string `gorm:"column:sep_fund_source;comment:分账依据" json:"sep_fund_source"`

	// EwalletID 钱包id
	EwalletID *string `gorm:"column:ewallet_id;comment:钱包id" json:"ewallet_id"`

	// CollectAccountID 收款账户id
	CollectAccountID *string `gorm:"column:collect_account_id;comment:收款账户id" json:"collect_account_id"`

	// SettleType 提款模式:01主动提款 02自动结算
	SettleType *string `gorm:"column:settle_type;comment:提款模式:01主动提款 02自动结算" json:"settle_type"`

	// SettleTypeDesc 提款模式描述
	SettleTypeDesc *string `gorm:"column:settle_type_desc;comment:提款模式描述" json:"settle_type_desc"`

	// SettleTime 结算时间(小时)
	SettleTime *string `gorm:"column:settle_time;comment:结算时间(小时)" json:"settle_time"`
}

func (rd RequestData) GetMerchantDetail(paymentStoreCode string) (resData MerchantDetailResponse, err error) {
	var data = make(map[string]interface{})
	data["payment_store_code"] = paymentStoreCode
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(MerchantDetail)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求商户详情失败请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求商户详情失败请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type SubMerDetailResponse struct {
	// Status 商户状态: VALID有效 INVALID无效
	Status string `gorm:"column:status;comment:商户状态: VALID有效 INVALID无效" json:"status"`

	// MerInnerNo 商户号 以400开头
	MerInnerNo string `gorm:"column:mer_inner_no;comment:商户号(以400开头)" json:"mer_inner_no"`

	// MerRegName 商户注册名称
	MerRegName string `gorm:"column:mer_reg_name;comment:商户注册名称" json:"mer_reg_name"`

	// MerOptName 商户经营名称
	MerOptName string `gorm:"column:mer_opt_name;comment:商户经营名称" json:"mer_opt_name"`

	// MerEname 商户英文名称
	MerEname string `gorm:"column:mer_ename;comment:商户英文名称" json:"mer_ename"`

	// MerBlis 营业执照编号
	MerBlis string `gorm:"column:mer_blis;comment:营业执照编号" json:"mer_blis"`

	// MerBlisExpDt 营业执照有效期（时间戳）
	MerBlisExpDt *int `gorm:"column:mer_blis_exp_dt;comment:营业执照有效期" json:"mer_blis_exp_dt"`

	// MerRegDistCode 商户注册地址区划码
	MerRegDistCode int `gorm:"column:mer_reg_dist_code;comment:商户注册地址区划码" json:"mer_reg_dist_code"`

	// MerRegAddr 商户注册地址
	MerRegAddr string `gorm:"column:mer_reg_addr;comment:商户注册地址" json:"mer_reg_addr"`

	// MerBusiContent 商户经营内容
	MerBusiContent string `gorm:"column:mer_busi_content;comment:商户经营内容" json:"mer_busi_content"`

	// LarName 法人姓名
	LarName string `gorm:"column:lar_name;comment:法人姓名" json:"lar_name"`

	// LarIDCard 法人身份证号
	LarIDCard string `gorm:"column:lar_idcard;comment:法人身份证号" json:"lar_idcard"`

	// LarCertsTypeCode 法人证件类型：17身份证 18护照 19港澳通行证 20台胞证
	LarCertsTypeCode int `gorm:"column:lar_certs_type_code;comment:法人证件类型:17身份证 18护照 19港澳通行证 20台胞证" json:"lar_certs_type_code"`

	// LarCertsTypeCodeDesc 法人证件类型描述
	LarCertsTypeCodeDesc string `gorm:"column:lar_certs_type_code_desc;comment:法人证件类型描述" json:"lar_certs_type_code_desc"`

	// LarIDCardExpDt 法人身份证有效期（格式如 "20440407"）
	LarIDCardExpDt string `gorm:"column:lar_idcard_exp_dt;comment:法人身份证有效期" json:"lar_idcard_exp_dt"`

	// MerContactName 商户联系人姓名
	MerContactName string `gorm:"column:mer_contact_name;comment:商户联系人姓名" json:"mer_contact_name"`

	// MerContactTel 商户联系电话
	MerContactTel string `gorm:"column:mer_contact_tel;comment:商户联系电话" json:"mer_contact_tel"`

	// MerContactMobile 商户联系人手机号
	MerContactMobile string `gorm:"column:mer_contact_mobile;comment:商户联系人手机号" json:"mer_contact_mobile"`

	// MerGradeCode 商户等级编码
	MerGradeCode *int `gorm:"column:mer_grade_code;comment:商户等级" json:"mer_grade_code"`

	// MerCityCode 城市编码
	MerCityCode string `gorm:"column:mer_city_code;comment:城市编码" json:"mer_city_code"`

	// MCCCode 商户类型码
	MCCCode string `gorm:"column:mcc_code;comment:商户类型码" json:"mcc_code"`

	// MerCupDistCode 银联区域码（地市）
	MerCupDistCode *int `gorm:"column:mer_cup_dist_code;comment:银联区域码（地市）" json:"mer_cup_dist_code"`

	// MerCupCountyCode 银联区域码（县乡）
	MerCupCountyCode *int `gorm:"column:mer_cup_county_code;comment:银联区域码（县乡）" json:"mer_cup_county_code"`

	// DiscountID 银联优惠标识
	DiscountID *int `gorm:"column:discount_id;comment:银联优惠标识" json:"discount_id"`

	// MerDeveloperOrgID 商户拓展机构 ID
	MerDeveloperOrgID *int `gorm:"column:mer_developer_org_id;comment:商户拓展机构" json:"mer_developer_org_id"`

	// MerCupNo 银联商户号（以822开头）
	MerCupNo string `gorm:"column:mer_cup_no;comment:银联商户号(以822开头)" json:"mer_cup_no"`

	// CreateTime 创建时间
	CreateTime string `gorm:"column:create_time;comment:创建时间" json:"create_time"`

	// ModifyLstTm 最后修改时间（时间戳）
	ModifyLstTm *int `gorm:"column:modify_lst_tm;comment:最后修改时间" json:"modify_lst_tm"`

	// VerNo 版本号
	VerNo *int `gorm:"column:ver_no;comment:版本号" json:"ver_no"`

	// SplitStatus 分账状态:VALID启用/INVALID禁用
	SplitStatus string `gorm:"column:split_status;comment:分账状态:VALID启用/INVALID禁用" json:"split_status"`

	// OrgID 分账商户机构号
	OrgID *string `gorm:"column:org_id;comment:分账商户机构号" json:"org_id"`

	// OrgName 分账商户机构名称
	OrgName *string `gorm:"column:org_name;comment:分账商户机构名称" json:"org_name"`

	// SplitLowestRatio 最低分账比例（百分比，支持2位精度）
	SplitLowestRatio string `gorm:"column:split_lowest_ratio;comment:最低分账比例（百分比，支持2位精度）" json:"split_lowest_ratio"`

	// SplitRange 分账范围
	SplitRange string `gorm:"column:split_range;comment:分账范围" json:"split_range"`

	// SepFundSource 分账依据
	SepFundSource string `gorm:"column:sep_fund_source;comment:分账依据" json:"sep_fund_source"`
}

// 子商户详情
func (rd RequestData) GetSubMerDetail(paymentStoreCode string) (resData SubMerDetailResponse, err error) {
	var data = make(map[string]interface{})
	data["payment_store_code"] = paymentStoreCode
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SubMerDetail)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求商户详情失败请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求商户详情失败请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

// 子商户详情
func (rd RequestData) AddHuiFuSubMer(requestData request.AddSubMer) (resData SubMerListResponse, err error) {
	var data = make(map[string]interface{})
	data["parent_store_code"] = requestData.ParentStoreCode
	data["merchant_no"] = requestData.MerchantNo
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(HuifuSubMerAddApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求新增子商户失败请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求新增子商户失败请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

// 创建子商户
func (rd RequestData) AddLaKaLaSubMer(requestData request.AddSubMer) (resData SubMerListResponse, err error) {
	var data = make(map[string]interface{})
	data["parent_store_code"] = requestData.ParentStoreCode
	data["merchant_no"] = requestData.MerchantNo
	data["register_name"] = requestData.RegisterName
	data["operate_name"] = requestData.OperateName
	data["term_no"] = requestData.TermNo
	data["vpos_id"] = requestData.VposID
	data["mer_cup_no"] = requestData.MerCupNo
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(LaKaLaSubMerAddApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求新增子商户失败请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求新增子商户失败请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

// 开通分账  分账设置 分账修改结构体
type SubMerSplitSettlement struct {
	// PaymentStoreCode 聚合支付数据通商户号
	PaymentStoreCode string `json:"payment_store_code" gorm:"column:payment_store_code"` // 聚合支付数据通商户号

	// ContactMobile 联系手机号
	ContactMobile string `json:"contact_mobile" gorm:"column:contact_mobile"` // 联系手机号

	// SplitLowestRatio 最低分账比例（百分比，支持2位精度），例如：70 或 70.50
	SplitLowestRatio string `json:"split_lowest_ratio" gorm:"column:split_lowest_ratio"` // 最低分账比例，百分比支持2位精度

	// EntrustFileName 分账结算委托书文件名称
	EntrustFileName string `json:"entrust_file_name" gorm:"column:entrust_file_name"` // 分账结算委托书文件名称

	// EntrustFile 分账结算委托书文件
	EntrustFile string `json:"entrust_file" gorm:"column:entrust_file"` // 分账结算委托书文件

	// EntrustFilePath 分账结算委托书文件路径（base64编码）
	EntrustFilePath string `json:"entrust_file_path" gorm:"column:entrust_file_path;type:text"` // 分账结算委托书文件路径（base64）
}

// 申请修改返回的结构
// 分账申请返回参数
type MerchantAuditInfo struct {
	// PaymentStoreCode 聚合支付数据通商户号
	PaymentStoreCode string `json:"payment_store_code" gorm:"column:payment_store_code"` // 聚合支付数据通商户号

	// AuditStatus 审核状态：0-待审核, 1-审核通过, 2-审核驳回
	AuditStatus int `json:"audit_status" gorm:"column:audit_status"` // 审核状态：0-待审核, 1-审核通过, 2-审核驳回

	// AuditStatusDesc 审核状态描述
	AuditStatusDesc string `json:"audit_status_desc" gorm:"column:audit_status_desc"` // 审核状态描述

}

// 请求查询返回的结构
type MerchantAudit struct {
	// PaymentStoreCode 聚合支付数据通商户号
	PaymentStoreCode string `json:"payment_store_code" gorm:"column:payment_store_code"` // 聚合支付数据通商户号
	SplitType        int    `json:"split_type" gorm:"column:split_type"`                 // 审核状态：1申请, 2修改

	// AuditStatus 审核状态：0-待审核, 1-审核通过, 2-审核驳回
	SplitStatus int `json:"split_status" gorm:"column:split_status"` // 审核状态：0-待审核, 1-审核通过, 2-审核驳回

	// AuditStatusDesc 审核状态描述
	SplitStatusDesc string `json:"split_status_desc" gorm:"column:split_status_desc"` // 审核状态描述
}

func (rd RequestData) ApplySubMerSplitSettlement(requestData SubMerSplitSettlement) (resData MerchantAuditInfo, err error) {
	var data = make(map[string]interface{})
	data["payment_store_code"] = requestData.PaymentStoreCode
	data["contact_mobile"] = requestData.ContactMobile
	data["split_lowest_ratio"] = requestData.SplitLowestRatio
	data["entrust_file_name"] = requestData.EntrustFileName
	data["entrust_file"] = requestData.EntrustFile
	data["entrust_file_path"] = base64.StdEncoding.EncodeToString([]byte(requestData.EntrustFile))

	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitApplyApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求开通分账API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求开通分账API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

// 修改分账信息
func (rd RequestData) SaveSubMerSplitSettlement(requestData SubMerSplitSettlement) (resData MerchantAuditInfo, err error) {
	var data = make(map[string]interface{})
	data["payment_store_code"] = requestData.PaymentStoreCode
	data["contact_mobile"] = requestData.ContactMobile
	data["split_lowest_ratio"] = requestData.SplitLowestRatio
	data["entrust_file_name"] = requestData.EntrustFileName
	data["entrust_file"] = requestData.EntrustFile
	data["entrust_file_path"] = base64.StdEncoding.EncodeToString([]byte(requestData.EntrustFile))

	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitModifyApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求修改分账API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求修改分账API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type BindOrUnbindSubMerSplitSettlement struct {
	Remark           string `json:"remark,omitempty"`                                            // 备注说明，解绑必传
	Type             int    `json:"type,omitempty"`                                              // 绑定类型，0-绑定，1-解绑 不传则默认为绑定0
	PaymentStoreCode string `json:"payment_store_code,omitempty"`                                // 聚合支付数据通商户号
	EntrustFileName  string `json:"entrust_file_name,omitempty"`                                 // 合作/解除协议附件名称
	EntrustFile      string `json:"entrust_file,omitempty"`                                      // 合作/解除协议附件
	EntrustFilePath  string `json:"entrust_file_path" gorm:"column:entrust_file_path;type:text"` // 分账结算委托书文件路径（base64）

}

// 修改分账信息
func (rd RequestData) BindOrUnbindSubMerSplitSettlement(requestData BindOrUnbindSubMerSplitSettlement) (resData MerchantAuditInfo, err error) {
	var data = make(map[string]interface{})
	data["payment_store_code"] = requestData.PaymentStoreCode
	data["entrust_file_name"] = requestData.EntrustFileName
	data["entrust_file"] = requestData.EntrustFile
	data["entrust_file_path"] = base64.StdEncoding.EncodeToString([]byte(requestData.EntrustFile))
	data["remark"] = requestData.Remark
	data["type"] = requestData.Type

	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitBind)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求绑定解绑分账方API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求绑定解绑分账方API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

// 获取开通修改分账审核状态
func (rd RequestData) GetSubMerSplitSettlement(paymentStoreCode string) (resData MerchantAudit, err error) {
	var data = make(map[string]interface{})
	data["payment_store_code"] = paymentStoreCode
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitApplyQueryApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求修改分账API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求修改分账API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type SubMerSplitBindStatusInfo struct {
	Status           int    `json:"status,omitempty"`             // 审核状态码，0-待审核，1-已通过，2-已拒绝
	PaymentStoreCode string `json:"payment_store_code,omitempty"` // 聚合支付数据通商户号
	StatusDesc       string `json:"status_desc,omitempty"`        // 审核状态描述
	Remark           string `json:"remark,omitempty"`
}

// 获取分账方绑定/解绑审核状态
func (rd RequestData) GetSubMerSplitBind(paymentStoreCode string) (resData SubMerSplitBindStatusInfo, err error) {
	var data = make(map[string]interface{})
	data["payment_store_code"] = paymentStoreCode
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitBindQueryApi)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求修改分账API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求修改分账API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type SplitAmountQuery struct {
	OrderCode        string `json:"order_code"`         // 商户订单号
	TotalSeparateAmt string `json:"total_separate_amt"` // 总分账金额（单位：分）
	CanSeparateAmt   string `json:"can_separate_amt"`   // 可分账金额（单位：分）
}

// 获取可分账金额
func (rd RequestData) GetSplitAmtQuery(orderCode uint) (resData SplitAmountQuery, err error) {
	var data = make(map[string]interface{})
	data["order_code"] = orderCode
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitAmtQuery)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求修改分账API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求修改分账API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

// 分账请求参数结构体
type SplitRequest struct {
	OrderCode     string     `json:"order_code"`           // 商户订单号
	OutSeparateNo string     `json:"out_separate_no"`      // 商户分账指令流水号 		//加上-0代表是主商户的分账  -1是子商户的分账
	TotalAmt      string     `json:"total_amt"`            // 分账总金额(单位:分)
	CalType       string     `json:"cal_type"`             // 分账计算类型(0-按照指定金额，1-按照指定比例。默认为0)
	NotifyURL     string     `json:"notify_url,omitempty"` // 回调地址 (可选)
	RecvDatas     []RecvData `json:"recv_datas,omitempty"` // 分账接收数据对象 (可选)
}

// 分账接收数据对象结构体
type RecvData struct {
	RecvNo        string `json:"recv_no,omitempty"`       // P开头聚合支付数据通商户号（主商户），分账给平台接收方时用此字段，二者只能填其一，不可同时为空
	RecvStoreNo   string `json:"recv_store_no,omitempty"` // P开头聚合支付数据通商户号（子商户），分账给商户自身时用此字段，二者只能填其一，不可同时为空
	SeparateValue string `json:"separate_value"`          // 分账数值，指定金额分账，单位分；按比例分账，百分比的小数值例如0.55
}

// 分账结果响应结构体
type SplitResponse struct {
	SeparateNo    string `json:"separate_no"`     // 支付通道分账指令流水号
	OutSeparateNo string `json:"out_separate_no"` // 请求报文中的商户分账指令流水号
	Status        string `json:"status"`          // 分账状态: PROCESSING(处理中), ACCEPTED(已受理), SUCCESS(成功), FAIL(失败)
	StatusDesc    string `json:"status_desc"`     // 分账状态描述
	LogNo         string `json:"log_no"`          // 拉卡拉对账单流水号
	LogDate       string `json:"log_date"`        // 拉卡拉订单日期，格式: yyyyMMdd
	TotalAmt      string `json:"total_amt"`       // 分账总金额(单位: 分)
}

// 分账
func (rd RequestData) SplitAmt(requestData SplitRequest) (resData SplitResponse, err error) {
	var data = make(map[string]interface{})
	data["order_code"] = requestData.OrderCode
	data["out_separate_no"] = requestData.OutSeparateNo
	data["total_amt"] = requestData.TotalAmt
	data["cal_type"] = 0
	data["notify_url"] = requestData.NotifyURL
	data["recv_datas"] = requestData.RecvDatas
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitAmtAPI)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求修改分账API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求修改分账API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}

type SplitStatusResponse struct {
	OutSeparateNo string `json:"out_separate_no"` // 商户分账指令流水号
	Status        string `json:"status"`          // 分账状态: PROCESSING(处理中), ACCEPTED(已受理), SUCCESS(成功), FAIL(失败)
	StatusDesc    string `json:"status_desc"`     // 分账结果状态描述
}

// 获取分账状态
func (rd RequestData) GetSplitAmtStatusQuery(outSeparateNo string) (resData SplitStatusResponse, err error) {
	var data = make(map[string]interface{})
	data["out_separate_no"] = outSeparateNo
	rd.Body, err = json.Marshal(data)
	if err != nil {
		err = errors.New("参数转json失败:" + err.Error())
		return
	}
	var result *APIResult
	rd.Url = string(SplitAmtStatusQueryAPI)
	result, err = rd.ClientPost()
	if err != nil {
		err = errors.New("请求修改分账API请求失败" + err.Error())
		return
	}
	if result.Status == false {
		err = errors.New("请求修改分账API请求失败" + result.Msg)
		return
	}
	resultData, _ := json.Marshal(result.Data)
	_ = json.Unmarshal(resultData, &resData)
	return
}
func (rd RequestData) ClientPost() (result *APIResult, err error) {
	client := &http.Client{Timeout: 5 * time.Minute}
	//if Request.Body = nil
	body := bytes.NewReader(rd.Body)

	req, _ := http.NewRequest("POST", rd.Host+rd.Url, body)

	req = rd.SetClentHeader(req)

	resp, err := client.Do(req)

	if err != nil {
		return
	}
	defer resp.Body.Close()

	res, _ := ioutil.ReadAll(resp.Body)
	err = json.Unmarshal(res, &result)

	if err != nil {
		log.Log().Error("JSON异常", zap.Any("err", err), zap.Any("data", rd))
	}

	//log.Log().Info("im返回",zap.Any("Url",rd.Url),zap.Any("result",result))
	return
}

func (rd RequestData) SetClentHeader(request *http.Request) (req *http.Request) {

	request.Header.Add("app-key", rd.Config.Value.AppKey)
	request.Header.Add("access-token", rd.Token)
	request.Header.Add("Content-Type", "application/json;charset=utf-8")

	return request
}
