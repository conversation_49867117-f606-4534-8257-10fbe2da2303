package request

import (
	yzRequest "yz-go/request"
)

type MemberOperationSearch struct {
	yzRequest.PageInfo
	StartAT string `json:"start_at"  form:"start_at"`
	EndAT   string `json:"end_at" form:"end_at"`
	Type    uint   `json:"type" form:"type"`
}

type CommunicationMerchantSyncDataSearch struct {
	yzRequest.PageInfo
	PaymentStoreCode string `json:"payment_store_code" form:"payment_store_code"`
	StoreName        string `json:"store_name" form:"store_name"`
}

// 新增子商户
type AddSubMer struct {
	ParentStoreCode string `json:"parent_store_code" gorm:"column:parent_store_code"` // 父级商户数据通商户号
	RegisterName    string `json:"register_name" gorm:"column:register_name"`         // 商户注册名称，必填
	OperateName     string `json:"operate_name" gorm:"column:operate_name"`           // 商户经营名称
	MerCupNo        string `json:"mer_cup_no" gorm:"column:mer_cup_no"`               // 822银联商户号
	MerchantNo      string `json:"merchant_no" gorm:"column:merchant_no"`             // 商户号，必填
	TermNo          string `json:"term_no" gorm:"column:term_no"`                     // 终端号
	VposID          string `json:"vpos_id" gorm:"column:vpos_id"`                     // 实体终端号
}
