package service

import (
	"errors"
	"go.uber.org/zap"
	pay "payment/model"
	"service-provider-system/common"
	"service-provider-system/model"
	"service-provider-system/request"
	"service-provider-system/response"
	"strings"

	"yz-go/component/log"
	"yz-go/source"
)

// GetCommunicationMerchantSyncDataList 获取主商户同步数据列表
func GetCommunicationMerchantSyncDataList(pageInfo request.CommunicationMerchantSyncDataSearch) (err error, list []response.GetCommunicationMerchantSyncDataList, total int64) {
	limit := pageInfo.PageSize
	offset := pageInfo.PageSize * (pageInfo.Page - 1)

	db := source.DB().Model(&response.GetCommunicationMerchantSyncDataList{}).Preload("CommunicationPaymentInfo")

	// 添加查询条件
	if pageInfo.PaymentStoreCode != "" {
		db = db.Where("payment_store_code LIKE ?", "%"+pageInfo.PaymentStoreCode+"%")
	}
	if pageInfo.StoreName != "" {
		db = db.Where("store_name LIKE ?", "%"+pageInfo.StoreName+"%")
	}

	// 获取总数
	err = db.Count(&total).Error
	if err != nil {
		return err, nil, 0
	}

	// 获取列表
	err = db.Limit(limit).Offset(offset).Find(&list).Error
	if err != nil {
		return err, nil, 0
	}

	return nil, list, total
}

// GetAllCommunicationMerchantSyncData 获取所有主商户同步数据
func GetAllCommunicationPaymentInfo() (err error, list []model.CommunicationPaymentInfo) {
	err = source.DB().Model(&model.CommunicationPaymentInfo{}).Find(&list).Error
	return err, list
}
func PaymentMerchantSync() (err error) {
	err, rd := common.Initial()
	if err != nil {
		return
	}
	var requestSearch request.GetPaymentMerchantSync
	requestSearch.Page = 1
	requestSearch.PageSize = 50
	data, nextPage, err := rd.GetPaymentMerchantSync(requestSearch)
	if err != nil {
		log.Log().Error("获取主商户信息失败", zap.Any("err", err))
		return
	}
	paymentBase, err := rd.GetPaymentBase()
	if err != nil {
		log.Log().Error("获取聚合支付主商家基础信息请求失败", zap.Any("err", err))
		return
	}
	if len(paymentBase.PaymentInfoList) == 0 {
		err = errors.New("未获取到任何支付方式")
		return
	}
	if len(data) > 0 {
		//获取之前保存的
		var olds []model.CommunicationMerchantSyncData
		source.DB().Model(&model.CommunicationMerchantSyncData{}).Find(&olds)
		var oldsMake = make(map[string]uint)
		if olds != nil {
			for _, item := range olds {
				oldsMake[item.PaymentStoreCode] = item.ID
			}
		}
		//获取之前保存的
		var oldCommunicationPaymentInfos []model.CommunicationPaymentInfo
		source.DB().Model(&model.CommunicationPaymentInfo{}).Find(&oldCommunicationPaymentInfos)
		var oldCommunicationPaymentInfosMake = make(map[string]uint)
		if oldCommunicationPaymentInfos != nil {
			for _, item := range oldCommunicationPaymentInfos {
				oldCommunicationPaymentInfosMake[item.PaymentStoreCode] = item.ID
			}
		}
		//旧的支付方式
		var oldCommunicationPaymentInfoTypes []model.CommunicationPaymentInfoType
		source.DB().Model(&model.CommunicationPaymentInfoType{}).Find(&oldCommunicationPaymentInfoTypes)
		var oldCommunicationPaymentInfoTypesMake = make(map[string]uint)
		if oldCommunicationPaymentInfos != nil {
			for _, item := range oldCommunicationPaymentInfoTypes {
				oldCommunicationPaymentInfoTypesMake[item.PaymentStoreCode+item.PayType] = item.ID
			}
		}
		//获取之前保存的
		var oldCommunicationMerchantDetails []model.CommunicationMerchantDetail
		source.DB().Model(&model.CommunicationMerchantDetail{}).Find(&oldCommunicationMerchantDetails)
		var oldCommunicationMerchantDetailsMake = make(map[string]uint)
		if oldCommunicationMerchantDetails != nil {
			for _, item := range oldCommunicationMerchantDetails {
				oldCommunicationMerchantDetailsMake[item.PaymentStoreCode] = item.ID
			}
		}
		go GetPaymentMerchantSyncStart(rd, requestSearch, data, nextPage, oldsMake, oldCommunicationMerchantDetailsMake)
		go GetPaymentPaymentInfoStart(paymentBase, oldCommunicationPaymentInfosMake, oldCommunicationPaymentInfoTypesMake)

	} else {
		err = errors.New("没有任何主商户信息")
		return
	}
	return
}

// 同步支付方式
func GetPaymentPaymentInfoStart(data common.PaymentBaseData, oldCommunicationPaymentInfos, oldCommunicationPaymentInfoTypesMake map[string]uint) {
	var err error
	for _, item := range data.PaymentInfoList {
		//钱包表
		if oldCommunicationPaymentInfos != nil && len(oldCommunicationPaymentInfos) > 0 {
			value, exists := oldCommunicationPaymentInfos[item.PaymentStoreCode]
			if exists {
				err = source.DB().Model(&model.CommunicationPaymentInfo{}).Where("id = ?", value).Updates(&model.CommunicationPaymentInfo{
					PaymentInfoData: item,
				}).Error
				if err != nil {
					log.Log().Error("修改聚合支付主商家基础信息失败", zap.Any("err", err), zap.Any("item", item))
				}
			} else {
				err = source.DB().Model(&model.CommunicationPaymentInfo{}).Create(&model.CommunicationPaymentInfo{
					PaymentInfoData: item,
				}).Error
				if err != nil {
					log.Log().Error("创建聚合支付主商家基础信息失败", zap.Any("err", err), zap.Any("item", item))
				}
			}
		} else {
			err = source.DB().Model(&model.CommunicationPaymentInfo{}).Create(&model.CommunicationPaymentInfo{
				PaymentInfoData: item,
			}).Error
			if err != nil {
				log.Log().Error("创建聚合支付主商家基础信息失败", zap.Any("err", err), zap.Any("item", item))
			}
		}
		//支付方式表
		payTypes := strings.Split(item.PayType, ",")
		for _, payType := range payTypes {
			value, exists := oldCommunicationPaymentInfoTypesMake[item.PaymentStoreCode+payType]
			var communicationPaymentInfoType model.CommunicationPaymentInfoType
			communicationPaymentInfoType.PaymentInfoData = item
			communicationPaymentInfoType.PaymentInfoData.PayType = payType
			if exists {
				err = source.DB().Model(&model.CommunicationPaymentInfoType{}).Where("id = ?", value).Updates(&communicationPaymentInfoType).Error
				if err != nil {
					log.Log().Error("修改聚合支付主商家支付方式失败", zap.Any("err", err), zap.Any("item", item))
				}
			} else {
				err = source.DB().Model(&model.CommunicationPaymentInfoType{}).Create(&communicationPaymentInfoType).Error
				if err != nil {
					log.Log().Error("创建聚合支付主商家支付方式失败", zap.Any("err", err), zap.Any("item", item))
				}
			}
		}
		//删除不存在的支付方式
		source.DB().Model(&model.CommunicationPaymentInfoType{}).Where("payment_store_code = ?", item.PaymentStoreCode).Where("pay_type not in ?", payTypes).Delete(&model.CommunicationPaymentInfoType{})
	}
	pay.ReseatPayStoreCommunicationPaymentInfos()
}

// 同步主商户

func GetPaymentMerchantSyncStart(rd common.RequestData, requestSearch request.GetPaymentMerchantSync, data []common.MerchantSyncData, nextPage bool, oldsMake map[string]uint, oldDetail map[string]uint) {
	var err error
	for _, item := range data {
		if oldsMake != nil && len(oldsMake) > 0 {
			value, exists := oldsMake[item.PaymentStoreCode]
			if exists {
				err = source.DB().Model(&model.CommunicationMerchantSyncData{}).Where("id = ?", value).Updates(&model.CommunicationMerchantSyncData{
					MerchantSyncData: item,
				}).Error
				if err != nil {
					log.Log().Error("修改主商户失败", zap.Any("err", err), zap.Any("item", item))
				}
			} else {
				err = source.DB().Model(&model.CommunicationMerchantSyncData{}).Create(&model.CommunicationMerchantSyncData{
					MerchantSyncData: item,
				}).Error
				if err != nil {
					log.Log().Error("创建主商户失败", zap.Any("err", err), zap.Any("item", item))
				}
			}
		} else {
			err = source.DB().Model(&model.CommunicationMerchantSyncData{}).Create(&model.CommunicationMerchantSyncData{
				MerchantSyncData: item,
			}).Error
			if err != nil {
				log.Log().Error("创建主商户失败", zap.Any("err", err), zap.Any("item", item))
			}
		}
		var merchantDetail common.MerchantDetailResponse
		merchantDetail, err = rd.GetMerchantDetail(item.PaymentStoreCode)
		if err != nil {
			log.Log().Error("获取主商户详情失败", zap.Any("err", err), zap.Any("item", item))
			continue
		}
		value, exists := oldDetail[item.PaymentStoreCode]
		if exists {
			err = source.DB().Model(&model.CommunicationMerchantDetail{}).Where("id = ?", value).Updates(&model.CommunicationMerchantDetail{
				MerchantDetailResponse: merchantDetail,
				PaymentStoreCode:       item.PaymentStoreCode,
			}).Error
			if err != nil {
				log.Log().Error("修改主商户详情失败", zap.Any("err", err), zap.Any("item", item))
			}
		} else {
			err = source.DB().Model(&model.CommunicationMerchantDetail{}).Create(&model.CommunicationMerchantDetail{
				MerchantDetailResponse: merchantDetail,
				PaymentStoreCode:       item.PaymentStoreCode,
			}).Error
			if err != nil {
				log.Log().Error("创建主商户详情失败", zap.Any("err", err), zap.Any("item", item))
			}
		}
	}
	//如果存在下一页
	if nextPage == true {
		requestSearch.Page++
		data, nextPage, err = rd.GetPaymentMerchantSync(requestSearch)
		GetPaymentMerchantSyncStart(rd, requestSearch, data, nextPage, oldsMake, oldDetail)
	} else {
		//同步聚合支付主商家基础信息 -- 包含钱包，余额 支付方式等等 支付方式以这个表为准

	}

}
