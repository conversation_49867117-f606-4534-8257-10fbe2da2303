package model

import (
	"gorm.io/gorm"
	"service-provider-system/common"
	"yz-go/source"
)

// 推送用户记录表
type ServiceProviderSystemPushUser struct {
	source.Model
	UserId            uint   `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;index;"`                                  // 用户id
	Status            int    `json:"status" form:"status" gorm:"column:status;comment:是否推送1是0否;default:0;type:int(11);"`                // 是否推送1是0否
	StoreName         string `json:"store_name" form:"store_name" gorm:"column:store_name;comment:关联商户简称;"`                             // 关联商户简称
	StoreCode         string `json:"store_code" form:"store_code" gorm:"column:store_code;comment:关联商户编号;"`                             // 关联商户编号
	DirectlyStoreName string `json:"directly_store_name" form:"directly_store_name" gorm:"column:directly_store_name;comment:直属服务商简称;"` // 关联商户简称
	DirectlyStoreCode string `json:"directly_store_code" form:"directly_store_code" gorm:"column:directly_store_code;comment:直属服务商编号;"` // 关联商户编号
	Msg               string `json:"msg" form:"msg" gorm:"column:msg;comment:错误信息;"`                                                    //错误信息
}

type ServiceProviderSystemPushOrder struct {
	source.Model
	OrderId uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"` // 订单id
	OrderSN uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;index;"`   // 编号
	Status  int    `json:"status" form:"status" gorm:"column:status;comment: 0待推送1部分推送2全部推送;"`  // 0待推送1部分推送2全部推送
	Msg     string `json:"msg" form:"msg" gorm:"column:msg;comment:错误信息;"`                      //错误信息
}

type ServiceProviderSystemPushOrderItem struct {
	source.Model
	OrderId     uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;index;"`                 // 订单id
	OrderSN     uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;index;"`                   // 编号
	OrderItemId uint   `json:"order_item_id" form:"order_item_id" gorm:"column:order_item_id;comment:子订单id;index;"` // 子订单id
	Status      int    `json:"status" form:"status" gorm:"column:status;comment:推送状态0待推送1已推送;"`                     // 0待推送1已推送
	Msg         string `json:"msg" form:"msg" gorm:"column:msg;comment:错误信息;"`                                      //错误信息
}

type MemberLevelRelation struct {
	source.Model
	LevelID         uint `json:"level_id"`
	RelationLevelID uint `json:"relation_level_id"`
}

type MemberOperationRecord struct {
	source.Model
	UserID  uint   `json:"user_id"`
	Type    uint   `json:"type"`    //1会员注册 2会员修改
	Content string `json:"content"` //记录说明
}

// 主商家数据
type CommunicationMerchantSyncData struct {
	source.Model
	common.MerchantSyncData
}

// 聚合支付主商家基础信息 -- 包含余额
type CommunicationPaymentInfo struct {
	source.Model
	common.PaymentInfoData
	PayTypeName string `json:"pay_type_name"`
}

// 聚合支付主商家基础信息 -- 支付方式使用这个里面的 这个表的pay_type是拆分上面表的pay_type
type CommunicationPaymentInfoType struct {
	source.Model
	common.PaymentInfoData
}

// 主商户详情
type CommunicationMerchantDetail struct {
	source.Model
	common.MerchantDetailResponse
	PaymentStoreCode string `json:"payment_store_code"`
}

// 子商户
type CommunicationSubMer struct {
	source.Model
	common.SubMerListResponse
	SmallShopId                            uint `gorm:"column:small_shop_id;comment:小商店id" json:"small_shop_id"`
	BindOrUnbindSubMerSplitSettlementLogID uint `json:"bind_or_unbind_sub_mer_split_settlement_log_id" gorm:"column:bind_or_unbind_sub_mer_split_settlement_log_id;comment:绑定分账记录id,申请/解绑时绑定"`
	SubMerSplitSettlementLogID             uint `json:"sub_mer_split_settlement_log_id" gorm:"column:sub_mer_split_settlement_log_id;comment:子商户申请修改审核记录id,申请/修改时绑定"`
}

// 子商户详情
type CommunicationSubMerDetail struct {
	source.Model
	common.SubMerDetailResponse
	PaymentStoreCode string `json:"payment_store_code"`
}

// 子商户申请修改审核记录
type SubMerSplitSettlementLog struct {
	source.Model
	common.SubMerSplitSettlement
	Type int `json:"type" gorm:"column:type;default:0"` // 绑定类型，0-开通，1-变更 2 第三方，不传则默认为绑定0
	// AuditStatus 审核状态：0-待审核, 1-审核通过, 2-审核驳回  全部不传这个字段
	AuditStatus int `json:"audit_status" gorm:"column:audit_status;default:0"` // 审核状态：0-待审核, 1-审核通过, 2-审核驳回

	// AuditStatusDesc 审核状态描述
	AuditStatusDesc string `json:"audit_status_desc" gorm:"column:audit_status_desc"` // 审核状态描述
}

type BindOrUnbindSubMerSplitSettlementLog struct {
	source.Model
	common.BindOrUnbindSubMerSplitSettlement
	AuditRemark string `json:"audit_remark" gorm:"column:audit_remark"` // 审核状态说明未通过的具体原因

	// AuditStatus 审核状态：0-待审核, 1-审核通过, 2-审核驳回  全部不传这个字段
	AuditStatus int `json:"audit_status" gorm:"column:audit_status;default:0"` // 审核状态：0-待审核, 1-审核通过, 2-审核驳回

	// AuditStatusDesc 审核状态描述
	AuditStatusDesc string `json:"audit_status_desc" gorm:"column:audit_status_desc"` // 审核状态描述
}

// 分账记录
type SplitShareRecord struct {
	source.Model
	OutSeparateNo          uint                     `json:"out_separate_no"` // 商户分账指令流水号 分账请求号
	MerRegName             string                   `gorm:"column:mer_reg_name;comment:商户注册名称" json:"mer_reg_name"`
	MerchantNo             string                   `gorm:"column:merchant_no;comment:商户号" json:"merchant_no"`
	PaySN                  uint                     `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:中台支付编号,聚合支付支付交易流水号;"` //支付编号
	PayInfoId              uint                     `json:"pay_info_id" gorm:"column:pay_info_id;comment:支付id"`
	OrderSN                uint                     `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:商户订单号-小商店订单号;"`                         //支付编号
	OrderID                uint                     `json:"order_id" form:"order_id" gorm:"column:order_id;comment:商户订单号-小商店订单id;"`                        //支付编号
	PayTypeName            string                   `json:"pay_type_name" gorm:"column:pay_type_name;comment:支付类型名称" json:"pay_type_name"`                 //支付编号
	PayTypeID              int                      `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"` // 订单支付方式
	ChannelType            int                      `json:"channel_type"`                                                                                  //1-lakala，2-汇付，3-乐刷，4-富友
	Channel                string                   `json:"channel"`
	TermNo                 string                   `json:"term_no" form:"term_no" gorm:"column:term_no;comment:终端号"` // 终端号
	Amount                 uint                     `json:"amount" gorm:"column:amount;comment:支付金额"`
	SplitAmount            uint                     `json:"split_amount" gorm:"column:split_amount;comment:分账总金额 第三方返回"`
	IsSplit                int                      `json:"is_split" gorm:"column:is_split;comment:是否已获取到分账金额进行分账完毕0否1是;default:0;"`
	SplitStatus            int                      `json:"split_status" gorm:"column:split_status;comment:分账状态0待处理1处理中2部分处理3处理完成-1处理失败-2请求异常;default:0;type:int(11);"`
	ErrorMsg               string                   `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;type:text;"` //错误内容
	PaymentStoreCode       string                   `json:"payment_store_code" gorm:"column:payment_store_code;comment:支付使用的子商户的聚合支付数据通商户号;"`
	SplitShareRecordAmount []SplitShareRecordAmount `json:"split_share_record_amount"  `
}

func (as *SplitShareRecord) AfterCreate(tx *gorm.DB) (err error) {
	timestamp := uint(as.CreatedAt.Unix())
	outSeparateNo := as.ID + timestamp*110
	err = tx.Model(&as).Update("out_separate_no", outSeparateNo).Error
	return
}

// 分账金额记录表
type SplitShareRecordAmount struct {
	source.Model
	MerchantNo         string `gorm:"column:merchant_no;comment:商户号" json:"merchant_no"`
	PaymentStoreCode   string `json:"payment_store_code" gorm:"column:payment_store_code;comment:聚合支付数据通商户号;"`
	Type               int    `json:"type" form:"type" gorm:"column:type;comment:类型0-商户（平台），1-子商户（商家）"`
	SplitShareRecordId uint   `json:"split_share_record_id" gorm:"column:split_share_record_id;comment:分账记录id"`
	SplitAmount        uint   `json:"split_amount" gorm:"column:split_amount;comment:分账金额"`
	SplitStatus        int    `json:"split_status" gorm:"column:split_status;comment:分账状态0待处理1处理中2已受理3处理完成-1处理失败 ;default:0;type:int(11);"`
	SettlementStatus   int    `json:"settlement_status" gorm:"column:settlement_status;comment:结算状态0待结算1已结算 已结算才可以进行请求分账;default:0;type:int(11);"`
	OutSeparateNo      string `json:"out_separate_no"`                                                        // 商户分账指令流水号 分账请求号
	SplitStatusDesc    string `json:"split_status_desc"`                                                      //分账结果描述第三方返回
	OrderID            uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:商户订单号-小商店订单id;"` //支付编号

	ErrorMsg string `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误内容;type:text;"` //错误内容
}
