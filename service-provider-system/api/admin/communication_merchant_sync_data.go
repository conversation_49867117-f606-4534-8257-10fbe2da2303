package admin

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	pay "payment/model"
	"service-provider-system/common"
	"service-provider-system/request"
	"service-provider-system/service"
	"yz-go/component/log"
	yzResponse "yz-go/response"
)

// GetCommunicationMerchantSyncDataList 获取主商户同步数据列表
func GetCommunicationMerchantSyncDataList(c *gin.Context) {
	var pageInfo request.CommunicationMerchantSyncDataSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetCommunicationMerchantSyncDataList(pageInfo); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

// GetAllCommunicationMerchantSyncData 获取所有主商户同步数据
// @Tags 服务商系统API
// @Summary 获取所有主商户同步数据
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /serviceProviderSystem/getAllCommunicationMerchantSyncData [get]
func GetAllCommunicationPaymentInfo(c *gin.Context) {
	if err, list := service.GetAllCommunicationPaymentInfo(); err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(list, c)
	}
}
func PaymentMerchantSync(c *gin.Context) {
	err := service.PaymentMerchantSync()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("同步中需要1-2分钟请勿重复点击", c)
}

// 清除缓存的支付数据
func ReseatPayStoreCommunicationPaymentInfos(c *gin.Context) {
	pay.ReseatPayStoreCommunicationPaymentInfos()

	yzResponse.OkWithMessage("成功", c)
}

// 聚合支付主商家基础信息
func GetPaymentBase(c *gin.Context) {
	err, rd := common.Initial()
	if err != nil {
		return
	}
	data, err := rd.GetPaymentBase()
	if err != nil {
		log.Log().Error("聚合支付主商家基础信息", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(data, c)
}
