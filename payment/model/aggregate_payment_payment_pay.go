package model

import (
	"gorm.io/gorm"
	"strings"
	"yz-go/config"
	"yz-go/source"
)

// 聚合支付主商家基础信息 -- 包含余额 以及支付方式使用这个里面的
type CommunicationPaymentInfoType struct {
	source.Model
	Balance          float64 `json:"balance"`
	StoreName        string  `json:"store_name"`
	ChannelType      int     `json:"channel_type"`
	Channel          string  `json:"channel"`
	PaymentStoreCode string  `json:"payment_store_code"`
	StoreCode        string  `json:"store_code"`
	PayType          string  `json:"pay_type"`
	WalletID         string  `json:"wallet_id"`
	SplitStatus      int     `json:"split_status"`
	PayTypeName      string  `json:"pay_type_name" gorm:"-"`
}

func (o *CommunicationPaymentInfoType) AfterFind(tx *gorm.DB) (err error) {
	o.PayTypeName = GetPaymentChannelPayTypeName(PaymentChannel(o.PayType))

	return
}

// PaymentChannel 支付渠道
type PaymentChannel string

var PayStoreCommunicationPaymentInfoTypes []CommunicationPaymentInfoType          //平台 这里设置指定商户所有可用的支付方式，当设置改变商户这里会改变 每次同步这里也会改变--如果没改变就是忘记写了
var SmallShopPayStoreCommunicationPaymentInfoTypes []CommunicationPaymentInfoType //小商店 这里设置指定商户所有可用的支付方式，当设置改变商户这里会改变 每次同步这里也会改变--如果没改变就是忘记写了
var SplitChannelType = []int{1, 2, 5}                                             //支持分账的通道
var SplitChannelTypeCreateSurMer = []int{1, 2}                                    //支持分账的通道-- 支持创建子商户的

const (
	// 基础支付方式
	WECHAT       PaymentChannel = "WECHAT"       // 微信支付
	ALIPAY       PaymentChannel = "ALIPAY"       // 支付宝支付
	CASH         PaymentChannel = "CASH"         // 拉卡拉收银台
	QUICKPAY     PaymentChannel = "QUICKPAY"     // 快捷支付
	MINIAPP_PAY  PaymentChannel = "MINIAPP_PAY"  // 小程序终端支付
	AGGR_CASHIER PaymentChannel = "AGGR_CASHIER" // 聚合收银台
)

var PayTypeNameMap = map[PaymentChannel]string{
	WECHAT:       "微信支付",  // 微信支付
	ALIPAY:       "支付宝",   // 支付宝支付
	CASH:         "拉卡拉",   // 拉卡拉收银台
	QUICKPAY:     "快捷",    // 快捷支付
	MINIAPP_PAY:  "小程序终端", // 小程序终端支付
	AGGR_CASHIER: "聚合收银台", // 聚合收银台
}

func GetPaymentChannelPayTypeName(status PaymentChannel) (statusName string) {
	var ok bool
	statusName, ok = PayTypeNameMap[status]
	if !ok {
		statusName = "未知支付方式"
	}
	return
}

// PaymentScene 支付场景
type PaymentScene string

const (
	SCENE_H5          PaymentScene = "H5"          // H5场景
	SCENE_PC          PaymentScene = "PC"          // PC场景
	SCENE_WECHAT_MINI PaymentScene = "WECHAT_MINI" // 微信小程序
	SCENE_ALIPAY_MINI PaymentScene = "ALIPAY_MINI" // 支付宝小程序
)

// 支付方式支持的场景映射
var PayTypeScenes = map[PaymentChannel][]PaymentScene{
	WECHAT:       {SCENE_H5, SCENE_PC, SCENE_WECHAT_MINI},
	ALIPAY:       {SCENE_H5, SCENE_PC, SCENE_ALIPAY_MINI},
	QUICKPAY:     {SCENE_H5, SCENE_PC},
	MINIAPP_PAY:  {SCENE_WECHAT_MINI},
	CASH:         {SCENE_H5, SCENE_WECHAT_MINI},
	AGGR_CASHIER: {SCENE_H5, SCENE_WECHAT_MINI},
}

// IsSceneSupported 检查支付方式字符串是否支持指定场景
func IsSceneSupported(payTypes string, scene PaymentScene) bool {

	// 分割并遍历支付方式
	for _, payType := range strings.Split(payTypes, ",") {
		payType = strings.TrimSpace(payType)
		// 检查该支付方式是否支持指定场景
		if scenes, exists := PayTypeScenes[PaymentChannel(payType)]; exists {
			for _, s := range scenes {
				if s == scene {
					return true
				}
			}
		}
	}
	return false
}

// IsSceneSupportedPayType 检查支付方式字符串是否支持指定场景 返回支付具体类型
// types 0全部 1排除微信 2仅匹配微信
func IsSceneSupportedPayType(payTypes string, scene PaymentScene, types int) (resPayType string) {
	// 分割并遍历支付方式
	for _, payType := range strings.Split(payTypes, ",") {
		payType = strings.TrimSpace(payType)
		//存在不支持微信的情况这时候不取微信
		if types == 1 && payType == "WECHAT" {
			continue
		}
		if types == 2 && payType != "WECHAT" {
			continue
		}
		// 检查该支付方式是否支持指定场景
		if scenes, exists := PayTypeScenes[PaymentChannel(payType)]; exists {
			for _, s := range scenes {
				if s == scene {
					resPayType = payType
					return
				}
			}
		}
	}
	return
}

// 同步或者设置用户之后重置全局变量保存的支付方式
func ReseatPayStoreCommunicationPaymentInfos() {
	PayStoreCommunicationPaymentInfoTypes = nil
	SmallShopPayStoreCommunicationPaymentInfoTypes = nil
}

// 平台支付方式
func GetPayStoreCommunicationPaymentInfos() (data []CommunicationPaymentInfoType) {
	//如果未开启则直接返回,如果没有指定商户直接返回
	if config.Config().AggregatedPayment.IsOpen == 0 || config.Config().AggregatedPayment.PaymentStoreCode == "" {
		return
	}
	//如果存在指定商户且全局支付方式是空则先获取在返回
	if config.Config().AggregatedPayment.PaymentStoreCode != "" && PayStoreCommunicationPaymentInfoTypes == nil {
		source.DB().Where("payment_store_code=?", config.Config().AggregatedPayment.PaymentStoreCode).Find(&PayStoreCommunicationPaymentInfoTypes)
	}
	return PayStoreCommunicationPaymentInfoTypes
}

// 小商店支付方式
func GetSmallShopPayStoreCommunicationPaymentInfos() (data []CommunicationPaymentInfoType) {
	err, smallShopAggregatedPaymentValueData := GetSmallShopAggregatedPaymentValue()
	if err != nil {
		return
	}
	//如果未开启则直接返回,如果没有指定商户直接返回
	if smallShopAggregatedPaymentValueData.SmallShopIsOpen == 0 || smallShopAggregatedPaymentValueData.SmallShopPaymentStoreCode == "" {
		return
	}
	//如果存在指定商户且全局支付方式是空则先获取在返回
	if smallShopAggregatedPaymentValueData.SmallShopPaymentStoreCode != "" && SmallShopPayStoreCommunicationPaymentInfoTypes == nil {
		source.DB().Where("payment_store_code=?", smallShopAggregatedPaymentValueData.SmallShopPaymentStoreCode).Find(&SmallShopPayStoreCommunicationPaymentInfoTypes)
	}
	return SmallShopPayStoreCommunicationPaymentInfoTypes
}

// 获取支持h5 pc 微信小程序支持的支付方式  types = PaymentScene
func GetCommunicationPaymentInfoData(types PaymentScene) (communicationPaymentInfo []CommunicationPaymentInfoType) {
	//先验证PayStoreCommunicationPaymentInfos 是否有数据
	GetPayStoreCommunicationPaymentInfos()
	if len(PayStoreCommunicationPaymentInfoTypes) == 0 {
		return
	}
	//获取支持的支付方式
	for _, item := range PayStoreCommunicationPaymentInfoTypes {
		if IsSceneSupported(item.PayType, types) {
			communicationPaymentInfo = append(communicationPaymentInfo, item)
		}
	}
	return
}

// 获取小商店支付方式
func GetSmallShopCommunicationPaymentInfoData(types PaymentScene) (communicationPaymentInfo []CommunicationPaymentInfoType) {
	//先验证PayStoreCommunicationPaymentInfos 是否有数据
	GetSmallShopPayStoreCommunicationPaymentInfos()
	if len(SmallShopPayStoreCommunicationPaymentInfoTypes) == 0 {
		return
	}
	//获取支持的支付方式
	for _, item := range SmallShopPayStoreCommunicationPaymentInfoTypes {
		if IsSceneSupported(item.PayType, types) {
			communicationPaymentInfo = append(communicationPaymentInfo, item)
		}
	}
	return
}
