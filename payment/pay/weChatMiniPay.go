package Pay

import (
	"context"
	"encoding/json"
	"errors"
	"github.com/wechatpay-apiv3/wechatpay-go/core"
	"github.com/wechatpay-apiv3/wechatpay-go/core/option"
	"github.com/wechatpay-apiv3/wechatpay-go/services/payments/jsapi"
	"github.com/wechatpay-apiv3/wechatpay-go/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"io/ioutil"
	"payment/model"
	model3 "payment/model"
	"payment/request"
	"payment/service"
	"payment/setting"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	utils2 "yz-go/utils"
)

type WxMiniPay struct {
	ConvergenceWeChatPayment  interface{}
	ConvergenceWeChatRefund   interface{}
	ConvergenceWeChatRecharge interface{}
}

func (weChat *WxMiniPay) SplitAccount() (err error) {

	return
}

func (weChat *WxMiniPay) Increase() (err error) {
	return
}

func (weChat *WxMiniPay) AfterOperation() (err error) {
	return
}

type SmallShopOrderPayInfo struct {
	ID                 uint `json:"id" form:"id" gorm:"primarykey"`
	SmallShopOrderID   uint `json:"small_shop_order_id"`
	SmallShopPayInfoID uint `json:"small_shop_pay_info_id"`
}

type SmallShopOrder struct {
	ID          uint `json:"id" form:"id" gorm:"primarykey"`
	Status      int8 `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;index;"`
	SmallShopID uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;default:0;comment:小商店id;"`
}

func (weChat *WxMiniPay) BeforeOperation() (err error) {

	paymentData := weChat.ConvergenceWeChatPayment.(request.WeChatPayRequestData)

	if paymentData.PayCode == 1 {
		var orderPayInfo SmallShopOrderPayInfo
		err = source.DB().Where("small_shop_pay_info_id=?", paymentData.PayInfoID).First(&orderPayInfo).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("未查询到支付单")
			return
		}
		var order SmallShopOrder
		err = source.DB().Where("id = ?", orderPayInfo.SmallShopOrderID).First(&order).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("未查询到支付单")
			return
		}
		if order.Status > 0 {
			err = errors.New("该订单已支付,请勿重复支付")
			return
		}
	} else {
		var orderPayInfos []model.OrderPayInfo
		err = source.DB().Where("pay_info_id=?", paymentData.PayInfoID).Find(&orderPayInfos).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("未查询到支付单")
			return
		}
		for _, orderPayInfo := range orderPayInfos {
			var order model.Order
			err = source.DB().Where("id=?", orderPayInfo.OrderID).First(&order).Error
			if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
				err = errors.New("未查询到支付单")
				return
			}
			if order.Status > 0 {
				err = errors.New("该订单已支付,请勿重复支付")
				return
			}
			if order.Status == -1 {
				err = errors.New("该订单已关闭,无法支付")
				return
			}
		}
	}

	return
}

func (weChat *WxMiniPay) Init() (err error, resData interface{}) {
	PayTypeList = append(PayTypeList, Type{Name: "微信小程序", Code: model.WXMINICODE, Status: 1, Mode: 1})
	return
}

type SmallShopPayInfo struct {
	ID              uint `json:"id" form:"id" gorm:"primarykey"`
	PaySN           uint `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;"`           // 编号
	Amount          uint `json:"amount" form:"amount" gorm:"column:amount;comment:支付总金额(分);"` // 支付总金额
	SmallShopUserID uint `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`      // 小商店用户id
}

type SmallShopUser struct {
	ID           uint   `json:"id" form:"id" gorm:"primarykey"`
	WxMiniOpenid string `json:"wx_mini_openid" form:"wx_mini_openid" gorm:"column:wx_mini_openid;comment:微信小程序openid;type:varchar(255);size:255;"`
	WxOpenid     string `json:"wx_openid" form:"wx_openid" gorm:"column:wx_openid;comment:微信openid;type:varchar(255);size:255;"`
}

type SmallShopOrderItem struct {
	ID               uint   `json:"id" form:"id" gorm:"primarykey"`
	SmallShopOrderID uint   `json:"small_shop_order_id" form:"small_shop_order_id" gorm:"column:small_shop_order_id;comment:订单id;"` // 小商店订单id
	Title            string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);size:100;"`                  // 名
	SkuTitle         string `json:"sku_title" form:"sku_title" gorm:"column:sku_title;comment:sku标题;type:varchar(100);size:100;"`   // 规格名
}

func wxMiniSmallShopPayment(payment request.WeChatPayRequestData) (err error, resData interface{}) {
	var payInfo SmallShopPayInfo
	err = source.DB().Where("id=?", payment.PayInfoID).First(&payInfo).Error
	if err != nil {
		log.Log().Error("查询支付单失败", zap.Any("err", err.Error()))
		err = errors.New("查询支付单失败" + err.Error())
		return
	}
	//应用id  商户号  商户证书序列号 商户APIv3密钥  私钥证书
	err, settingMini := setting.GetMiniBySmallShop()
	if err != nil {
		err = errors.New("配置获取失败")
		return
	}
	var (
		mchID                      = settingMini.Mchid                      // 商户号
		mchCertificateSerialNumber = settingMini.MchCertificateSerialNumber // 商户证书序列号
		mchAPIv3Key                = settingMini.PayKey                     // 商户APIv3密钥
	)
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath("./data/goSupply/" + settingMini.KeyPath)
	if err != nil {
		err = errors.New("证书不存在" + err.Error())
		return
	}
	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		err = errors.New("new wechat pay client err" + err.Error())
		return
	}
	config.Config().Local.Host, err = service.LocUrl()
	if err != nil {
		return
	}
	var user SmallShopUser
	err = source.DB().Where("id = ?", payInfo.SmallShopUserID).First(&user).Error
	if err != nil {
		err = errors.New("获取用户信息失败" + err.Error())
		return
	}
	if user.WxMiniOpenid == "" {
		err = errors.New("用户没有绑定微信小程序标识")
		return
	}
	var weChatNotify request.WeChatNotify
	weChatNotify.PaySN = payInfo.PaySN
	weChatNotify.PayType = model.WXMINICODE
	attach, err := json.Marshal(weChatNotify)
	if err != nil {
		err = errors.New("数组转json失败" + err.Error())
		return
	}
	var orderList SmallShopOrderPayInfo
	err = source.DB().Where("small_shop_pay_info_id =  ?", payInfo.ID).First(&orderList).Error
	if err != nil {
		log.Log().Error("微信支付获取商品信息错误!", zap.Any("info", err))
		err = errors.New("微信支付获取商品信息错误" + err.Error())
		return
	}
	var orderItems []SmallShopOrderItem
	err = source.DB().Where("small_shop_order_id = ?", orderList.SmallShopOrderID).Find(&orderItems).Error
	if err != nil {
		log.Log().Error("微信支付获取商品信息错误1!", zap.Any("info", err))
		err = errors.New("微信支付获取商品信息错误1" + err.Error())
		return
	}
	var description string
	for _, item := range orderItems {
		description += item.Title + "-" + item.SkuTitle
	}
	if len(description) > 100 {
		description = string(description[:100]) //保持描述120个字符
	}
	var notifyUrl = config.Config().Local.Host + "/supplyapi/api/smallShop/finance/wechatPayMiniNotify"
	svc := jsapi.JsapiApiService{Client: client}
	resData, result, err := svc.PrepayWithRequestPayment(ctx, jsapi.PrepayRequest{
		Appid:       core.String(settingMini.AppId),
		Mchid:       core.String(mchID),
		Description: core.String(description),
		OutTradeNo:  core.String(strconv.Itoa(int(payInfo.PaySN))),
		TimeExpire:  core.Time(time.Now()),
		Attach:      core.String(string(attach)),
		NotifyUrl:   core.String(notifyUrl),
		Amount: &jsapi.Amount{
			Currency: core.String("CNY"),
			Total:    core.Int64(int64(payInfo.Amount)),
		},
		Payer: &jsapi.Payer{
			Openid: core.String(user.WxMiniOpenid),
		},
	})
	if err != nil {
		var apiError core.APIError

		res, _ := ioutil.ReadAll(result.Response.Body)
		_ = json.Unmarshal(res, &apiError)

		err = errors.New(apiError.Message)
		return
	}
	return
}

func wxMiniPayment(payment request.WeChatPayRequestData) (err error, resData interface{}) {
	var payInfo model.PayInfo
	err = source.DB().Where("id=?", payment.PayInfoID).First(&payInfo).Error
	if err != nil {
		log.Log().Error("查询支付单失败", zap.Any("err", err.Error()))
		err = errors.New("查询支付单失败" + err.Error())
		return
	}
	//应用id  商户号  商户证书序列号 商户APIv3密钥  私钥证书
	err, settingMini := setting.GetMini()
	if err != nil {
		err = errors.New("配置获取失败")
		return
	}

	var (
		mchID                      = settingMini.Mchid                      // 商户号
		mchCertificateSerialNumber = settingMini.MchCertificateSerialNumber // 商户证书序列号
		mchAPIv3Key                = settingMini.PayKey                     // 商户APIv3密钥
	)
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath("./data/goSupply/" + settingMini.KeyPath)
	if err != nil {
		err = errors.New("证书不存在" + err.Error())
		return
		//log.Print("load merchant private key error")
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		err = errors.New("new wechat pay client err" + err.Error())
		return
	}

	config.Config().Local.Host, err = service.LocUrl()
	if err != nil {
		return
	}
	var user model3.User
	err = source.DB().Where("id = ?", payInfo.UserID).First(&user).Error
	if err != nil {
		err = errors.New("获取用户信息失败" + err.Error())
		return
	}
	if user.WxMiniOpenid == "" {
		err = errors.New("用户没有绑定微信小程序标识")
		return
	}
	var weChatNotify request.WeChatNotify
	weChatNotify.PaySN = payInfo.PaySN
	weChatNotify.PayType = model.WXMINICODE
	attach, err := json.Marshal(weChatNotify)
	if err != nil {
		err = errors.New("数组转json失败" + err.Error())
		return
	}
	var orderList model.OrderPayInfo
	err = source.DB().Where("pay_info_id =  ?", payInfo.ID).First(&orderList).Error
	if err != nil {
		log.Log().Error("微信支付获取商品信息错误!", zap.Any("info", err))
		err = errors.New("微信支付获取商品信息错误" + err.Error())
		return
	}
	var orderItems []model.OrderItem
	err = source.DB().Where("order_id = ?", orderList.OrderID).Find(&orderItems).Error
	if err != nil {
		log.Log().Error("微信支付获取商品信息错误1!", zap.Any("info", err))
		err = errors.New("微信支付获取商品信息错误1" + err.Error())
		return
	}
	var description string
	for _, item := range orderItems {
		description += item.Title + "-" + item.SkuTitle
	}
	if len(description) > 100 {
		description = string(description[:100]) //保持描述120个字符
	}
	var notifyUrl = config.Config().Local.Host + "/supplyapi/api/finance/wechatPayMiniNotify"
	svc := jsapi.JsapiApiService{Client: client}
	resData, result, err := svc.PrepayWithRequestPayment(ctx, jsapi.PrepayRequest{
		Appid:       core.String(settingMini.AppId),
		Mchid:       core.String(mchID),
		Description: core.String(description),
		OutTradeNo:  core.String(strconv.Itoa(int(payInfo.PaySN))),
		TimeExpire:  core.Time(time.Now()),
		Attach:      core.String(string(attach)),
		NotifyUrl:   core.String(notifyUrl),
		Amount: &jsapi.Amount{
			Currency: core.String("CNY"),
			Total:    core.Int64(int64(payInfo.Amount)),
		},
		Payer: &jsapi.Payer{
			Openid: core.String(user.WxMiniOpenid),
		},
	})
	if err != nil {
		var apiError core.APIError

		res, _ := ioutil.ReadAll(result.Response.Body)
		_ = json.Unmarshal(res, &apiError)

		err = errors.New(apiError.Message)
		return
	}
	return
}

func (weChat *WxMiniPay) Payment() (err error, resData interface{}) {
	err = weChat.BeforeOperation()
	if err != nil {
		return
	}
	payment := weChat.ConvergenceWeChatPayment.(request.WeChatPayRequestData)

	if payment.PayCode == 1 {

	} else {
		err, resData = wxMiniPayment(payment)
	}

	return
}

func (weChat *WxMiniPay) Refund() (err error, resData interface{}) {
	refund := weChat.ConvergenceWeChatRefund.(request.Refund)

	err = service.WechatRefund(refund, 2)
	return
}

func (weChat *WxMiniPay) Recharge() (err error, resData interface{}) {
	paymentData := weChat.ConvergenceWeChatRecharge.(request.WeChatRechargeRequestData)
	if paymentData.PayType <= 0 {
		err = errors.New("支付类型不能为空")
		return
	}
	var paySN = strconv.Itoa(int(paymentData.Uid)) + strconv.Itoa(int(time.Now().Unix()))
	var count int64
	source.DB().Model(model.UserTopUp{}).Debug().Where("pay_sn", paySN).Count(&count)
	if count > 0 {
		err = errors.New("请勿使用重复支付单")
		return
	}
	var UserTopUp model3.RechargeBalance
	UserTopUp.Amount = paymentData.Amount
	UserTopUp.PayType = paymentData.PayType
	UserTopUp.PaySN = utils2.StrToUInt(paySN)
	UserTopUp.Uid = paymentData.Uid
	err = source.DB().Create(&UserTopUp).Error
	if err != nil {
		return
	}
	//应用id  商户号  商户证书序列号 商户APIv3密钥  私钥证书
	err, settingMini := setting.GetMini()
	if err != nil {
		err = errors.New("配置获取失败")
		return
	}

	var (
		mchID                      = settingMini.Mchid                      // 商户号
		mchCertificateSerialNumber = settingMini.MchCertificateSerialNumber // 商户证书序列号
		mchAPIv3Key                = settingMini.PayKey                     // 商户APIv3密钥
	)
	// 使用 utils 提供的函数从本地文件中加载商户私钥，商户私钥会用来生成请求的签名
	mchPrivateKey, err := utils.LoadPrivateKeyWithPath("./data/goSupply/" + settingMini.KeyPath)
	if err != nil {
		err = errors.New("证书不存在" + err.Error())
		return
		//log.Print("load merchant private key error")
	}

	ctx := context.Background()
	// 使用商户私钥等初始化 client，并使它具有自动定时获取微信支付平台证书的能力
	opts := []core.ClientOption{
		option.WithWechatPayAutoAuthCipher(mchID, mchCertificateSerialNumber, mchPrivateKey, mchAPIv3Key),
	}
	client, err := core.NewClient(ctx, opts...)
	if err != nil {
		err = errors.New("new wechat pay client err" + err.Error())
		return
	}

	config.Config().Local.Host, err = service.LocUrl()
	if err != nil {
		return
	}
	var user model3.User
	err = source.DB().Where("id = ?", paymentData.Uid).First(&user).Error
	if err != nil {
		err = errors.New("获取用户信息失败" + err.Error())
		return
	}
	if user.WxMiniOpenid == "" {
		err = errors.New("用户没有绑定微信小程序标识")
		return
	}
	//设置额外信息 支付通知会请求返回
	var weChatNotify request.WeChatNotify
	weChatNotify.PaySN = UserTopUp.PaySN
	weChatNotify.PayType = paymentData.PayType
	weChatNotify.Type = 1

	attach, err := json.Marshal(weChatNotify)
	if err != nil {
		err = errors.New("数组转json失败" + err.Error())
		return
	}
	var description = "微信充值" + utils2.Fen2Yuan(paymentData.Amount)
	var notifyUrl = config.Config().Local.Host + "/supplyapi/api/finance/wechatPayNotify"
	svc := jsapi.JsapiApiService{Client: client}
	resData, result, err := svc.PrepayWithRequestPayment(ctx, jsapi.PrepayRequest{
		Appid:       core.String(settingMini.AppId),
		Mchid:       core.String(mchID),
		Description: core.String(description),
		OutTradeNo:  core.String(strconv.Itoa(int(weChatNotify.PaySN))),
		TimeExpire:  core.Time(time.Now()),
		Attach:      core.String(string(attach)),
		NotifyUrl:   core.String(notifyUrl),
		Amount: &jsapi.Amount{
			Currency: core.String("CNY"),
			Total:    core.Int64(int64(paymentData.Amount)),
		},
		Payer: &jsapi.Payer{
			Openid: core.String(user.WxMiniOpenid),
		},
	})
	if err != nil {
		var apiError core.APIError

		res, _ := ioutil.ReadAll(result.Response.Body)
		_ = json.Unmarshal(res, &apiError)

		err = errors.New(apiError.Message)
		return
	}

	return
}

func (weChat *WxMiniPay) Deduction() (err error, resData interface{}) {
	return
}

func (weChat *WxMiniPay) GetBalance() (err error, resData interface{}) {
	return
}

func (weChat *WxMiniPay) Settlement() (err error) {
	return
}
