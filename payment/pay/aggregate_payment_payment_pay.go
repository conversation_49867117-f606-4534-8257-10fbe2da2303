package Pay

import (
	"database/sql/driver"
	"encoding/json"
	"errors"
	"fmt"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"payment/model"
	model3 "payment/model"
	"payment/request"
	"payment/service"
	serviceProviderSystemCommon "service-provider-system/common"
	serviceProviderSystemModel "service-provider-system/model"
	shopSetting "shop/setting"
	"strconv"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	yzgoModel "yz-go/model"
	"yz-go/source"
	utils2 "yz-go/utils"
)

type AggregatePaymentPay struct {
	ConvergenceWeChatPayment  interface{}
	ConvergenceWeChatRefund   interface{}
	ConvergenceWeChatRecharge interface{}
}

func (weChat *AggregatePaymentPay) SplitAccount() (err error) {

	return
}

func (weChat *AggregatePaymentPay) Increase() (err error) {
	return
}

func (weChat *AggregatePaymentPay) AfterOperation() (err error) {
	return
}

func (weChat *AggregatePaymentPay) BeforeOperation() (err error) {

	paymentData := weChat.ConvergenceWeChatPayment.(request.AggregatePaymentPayRequestData)

	if paymentData.PayCode == 1 {
		var orderPayInfos []SmallShopOrderPayInfo
		err = source.DB().Where("small_shop_pay_info_id=?", paymentData.PayInfoID).First(&orderPayInfos).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("未查询到支付单")
			return
		}
		for _, orderPayInfo := range orderPayInfos {
			var order SmallShopOrder

			err = source.DB().Where("id = ?", orderPayInfo.SmallShopOrderID).First(&order).Error
			if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
				err = errors.New("未查询到支付单")
				return
			}
			if order.Status > 0 {
				err = errors.New("该订单已支付,请勿重复支付")
				return
			}
			if order.Status == -1 {
				err = errors.New("该订单已关闭,无法支付")
				return
			}
		}

	} else {
		var orderPayInfos []model.OrderPayInfo
		err = source.DB().Where("pay_info_id=?", paymentData.PayInfoID).Find(&orderPayInfos).Error
		if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("未查询到支付单")
			return
		}
		for _, orderPayInfo := range orderPayInfos {
			var order model.Order
			err = source.DB().Where("id=?", orderPayInfo.OrderID).First(&order).Error
			if err != nil && errors.Is(err, gorm.ErrRecordNotFound) {
				err = errors.New("未查询到支付单")
				return
			}
			if order.Status > 0 {
				err = errors.New("该订单已支付,请勿重复支付")
				return
			}
			if order.Status == -1 {
				err = errors.New("该订单已关闭,无法支付")
				return
			}
		}
	}
	return
}

func (weChat *AggregatePaymentPay) Init() (err error, resData interface{}) {
	return
}

func (weChat *AggregatePaymentPay) Payment() (err error, resData interface{}) {
	err = weChat.BeforeOperation()
	if err != nil {
		return
	}
	payment := weChat.ConvergenceWeChatPayment.(request.AggregatePaymentPayRequestData)
	var payInfo model.PayInfo
	var user model3.User
	var description string
	var paymentStoreCode string //基础设置 设置的支付商户 -- 分小商店和平台
	var smallShopOrderID uint   //小商店的订单id 仅小商店支付才有
	//区分小商店小程序和平台小程序
	if payment.PayCode == 1 {
		var payInfo1 SmallShopPayInfo
		err = source.DB().Where("id=?", payment.PayInfoID).First(&payInfo1).Error
		if err != nil {
			log.Log().Error("查询支付单失败", zap.Any("err", err.Error()))
			err = errors.New("查询支付单失败" + err.Error())
			return
		}
		//赋值下面需要使用
		payInfo.ID = payInfo1.ID
		payInfo.PaySN = payInfo1.PaySN
		payInfo.Amount = payInfo1.Amount
		payInfo.UserID = payInfo1.SmallShopUserID

		var user1 SmallShopUser
		err = source.DB().Where("id = ?", payInfo1.SmallShopUserID).First(&user1).Error
		if err != nil {
			err = errors.New("获取用户信息失败" + err.Error())
			return
		}
		//赋值下面统一判断是否有标识
		user.WxMiniOpenid = user1.WxMiniOpenid
		user.WxOpenid = user1.WxOpenid

		user.ID = user1.ID

		var orderList SmallShopOrderPayInfo
		err = source.DB().Where("small_shop_pay_info_id =  ?", payInfo.ID).First(&orderList).Error
		if err != nil {
			log.Log().Error("聚合支付获取商品信息错误!", zap.Any("info", err))
			err = errors.New("聚合支付获取商品信息错误" + err.Error())
			return
		}
		smallShopOrderID = orderList.SmallShopOrderID
		var orderItems []SmallShopOrderItem
		err = source.DB().Where("small_shop_order_id = ?", orderList.SmallShopOrderID).Find(&orderItems).Error
		if err != nil {
			log.Log().Error("聚合支付获取商品信息错误1!", zap.Any("info", err))
			err = errors.New("聚合支付获取商品信息错误1" + err.Error())
			return
		}
		for _, item := range orderItems {
			description += item.Title + "-" + item.SkuTitle
		}
		if len(description) > 100 {
			description = string(description[:100]) //保持描述120个字符
		}
		var smallShopAggregatedPaymentValue model3.SmallShopAggregatedPaymentValue
		err, smallShopAggregatedPaymentValue = model3.GetSmallShopAggregatedPaymentValue()
		if err != nil {
			err = errors.New("聚合支付获取支付配置失败" + err.Error())
			return
		}
		paymentStoreCode = smallShopAggregatedPaymentValue.SmallShopPaymentStoreCode
	} else {
		var payInfo1 model.PayInfo
		err = source.DB().Where("id=?", payment.PayInfoID).First(&payInfo1).Error
		if err != nil {
			log.Log().Error("查询支付单失败", zap.Any("err", err.Error()))
			err = errors.New("查询支付单失败" + err.Error())
			return
		}
		var user1 model3.User
		user1.WxOpenid = payment.WxOpenid
		//如果是登录支付才查询会员信息
		if payment.IsLogin == 0 {
			err = source.DB().Where("id = ?", payInfo1.UserID).First(&user1).Error
			if err != nil {
				err = errors.New("获取用户信息失败" + err.Error())
				return
			}
		}
		payInfo = payInfo1
		user = user1
		var orderList model.OrderPayInfo
		err = source.DB().Where("pay_info_id =  ?", payInfo.ID).First(&orderList).Error
		if err != nil {
			log.Log().Error("聚合支付获取商品信息错误!", zap.Any("info", err))
			err = errors.New("聚合支付获取商品信息错误" + err.Error())
			return
		}
		var orderItems []model.OrderItem
		err = source.DB().Where("order_id = ?", orderList.OrderID).Find(&orderItems).Error
		if err != nil {
			log.Log().Error("聚合支付获取商品信息错误1!", zap.Any("info", err))
			err = errors.New("聚合支付获取商品信息错误1" + err.Error())
			return
		}
		for _, item := range orderItems {
			description += item.Title + "-" + item.SkuTitle
		}
		if len(description) > 100 {
			description = string(description[:100]) //保持描述120个字符
		}
		paymentStoreCode = config.Config().AggregatedPayment.PaymentStoreCode
	}

	config.Config().Local.Host, err = service.LocUrl()
	if err != nil {
		return
	}
	//config.Config().Local.Host = "https://40e0e2dd.cpolar.cn/"

	//设置额外信息 支付通知会请求返回
	var weChatNotify serviceProviderSystemCommon.Attach
	weChatNotify.PaySN = payInfo.PaySN
	weChatNotify.PayType = payment.PayType
	weChatNotify.PayCode = payment.PayCode
	weChatNotify.WxOpenid = payment.WxOpenid
	var communicationPaymentInfo model.CommunicationPaymentInfoType
	err = source.DB().Where("id = ?", payment.PayType-model.AGGREGATE_PAYMENT).First(&communicationPaymentInfo).Error
	if err != nil {
		err = errors.New("获取聚合支付失败" + err.Error())
		return
	}
	if paymentStoreCode != communicationPaymentInfo.PaymentStoreCode {
		err = errors.New("支付商户与支付方式商户不一致,联系客服更新设置")
		return
	}

	// 回调url
	var notifyUrl = config.Config().Local.Host + "/supplyapi/api/finance/aggregatePaymentPayNotify"

	//settle_type 结算类型没有分账不用传
	paymentRequest := serviceProviderSystemCommon.PaymentRequest{
		PaymentStoreCode: communicationPaymentInfo.PaymentStoreCode,
		OutTradeNo:       strconv.Itoa(int(payInfo.PaySN)),
		SpbillCreateIP:   payment.Ip,
		//PayType:          communicationPaymentInfo.PayType,
		TotalAmount: strconv.Itoa(int(payInfo.Amount)), // 单位分
		Subject:     description,
		NotifyURL:   notifyUrl,
		OrderType:   3,
		RequestType: payment.Type,
		UserIP:      payment.Ip,
		PayScene:    "offline",
	}
	_, splitSettlementSettingData := GetWxSetting("aggregated_payment_split_settlement")
	var isSplit = false
	for _, item := range model3.SplitChannelType {
		if communicationPaymentInfo.ChannelType == item {
			isSplit = true
			break
		}
	}
	log.Log().Info("聚合支付", zap.Any("isSplit", isSplit), zap.Any("isSplit", payment.PayCode), zap.Any("IsOpen", splitSettlementSettingData.Value.IsOpen), zap.Any("isSplit", isSplit))
	//判断小商店支付,并且聚合分账插件开启 并且支付方式为拉卡拉 或者汇付
	if payment.PayCode == 1 && splitSettlementSettingData.Value.IsOpen == 1 && isSplit == true {

		var order SmallShopOrder
		err = source.DB().Where("id = ?", smallShopOrderID).First(&order).Error
		if err != nil {
			err = errors.New("未查询到支付单" + err.Error())
			return
		}
		//判断小商店是否绑定子商户，如果绑定 本次支付变为分账结算，并且支付商户变为子商户
		var communicationSubMer serviceProviderSystemModel.CommunicationSubMer
		err = source.DB().Model(&serviceProviderSystemModel.CommunicationSubMer{}).Where("parent_store_code = ?", paymentStoreCode).Where("small_shop_id = ?", order.SmallShopID).First(&communicationSubMer).Error

		//判断如果是非空的报错就返回不让进行支付
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			err = errors.New("聚合支付查询子商户失败" + err.Error())
			return
		}

		if communicationSubMer.ID > 0 {
			isSplit = false
			for _, item := range model3.SplitChannelType {
				if communicationSubMer.ChannelType == item {
					isSplit = true
					break
				}
			}
			if isSplit == false {
				err = errors.New("聚合支付-该子商户不支持分账")
				return
			}
			paymentStoreCode = communicationSubMer.PaymentStoreCode
			paymentRequest.SettleType = "1"
			weChatNotify.ProfitSharingStatus = 1
		} else {
			log.Log().Debug("小商店未绑定子商户不进行聚合支付分账", zap.Any("PaySN", payInfo.PaySN))
		}
	}
	var wxsetting SysSetting
	///P:PC页面版(仅快捷支付支持)，M:H5页面版，W:微信小程序，Z：支付宝小程序，示例值：M
	//如果是微信支付需要这个
	//PayInfo: PayInfo{
	//	AppID:  "wxabc123123",
	//	OpenID: "oIYWg5-YAL9gBh3YzMHcmM9uo7gg",
	//},
	//M-H5
	paymentRequest.PayInfo.MemberID = strconv.Itoa(int(payInfo.UserID))
	typeString := communicationPaymentInfo.PayType
	paymentRequest.Attach = weChatNotify

	switch payment.Type {
	case "P":

		break
	case "M":
		if communicationPaymentInfo.PayType == "WECHAT" {
			if user.WxOpenid == "" {
				err = errors.New("没有微信标识,无法使用微信支付")
				return
			}
			paymentRequest.PayInfo.OpenID = user.WxOpenid
			var key = "wechatofficial_setting"
			//如果是小商店获取小商店的小程序key
			if payment.PayCode == 1 {
				key = "small_shop_wechat_payment_setting"
			}
			err, wxsetting = GetWxSetting(key)
			if err != nil {
				err = errors.New("获取微信公众号配置失败")
				return
			}
			paymentRequest.PayInfo.AppID = wxsetting.Value.AppId
		}
		break
	case "W":
		if communicationPaymentInfo.PayType == "WECHAT" || communicationPaymentInfo.PayType == "MINIAPP_PAY" {
			if user.WxMiniOpenid == "" {
				err = errors.New("没有微信小程序标识,无法使用小程序支付")
				return
			}
			var key = "wechatmini_setting"

			//如果是小商店获取小商店的小程序key
			if payment.PayCode == 1 {
				key = "small_shop_wx_setting"
			}
			err, wxsetting = GetWxSetting(key)
			if err != nil {
				err = errors.New("获取微信小程序配置失败" + err.Error())
				return
			}
			paymentRequest.PayInfo.AppID = wxsetting.Value.AppId
			paymentRequest.PayInfo.OpenID = user.WxMiniOpenid
		}
		break
	}
	if typeString == "" {
		err = errors.New("支付类型错误")
		return
	}
	paymentRequest.PayType = typeString

	err, pay := serviceProviderSystemCommon.Initial()
	if err != nil {
		return
	}
	data, err := pay.PaymentPreOrder(paymentRequest)
	if err != nil {
		return
	}
	//是否是分账
	if paymentRequest.SettleType == "1" {
		err = source.DB().Model(&SmallShopPayInfo{}).Where("id = ?", payInfo.ID).Updates(map[string]interface{}{"payment_store_code": paymentStoreCode, "split_create_status": 0, "is_split_status": 1}).Error
		if err != nil {
			log.Log().Error("聚合支付分账创建记录失败", zap.Any("paymentStoreCode", paymentStoreCode), zap.Any("payInfo", payInfo), zap.Any("err", err.Error()))
			err = errors.New("创建支付记录失败" + err.Error())
			return
		}
	}
	if paymentRequest.PayType == "ALIPAY" {
		data.Fields.CounterURL = data.Fields.Code
	}
	resData = data

	return
}

func (weChat *AggregatePaymentPay) Refund() (err error, resData interface{}) {
	refund := weChat.ConvergenceWeChatRefund.(request.Refund)
	err, pay := serviceProviderSystemCommon.Initial()
	if err != nil {
		return
	}
	var refundRequest serviceProviderSystemCommon.RefundRequest
	refundRequest.OutTradeNo = refund.PaySN
	refundRequest.RefundAmount = fmt.Sprintf("%v", refund.Amount)
	err = pay.PaymentRefund(refundRequest)
	if err != nil {
		return
	}
	return
}

// todo 需求中这个不需要 充值
func (weChat *AggregatePaymentPay) Recharge() (err error, resData interface{}) {
	paymentData := weChat.ConvergenceWeChatPayment.(request.AggregatePaymentPayRequestData)
	if paymentData.PayType <= 0 {
		err = errors.New("支付类型不能为空")
		return
	}
	var paySN = strconv.Itoa(int(paymentData.Uid)) + strconv.Itoa(int(time.Now().Unix()))
	var UserTopUp model3.RechargeBalance
	//微信PC的支付 需要PC扫码之后H5唤起支付（H5无需登录所以微信需要先生成充值单）
	if paymentData.PayInfoID > 0 {
		err = source.DB().Where("id = ?", paymentData.PayInfoID).First(&UserTopUp).Error
		if err != nil {
			err = errors.New("充值单不存在" + err.Error())
			return
		}
		if UserTopUp.Status == 1 {
			err = errors.New("已充值,请重新生成二维码")
			return
		}
	} else {
		var count int64
		source.DB().Model(model.UserTopUp{}).Where("pay_sn", paySN).Count(&count)
		if count > 0 {
			err = errors.New("请勿使用重复支付单")
			return
		}
		UserTopUp.Amount = paymentData.Amount
		UserTopUp.PayType = paymentData.PayType
		UserTopUp.PaySN = utils2.StrToUInt(paySN)
		UserTopUp.Uid = paymentData.Uid
		err = source.DB().Create(&UserTopUp).Error
		if err != nil {
			err = errors.New("创建充值订单失败" + err.Error())
			return
		}

	}

	var user model3.User

	//如果不是登录支付就使用前端传过来的openid
	if paymentData.IsLogin == 1 {
		user.WxOpenid = paymentData.WxOpenid
	} else {
		err = source.DB().Where("id = ?", paymentData.Uid).First(&user).Error
		if err != nil {
			err = errors.New("获取用户信息失败" + err.Error())
			return
		}
	}

	config.Config().Local.Host, err = service.LocUrl()
	if err != nil {
		return
	}
	//config.Config().Local.Host = "https://40e0e2dd.cpolar.cn/"

	//设置额外信息 支付通知会请求返回
	var weChatNotify serviceProviderSystemCommon.Attach
	weChatNotify.PaySN = UserTopUp.PaySN
	weChatNotify.PayType = UserTopUp.PayType
	weChatNotify.PayCode = 0
	weChatNotify.WxOpenid = user.WxOpenid
	weChatNotify.Type = 1 //充值
	var communicationPaymentInfo model.CommunicationPaymentInfoType
	err = source.DB().Where("id = ?", paymentData.PayType-model.AGGREGATE_PAYMENT).First(&communicationPaymentInfo).Error
	if err != nil {
		err = errors.New("获取聚合支付失败" + err.Error())
		return
	}
	if config.Config().AggregatedPayment.PaymentStoreCode != communicationPaymentInfo.PaymentStoreCode {
		err = errors.New("支付商户与支付方式商户不一致,联系客服更新设置")
		return
	}

	// 回调url
	var notifyUrl = config.Config().Local.Host + "/supplyapi/api/finance/aggregatePaymentPayNotify"
	_, shopSettingData := shopSetting.Get()
	if shopSettingData.ShopName == "" {
		shopSettingData.ShopName = "供应链"
	}
	//settle_type 结算类型没有分账不用传
	paymentRequest := serviceProviderSystemCommon.PaymentRequest{
		Attach:           weChatNotify,
		PaymentStoreCode: communicationPaymentInfo.PaymentStoreCode,
		OutTradeNo:       strconv.Itoa(int(UserTopUp.PaySN)),
		SpbillCreateIP:   paymentData.Ip,
		//PayType:          communicationPaymentInfo.PayType,
		TotalAmount: strconv.Itoa(int(UserTopUp.Amount)), // 单位分
		Subject:     shopSettingData.ShopName,
		NotifyURL:   notifyUrl,
		OrderType:   3,
		RequestType: paymentData.Type,
		UserIP:      paymentData.Ip,
		PayScene:    "offline",
	}
	var wxsetting SysSetting
	///P:PC页面版(仅快捷支付支持)，M:H5页面版，W:微信小程序，Z：支付宝小程序，示例值：M
	//如果是微信支付需要这个
	//PayInfo: PayInfo{
	//	AppID:  "wxabc123123",
	//	OpenID: "oIYWg5-YAL9gBh3YzMHcmM9uo7gg",
	//},
	//M-H5
	paymentRequest.PayInfo.MemberID = strconv.Itoa(int(UserTopUp.Uid))

	switch paymentData.Type {
	case "P":

		break
	case "M":
		if communicationPaymentInfo.PayType == "WECHAT" {
			if user.WxOpenid == "" {
				err = errors.New("没有微信标识,无法使用微信支付")
				return
			}
			paymentRequest.PayInfo.OpenID = user.WxOpenid
			var key = "wechatofficial_setting"
			err, wxsetting = GetWxSetting(key)
			if err != nil {
				err = errors.New("获取微信公众号配置失败")
				return
			}
			paymentRequest.PayInfo.AppID = wxsetting.Value.AppId
		}
		break
	case "W":
		if communicationPaymentInfo.PayType == "WECHAT" || communicationPaymentInfo.PayType == "MINIAPP_PAY" {
			if user.WxMiniOpenid == "" {
				err = errors.New("没有微信小程序标识,无法使用小程序支付")
				return
			}
			var key = "wechatmini_setting"

			//如果是小商店获取小商店的小程序key
			if paymentData.PayCode == 1 {
				key = "small_shop_wx_setting"
			}
			err, wxsetting = GetWxSetting(key)
			if err != nil {
				err = errors.New("获取微信小程序配置失败" + err.Error())
				return
			}
			paymentRequest.PayInfo.AppID = wxsetting.Value.AppId
			paymentRequest.PayInfo.OpenID = user.WxMiniOpenid
		}
		break
	}
	paymentRequest.PayType = communicationPaymentInfo.PayType

	err, pay := serviceProviderSystemCommon.Initial()
	if err != nil {
		return
	}
	data, err := pay.PaymentPreOrder(paymentRequest)
	if err != nil {
		return
	}
	if paymentRequest.PayType == "ALIPAY" {
		data.Fields.CounterURL = data.Fields.Code
	}
	data.PaySN = UserTopUp.PaySN
	resData = data
	return
}

func (weChat *AggregatePaymentPay) Deduction() (err error, resData interface{}) {
	return
}

func (weChat *AggregatePaymentPay) GetBalance() (err error, resData interface{}) {
	return
}

func (weChat *AggregatePaymentPay) Settlement() (err error) {
	return
}

type SysSetting struct {
	yzgoModel.SysSetting
	Value Value `json:"value"`
}

type Value struct {
	IsOpen int    `mapstructure:"isopen" json:"isopen" yaml:"isopen"` //状态1开始2关闭
	AppId  string `mapstructure:"appid" json:"appid" yaml:"appid"`    //AppID 	//是否上传物流信息 1是 0否
}

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}

func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

// 微信小程序配置  key = wechatmini_setting 小程序  wechatofficial_setting 微信公众号  small_shop_wx_setting 小商店小程序  aggregated_payment_split_settlement 聚合支付分账
func GetWxSetting(key string) (err error, setting SysSetting) {
	err = source.DB().Where("`key` = ?", key).First(&setting).Error
	return
}
