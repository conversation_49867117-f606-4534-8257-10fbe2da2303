<div>
    <!-- 轮播图部分 -->
    <div class="dynamic-bgcolor">
        <div class="inner f fjsb banner-portion">
            <div style="width: 233px"></div>
            <!-- 轮播 -->
            <el-carousel style="background-color:#ffffff" class="f1 banner-box" height="420px" arrow="never"
                         :interval="5000">
                <el-carousel-item v-for="item in bannerData" :key="item.id">
                    <a href="javascript:;" @click="handleBannerJumpClick(item)">
                        <!-- <img class="img100" v-bind:src="item.src"> -->
                        <m-image :src="item.src"></m-image>
                    </a>
                </el-carousel-item>
            </el-carousel>
            <!-- 登录and公告部分 -->
            <div class="b-right-box" :style="{ backgroundColor: userData ? '' : 'white', paddingTop: userData ? '0' : '33px' }">
                <!-- 登录部分 已登录 -->
                <div class="login-box" v-if="userData">
                    <!-- <div class="avatar-box avatar-y">
                        <el-avatar :size="79" :src="userData.avatar">
                        </el-avatar>
                        <div class="label-temporary-box">
                            <p class="text-center" v-if="freeze === 1 && supplierStatus === 1">已冻结</p>
                            <p class="text-center" v-else>{{ $ls.getUser().user_level.name || "" }}</p>
                        </div>
                    </div>
                    <div class="text-center login-user-box">
                        <p>Hi, {{ userData.nickname ? userData.nickname : userData.username }}</p>
                        <p class="welcome-text">欢迎进入{{ shopSetting.header.welcome }}商城</p>
                    </div> -->
                    <div class="fl">
                        <router-link to="/personalCenter/generalize">
                            <div>
                                <i class="iconfont icon-geren iconPhoto"></i>
                                <span>个人中心</span>
                            </div>
                        </router-link>
                        <router-link to="/personalCenter/myOrder">
                            <div>
                                <i class="iconfont icon-dingdan2 iconPhoto"></i>
                                <span>我的订单</span>
                            </div>
                        </router-link>
                        <router-link to="/personalCenter/myWallet">
                            <div>
                                <i class="iconfont icon-daifukuan1 iconPhoto"></i>
                                <span>我的钱包</span>
                            </div>
                        </router-link>
                        <router-link to="/personalCenter/myClient">
                            <div>
                                <i class="iconfont icon-kehu iconPhoto"></i>
                                <span>我的客户</span>
                            </div>
                        </router-link>
                        <router-link to="personalCenter/batchOrder">
                            <div>
                                <i class="iconfont icon-piliangxiadan iconPhoto"></i>
                                <span>批量下单</span>
                            </div>
                        </router-link>
                        <router-link to="/selectionCenterList">
                            <div>
                                <i class="iconfont icon-a-ht_basis_shangpin_huaban1_huaban1 iconPhoto"></i>
                                <span>选品中心</span>
                            </div>
                        </router-link>
                    </div>
                    <div v-if="isApplyAPI || freeze === 1" style="height: 54px; background-color: #ffffff; margin-top: 5px;"></div>
                    <div style="height: 54px; background-color: #ffffff; margin-top: 5px;" v-else>
                        <div class="upgrade-btn-box" >
                            <el-button class=" upgrade-btn" size="small" 
                                       @click="$_blank('/personalCenter/apiProcurement')">
                                <!-- <router-link target="_blank" to="/supplierRegister">
                                    升级成为供应商
                                </router-link> -->
                                API采购
                            </el-button>
                        </div>
                    </div>
                    <!-- 供应商登录 -->
                    <div class="upgrade-btn-box" v-if="supplierEntryStatus === 2 ? false : true">
                        <div v-if="supplierApplicationStatus">
                            <el-button class=" upgrade-btn upgrade-btn-login" size="small"  @click="jumpAdmin" >供应商登录</el-button>
                        </div>
                        <div v-else>
                            <a href="/supplierRegister" target="_blank">
                                <el-button class=" upgrade-btn upgrade-btn-login" size="small" >供应商入驻</el-button>
                            </a>
                        </div>
                    </div>
                    <div v-else style="height: 50px;"></div>
                </div>
                <!-- 登录部分 未登录 -->
                <div class="login-box" v-else="false">
                    <div class="avatar-box">
                        <el-avatar :size="79" src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png">
                        </el-avatar>
                    </div>
                    <div class="text-center welcome-box">
                        <p>Hi, 你好!</p>
                        <p class="welcome-text">欢迎进入{{ shopSetting.header.welcome }}商城</p>
                    </div>
                    <div class="btn-box">
                        <el-button class="red-button w100 mt10" size="small" type="danger"
                                   @click="$router.push('/login')">登录
                        </el-button>
                        <div class="f fjsb">
                            <el-button size="small" class="login-button w100" @click="$router.push('/register')">注册
                            </el-button>
                            <!-- <el-button size="small" class="login-button" @click="$router.push('/register')">入驻
                            </el-button> -->
                        </div>
                    </div>
                </div>
                <!-- 公告部分 -->
                <el-tabs v-model="activeName" class="notice-box">
                    <el-tab-pane v-for="item in userLinkGroups" :key="item.id" :label="item.title" :name="item.title">
                        <a href="javascript:;" @click="$newOpenLink(link.url)" v-for="link in item.child" :key="link.id">
                            <p>{{ link.title }}</p>
                        </a>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </div>

    <div class="index-background-color">
        <!-- 商品专辑部分 -->
         <template v-show="home_album_switch && albumList.length > 0">
            <div class="inner swiper-album swiper-container" >
                <div class="swiper-wrapper album-wrapper" style="margin-left: 0px">
                    <div class="swiper-slide" style="margin-right: 13px;" v-for="item in albumList">
                        <div class="album-box" >
                            <div class="album-title limit-line1" @click="$router.push('albumDetail?productId=' + item.product_album_id)">{{ item.name }}</div>
                            <div class="con-tag">
                                <el-tag type="warning" size="small" v-for="tag in item.tags">
                                        {{tag}}
                                </el-tag>
                            </div>
                            <div class="con-img">
                                <el-row :gutter="10">
                                    <template v-for="img in item.products">
                                        <el-col :span="8" class="border item-img" style="width: 66px; height: 66px; padding: 0;cursor: pointer;">
                                            <el-image :src="img.image_url" @click="$_blank('goodsDetail', {goods_id: img.id})"></el-image>
                                        </el-col>
                                    </template>
                                </el-row>
                            </div>
                            <div class="f fjsb" style="font-size: 12px;">
                                <div style="color: #F42121;cursor: pointer;" @click="$router.push('albumDetail?productId=' + item.product_album_id)">查看详情（商品 {{item.product_count}}）</div>
                                <div>已售{{item.sales_total}}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
         </template>
        <div class="inner"  v-if="home_album_switch === 1 && albumList.length > 0"  style="width: 80px; margin: 15px auto 50px;">
            <el-button class="btn-search" size="mini" @click="$router.push('/albumList')">全部专辑</el-button>
        </div>
        <!-- 热销部分 -->
        <div class="inner f">
            <!-- 左侧部分 -->
            <div 
                class="hot-sell-box bg-default" 
                :style="bgObject"
            >
                <div class="text-center hot-sell-h2-box">
                    <h1 class="hot-sell-h2">{{ productBox.banner.title }}</h1>
                </div>
                <p class="hot-sell-synopsis">{{ productBox.banner.desc }}</p>
            </div>

            <!-- 右侧商品部分 -->
            <div class="f f1 hot-sell-right-box swiper-product swiper-container">
                <div class="hot-sell-product-box swiper-wrapper" style="margin-left: 0px">
                    <div class="swiper-slide" v-for="item in productBox.product_cards" :key="item.id">
                        <router-link target="_blank" :to="`/goodsDetail?goods_id=${item.id}`">                         
                            <div class="hot-sell-product-img-box">
                                <m-image :size="['160px','160px']" :src="item.thumb"></m-image>
                            </div>
                            <div class="hot-sell-product-b-box">
                                <p>{{ item.title }}</p>
                                <div class="f fac">
                                    <template v-if="shopSetting.page_setup_setting.pc_list_setting.is_super_wholesal_price === 0">
                                        <span class="retail-price-title mt10">{{shopSetting.page_setup_setting.language_setting.super_wholesal_price === '' ? '超级批发价' : shopSetting.page_setup_setting.language_setting.super_wholesal_price }}: </span>
                                        <span class="retail-price-number mt10" v-if="userLevelTitle && $fn.isMatchingPriceAuthority(item.id)">￥{{
                                                item.min_price |
                                                        formatF2Y
                                            }}</span>
                                        <span class="retail-price-number mt10" v-else-if="userLevelTitle">￥{{
                                                userLevelTitle
                                            }}</span>
                                        <span class="retail-price-number mt10" v-else-if="$ls.getUserId()">￥{{
                                                item.min_price |
                                                        formatF2Y
                                            }}</span>
                                        <el-button type="text" class="color-red mt10" v-else
                                                   @click.prevent="$router.push('/login')">登录查看
                                        </el-button>
                                    </template>
                                    <del v-if="shopSetting.page_setup_setting.pc_list_setting.is_suggested_retail_price === 0" class="mt10 del-price">{{ item.origin_price | formatF2Y }}</del>
                                </div>
                            </div>
                        </router-link>
                    </div>
                </div>
            </div>

            <!--                    <div class="f f1 hot-sell-right-box swiper-container" >-->
            <!--                            <div class="hot-sell-product-box swiper-wrapper" v-for="item in productBox.product_cards" :key="item.id">-->
            <!--                                <div class="swiper-slide">-->
            <!--                                <router-link  target="_blank" :to="`/goodsDetail?goods_id=${item.id}`">-->
            <!--                                    <div class="hot-sell-product-img-box">-->
            <!--                                        <img :src="item.thumb">-->
            <!--                                        <m-image :size="['160px','160px']" :src="item.thumb"></m-image>-->
            <!--                                    </div>-->
            <!--                                    <div class="hot-sell-product-b-box">-->
            <!--                                        <p>{{ item.title }}</p>-->
            <!--                                        <div class="f fac">-->
            <!--                                            <span class="retail-price-title mt10">超级批发价: </span>-->
            <!--                                            <span class="retail-price-number mt10" v-if="$ls.getDiscount() !== null">￥{{-->
            <!--                                                    $fn.superTradePrice(item.min_price) |-->
            <!--                                                            formatF2Y-->
            <!--                                                }}</span>-->
            <!--                                            <el-button type="text" class="color-red mt10" v-else-->
            <!--                                                       @click.prevent="$router.push('/login')">登录查看-->
            <!--                                            </el-button>-->
            <!--                                            <del class="mt10 del-price">{{ item.origin_price | formatF2Y }}</del>-->
            <!--                                        </div>-->
            <!--                                    </div>-->
            <!--                                </router-link>-->
            <!--                                </div>-->
            <!--                            </div>-->
            <!--                        </div>-->
        </div>


        <!-- 频道广场 -->
        <div class="inner">
            <!-- <m-title title="频道广场"></m-title> -->
            <m-title title="频道广场"></m-title>
            <div class="advertising-box">
                <!-- 头两个大图 -->
                <div v-for="item in channelImages" :key="item.id" @click="handleBannerJumpClick(item)">
                    <!-- <img :src="item.src" @click="handleBannerJumpClick(item)"> -->
                    <m-image :src="item.src"></m-image>
                </div>
                <!--<div v-for="item in channelCards" :key="item.id">
                    <p class="title-p">{{item.title}} <span>{{item.desc}}</span></p>
                    <img :src="item.src">
                </div>-->
                <div v-for="item in channelCards" :key="item.id" class="small-product">
                    <p class="title-p">{{ item.title }} <span>{{ item.desc }}</span></p>
                    <div class="f fjc">
                        <a href="javascript:;" @click="handleBannerJumpClick(channelCardBanner)"
                           v-for="channelCardBanner in item.banner" :key="channelCardBanner.id">
                            <m-image :src="channelCardBanner.src" :size="item.banner && item.banner.length > 1 ? ['110px','110px'] : ['246px','110px']"></m-image>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        <!-- 每日特价 -->
        <div class="inner" v-for="item in productBoxes" :key="item.id">
            <div class="cont-box-title">
                <m-title :title="item.banner.title"></m-title>
                <!-- <el-button type="text" v-if="item.product_cards && item.product_cards.length >= 10"
                    @click="$_blank('/albumGoods',{album_key:item.banner.collection_id})" class="color-red more-btn">
                    查看更多
                </el-button> -->
            </div>

            <m-goods>
                <goods-item v-for="itemProduct in item.product_cards" :key="itemProduct.id"
                            :product_id="itemProduct.id"
                            :page_setup_setting="shopSetting.page_setup_setting"
                            :link="`/goodsDetail?goods_id=${itemProduct.id}`" :url="itemProduct.thumb"
                            :price="itemProduct.min_price"
                            :title="itemProduct.title"
                            :originPrice="itemProduct.origin_price">
                </goods-item>
            </m-goods>
            <div class="text-center mt10">
                <el-button size="mini" class="red-btn" v-if="item.product_cards && item.product_cards.length >= 10"
                           @click="$_blank('/albumGoods',{album_key:item.banner.collection_id})">查看全部
                </el-button>
            </div>
        </div>
        <!-- 为你推荐 -->
        <div class="inner" ref="recommendDiv" v-if="productMenuBox.menus!=null">
            <m-title :title="productMenuBox.banner.title"></m-title>
            <div class="recommend-tabs-box f fac fjsb">
                <el-radio-group v-model="recommendSearch.category1_id" @change="handleRecommendChange">
                    <el-radio v-for="(menu,index) in productMenuBox.menus" :key="index" :label="menu.id">
                        {{ $fn.cutString(menu.name, 44, "...") }}
                    </el-radio>
                    <!-- <el-radio :label="0" v-if="productMenuBoxIsShow">
                        全部
                    </el-radio> -->
                </el-radio-group>
                <router-link target="_blank" class="recommend-a-all"
                             to="/goodsList?category1_id=&category2_id=&category3_id=">全部
                </router-link>
            </div>
        </div>
        <div class="inner mt10">

            <div class="f fjsb fw special-offer-box" v-loading="recommendLoading">
                <m-goods>
                    <goods-item v-for="item in productMenuBoxProductCardsTemp" :key="item.id"
                                :page_setup_setting="getFramework.page_setup_setting"
                                :link="`/goodsDetail?goods_id=${item.id}`" :url="item.thumb"
                                :price="item.min_price"
                                :title="item.title" :originPrice="item.origin_price"></goods-item>
                                <skeletonGoods v-if="hasMore  && recommendLoading === false" v-for="item in 10"></skeletonGoods>
                </m-goods>
            </div>
        </div>
    </div>
    <!-- 为你推荐 头部 -->
    <el-collapse-transition>
        <div class="homeHead" v-show="headIsShow">
            <div class="homeHead-top">

                <Head :flg="2"/>
            </div>
            <div class="bgw">
                <div class="recommend-tabs-box f fac fjsb inner">
                    <el-radio-group v-model="recommendSearch.category1_id" @change="handleRecommendChange">
                        <el-radio v-for="(menu,index) in productMenuBox.menus" :key="index" :label="menu.id">
                            {{ menu.name }}
                        </el-radio>
                    </el-radio-group>
                    <router-link target="_blank" class="recommend-a-all" to="/categoryGoodsList?category1=0">全部
                    </router-link>

                </div>
            </div>
        </div>
    </el-collapse-transition>
    <shared-dialog ref="sharedDialog"></shared-dialog>
</div>