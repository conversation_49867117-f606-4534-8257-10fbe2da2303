<!-- 店铺 -->
<div class="store-box inner">
    <Breadcrumb :data="[{name:'首页'},{name:supplier.name}]"/>
    <div class="f">
        <!-- 左侧部分 -->
        <div class="store-left-box">
            <!-- 店铺信息 -->
            <div class="store-info-box bgw">
                <div class="f fac top">
                    <m-image :src="supplier.shop_logo" :size="['50px','50px']"></m-image>
                    <div>
                        <p class="title-p">{{ supplier.name }}</p>
                        <p class="address-p">{{ supplier.province }} {{ supplier.city }} {{ supplier.county }}</p>
                    </div>
                </div>
                <div class="statis-box">
                    <p><span class="title-span">商品总数</span> <span class="red-span">{{ supplier.goods_count }}</span></p>
                    <p><span class="title-span">热销(件)</span> <span class="red-span">{{ supplier.hot_sale_count }}</span>
                    </p>
                </div>
                <div class="xian"></div>
                <div class="score-box f fjsb">
                    <div class="text-center">
                        <p>描述相符</p>
                        <p class="red-p">{{ supplier.describe_score || 0 }}</p>
                    </div>
                    <div class="text-center">
                        <p>卖家服务</p>
                        <p class="red-p">{{ supplier.service_score || 0 }}</p>
                    </div>
                    <div class="text-center">
                        <p>物流服务</p>
                        <p class="red-p">{{ supplier.shopping_score || 0 }}</p>
                    </div>
                </div>
                <div class="btn-box f fjsb">
                    <!-- v-if="!isFavoriteStore" -->
                    <el-button v-if="$route.query.type === '0' && !isFavoriteStore" class="btn2" :disabled="supplier.id === 0" @click="favoriteStore()"
                               >收藏店铺
                    </el-button>
                    <el-button class="btn1" @click="favoriteStore()" v-if="isFavoriteStore">取消收藏</el-button>
                    <el-button class="btn2"
                               @click="$router.push({path:'/store',query:{case:'license',sid:$route.query.sid,type:$route.query.type}})">店铺执照
                    </el-button>
                </div>
            </div>
            <!-- 店铺公告 -->
            <div class="store-notice-box bgw mt20">
                <p class="title-p">店铺公告</p>
                <p class="cont-p">
                <div v-if="supplier.supplier_setting && supplier.supplier_setting.affiche" class="ql-editor"
                     v-html="supplier.supplier_setting.affiche">
                </div>
                <span v-else>暂无公告~</span>
                </p>
            </div>
            <!-- 退货说明 -->
            <div class="return-box bgw mt20">
                <p class="title-p">退货说明</p>
                <p class="cont-p">
                <div v-if="supplier.supplier_setting && supplier.supplier_setting.explain" class="ql-editor"
                     v-html="supplier.supplier_setting.explain">
                </div>
                <span v-else>暂无说明~</span>
                </p>
            </div>
        </div>
        <!-- 右侧部分 -->
        <div class="store-right-box f1" v-if="$route.query.case !== 'license'">
            <!-- 类别部分 -->
            <div class="classify-box bgw">
                <div class="classify1-div">
                    <el-radio-group class="classify1-radio-group" :class="isUnfold ? 'unfold' : ''"
                                    v-model="searchForm.category1_id" @change="handleTypeChange">
                        <el-radio v-for="item in classifyList" :key="item.id" :label="item.id">{{ item.name }}
                        </el-radio>
                    </el-radio-group>
                    <div class="operation-div">
                        <a href="javascript:;" class="operation-a"
                           @click="isUnfold = !isUnfold">{{ isUnfold | unfoldText }} <i
                                :class="isUnfold ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i></a>
                    </div>
                </div>
            </div>
            <!-- 搜索框部分 -->
            <div class="search-form-box bgw mt10 f fac">
                <div class="screen-item" v-for="item in sortArr" :key="item.id" @click="handleCheckSort(item)">
                    <span class="title-span" :class="checkedSort.id === item.id ? 'active' : ''">{{ item.title }}</span>
                    <span class="caret-wrapper">
                        <i v-for="icon in item.sortIcons" :key="icon.id" class="sort-icon"
                           :class="`${icon} ${checkedSort.sortIcon === icon && checkedSort.id === item.id ? 'active' : ''}`"></i>
                    </span>
                </div>
                <el-input v-model="min_price" class="w87">
                    <span slot="prefix">￥</span>
                </el-input>
                <p class="xian-p"></p>
                <el-input v-model="max_price" class="w87">
                    <span slot="prefix">￥</span>
                </el-input>
                <el-input v-model="searchForm.title" placeholder="商品名称" class="goodsName-input"></el-input>
                <el-button @click="handleSearchClick">确定</el-button>
            </div>
            <!-- 商品部分 -->
            <div v-loading="isLoading">
                <m-goods :rows="4" v-if="goodsList.length > 0">
                    <goods-item v-for="item in goodsList" :key="item.id" :link="`/goodsDetail?goods_id=${item.id}`"
                                :product_id="item.id"
                                :page_setup_setting="getFramework.page_setup_setting"
                                :url="item.thumb" :price="item.min_price" :title="item.title"
                                :originPrice="item.origin_price">
                    </goods-item>
                    <skeletonGoods v-if="hasMore  && isLoading === false" v-for="item in 10"></skeletonGoods>

                </m-goods>
                <!-- <div class="goods-box" v-if="goodsList.length > 0">
                    <div class="goods-offer-item" v-for="item in goodsList" :key="item.id">
                        <router-link target="_blank" :to="`/goodsDetail?goods_id=${item.id}`">
                            <div class="goods-offer-product-img-box">
                                <m-image :size="['160px','160px']" :src="item.thumb"></m-image>
                            </div>
                            <div class="goods-offer-b-box">
                                <p>{{ item.title }}</p>
                                <div class="f fac">
                                    <span class="goods-offer-title mt10">零售价</span>
                                    <span class="goods-offer-number mt10">￥{{ item.price | formatF2Y }}</span>
                                </div>
                            </div>
                        </router-link>
                    </div>
                </div> -->
                <m-empty v-else></m-empty>
            </div>
            <!-- <div class="text-center">
                <Pagination @pagination="pagination" :page="page" :limit="pageSize" class="pagination"
                            layout="prev,pager, next,total, jumper" :total="total"></Pagination>
            </div> -->
        </div>
        <div v-else class="store-right-box f1">
            <div class="title-yyzz-box bgw f fac fjsb">
                <p class="title-yyzz-p">查看营业执照</p>
                <a href="javascript:;" @click="$router.back()">返回</a>
            </div>
            <div class="yyzz-cont bgw mt10 text-center">
                <div v-if="supplier.supplier_qualification && supplier.supplier_qualification.length > 0">
                    <template v-for="item in supplier.supplier_qualification">
                        <img style="max-width: 1000px" v-for="item2 in item.imgs" :src="item2">
                    </template>
                </div>
                <img v-else src="../../../assets/images/blanck_bg.png">
            </div>
        </div>
    </div>
</div>