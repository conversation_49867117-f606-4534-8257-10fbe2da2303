<template>
    <!-- 选购中心 -->
    <div>
        <!-- 用户筛选卡片 -->
        <div class="screening-card">
            <div class="merchandise-resources">
                <div class="tag-name">商品来源：</div>
                <template v-for="item in merchandiseresources">
                    <div
                        :key="item.source_name"
                        v-if="
                            terraceKEY ==
                            item.gather_supply_id +
                                '-' +
                                item.type +
                                '-' +
                                item.source_id
                        "
                        class="merchandise-resources-style merchandise-resources-col"
                    >
                        {{ item.source_name }}
                    </div>
                    <div
                        :key="item.source_name"
                        @click="goodsresources(item)"
                        class="merchandise-resources-style"
                        v-else
                    >
                        {{ item.source_name }}
                    </div>
                </template>
            </div>
            <div class="merchandise-screen">
                <div class="tag-name">类目：</div>
                <el-select
                    class="merchandise-screen-select"
                    v-model="value.fl1"
                    placeholder="请选择一级分类"
                    @change="getCategory(2, value.fl1)"
                >
                    <el-option
                        v-for="item in category1"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="merchandise-screen-select"
                    v-model="value.fl2"
                    placeholder="请选择二级分类"
                    @change="getCategory(3, value.fl2)"
                >
                    <el-option
                        v-for="item in category2"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="merchandise-screen-select"
                    v-model="value.fl3"
                    placeholder="请选择三级分类"
                >
                    <el-option
                        v-for="item in category3"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                    >
                    </el-option>
                </el-select>
            </div>
            <div class="merchandise-screen-good">
                <div class="tag-name-good">
                    <div class="tag-name">商品属性：</div>
                </div>
                <div class="f">
                    <div
                        v-if="value.grossProfitRate != 5"
                        class="merchandise-screen-select"
                    >
                        <div class="select-name">毛利率</div>
                        <el-select
                            class="selecl-sty"
                            v-model="value.grossProfitRate"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in grossProfitRate"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="merchandise-screen-select-eles">
                        <div class="select-name">毛利率</div>
                        <el-input
                            class="input-sty"
                            v-model="value.g1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="value.g2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">%</div>
                        </div>
                    </div>
                    <div
                        v-if="value.profitRate != 5"
                        class="merchandise-screen-select"
                    >
                        <div class="select-name">利润率</div>
                        <el-select
                            class="selecl-sty"
                            v-model="value.profitRate"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in profitRateOptios"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="merchandise-screen-select-eles">
                        <div class="select-name">利润率</div>
                        <el-input
                            class="input-sty"
                            v-model="value.p1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="value.p2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">%</div>
                        </div>
                    </div>
                    <div
                        v-if="value.agreementPrice != 5"
                        class="merchandise-screen-select"
                    >
                        <div class="select-name">协议价</div>
                        <el-select
                            class="selecl-sty"
                            v-model="value.agreementPrice"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in agreementPriceOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="merchandise-screen-select-eles">
                        <div class="select-name">协议价</div>
                        <el-input
                            class="input-sty"
                            v-model="value.a1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="value.a2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">元</div>
                        </div>
                    </div>
                    <div class="merchandise-screen-select">
                        <div class="select-name">指导价</div>
                        <el-select
                            class="selecl-sty"
                            v-model="value.guidePrice"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in guide_marketing_PriceOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-if='value.discount !== 5' class="merchandise-screen-select">
                        <div class="select-name">折扣</div>
                        <el-select
                            class="selecl-sty"
                            v-model="value.discount"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in discountList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="merchandise-screen-select-eles">
                        <div class="select-name">折扣</div>
                        <el-input
                            class="input-sty"
                            v-model="value.d1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="value.d2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">折</div>
                        </div>
                    </div>
                    <div class="merchandise-screen-select">
                        <div class="select-name">营销价</div>
                        <el-select
                            class="selecl-sty"
                            v-model="value.marketingPrice"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in guide_marketing_PriceOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div class="merchandise-screen-select">
                        <div class="select-name selectc">是否已导入</div>
                        <el-select
                            class="selecl-sty selectcs"
                            v-model="value.self_is_import"
                            placeholder="请选择"
                        >
                            <el-option label="全部" value=""></el-option>
                            <el-option label="已导入" value="1"></el-option>
                            <el-option label="未导入" value="0"></el-option>
                        </el-select>
                    </div>
                    <div class="merchandise-screen-select">
                        <div class="select-name" style="width: 60px">
                            营销属性
                        </div>
                        <el-select
                            class="selecl-sty"
                            style="width: 182px"
                            v-model="value.yxsx"
                            placeholder="请选择"
                        >
                            <el-option label="默认" value=""></el-option>
                            <el-option label="新品" value="is_new"></el-option>
                            <el-option label="热卖" value="is_hot"></el-option>
                            <el-option
                                label="促销"
                                value="is_promotion"
                            ></el-option>
                        </el-select>
                    </div>
                </div>
            </div>
            <div class="merchandise-screen" style="margin-top: 0">
                <div class="tag-name">关键词：</div>
                <el-input
                    v-model="value.goodsName"
                    class="merchandise-screen-select"
                    placeholder="商品名称"
                ></el-input>
                <el-input
                    v-model="value.shop_words"
                    class="merchandise-screen-select"
                    placeholder="商铺名称"
                ></el-input>
                <el-input
                    v-model="value.supplier_id"
                    class="merchandise-screen-select"
                    placeholder="商铺ID"
                ></el-input>
            </div>
            <div class="merchandise-screen">
                <div class="tag-name"></div>
                <el-button class="but-true" @click="search">搜索</el-button>
                <el-button @click="resetfun">重置</el-button>
                <div v-if="ai_key && ai_type" class="btn-ai f fac ml10 shou" @click="selectAi">
                    <img style="width: 21px;" src="../../assets/images/xuanpinai.png">
                    <div class="ml5">选品AI</div>
                </div>
                <div class="selection-num" @click="toMySel">
                    我的选品库（{{ my_center_total ? my_center_total : 0 }}）
                </div>
            </div>
        </div>
        <!-- 排序 -->
        <div class="check-all">
            <div class="check-all-left">
                <el-checkbox
                    style="margin-right: 30px"
                    v-model="checked"
                    @change="checkedfun"
                    >全选</el-checkbox
                >
                <template v-for="item in sort">
                    <div
                        v-if="item.id == 1 && item.id == sortid"
                        :key="item.id"
                        style="cursor: pointer; color: #f42121"
                    >
                        {{ item.name }}
                    </div>
                    <div
                        v-else-if="item.id == 1 && item.id != sortid"
                        :key="item.id"
                        @click="newest"
                        style="cursor: pointer"
                    >
                        {{ item.name }}
                    </div>
                </template>
                <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                    <template v-for="item in sort">
                        <SortButton
                            v-if="item.id != 1"
                            :key="item.id"
                            :value="item.value"
                        >
                            <space-between
                                v-if="item.id == sortid"
                                style="color: #f42121"
                            >
                                {{ item.name }}
                            </space-between>
                            <span v-else @click="sortid = item.id">{{
                                item.name
                            }}</span>
                        </SortButton>
                    </template>
                </SortButtonGroup>
            </div>
            <div class="check-all-right">
                <div @click="tolead(0)">
                    待导入商品（{{ selection_list.length }}）
                </div>
                <div @click="tolead(1)">
                    导入全部筛选商品（{{
                        paginationValue.total ? paginationValue.total : 0
                    }}）
                </div>
            </div>
        </div>
        <!-- 商品展示 -->
        <div class="commodity" v-loading="isLoading">
            <template v-for="item in shop">
                <div class="commodity-card">
                    <div class="commodity-card-true">
                        <img
                            class="commodityimg"
                            :src="item.image_url"
                            alt=""
                        />
                        <div class="commodity-name">{{ item.title }}</div>
                        <div class="price">
                            <div class="price-box fen">
                                <div class="price-name">协议价</div>
                                <!-- <div class="price-num">¥{{ item.agreement_price | formatF2Y }}</div> -->
                                <div class="price-num">
                                    ¥{{ item.agreement_price / 100 }}
                                </div>
                            </div>
                            <div class="price-box">
                                <div class="price-name">利润率</div>
                                <div class="price-num">
                                    ¥{{ $fn.changeMoneyY2F(item.market_rate) }}%
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="commodity-card-hover">
                        <div style="position: relative">
                            <div class="commodityimg-hover">
                                <img :src="item.image_url" alt="" />
                            </div>
                            <div class="commodityimg-hover-button">
                                <div
                                    style="border-right: 1px solid #ffffff"
                                    @click="goodsDetail(item.id)"
                                >
                                    查看详情
                                </div>
                                <div v-if="item.is_import == 1">已导入</div>
                                <div
                                    v-else-if="
                                        !selection_list.find(
                                            (val) => val == item.id,
                                        )
                                    "
                                    @click="addOption(item.id)"
                                >
                                    加入选品
                                </div>
                                <div v-else @click="delOption(item.id)">
                                    移除选品
                                </div>
                            </div>
                        </div>
                        <div class="commodity-name">{{ item.title }}</div>
                        <div class="price">
                            <div class="price-box fen">
                                <div class="price-name">协议价</div>
                                <!-- <div class="price-num">¥{{ item.agreement_price | formatF2Y }}</div> -->
                                <div class="price-num">
                                    ¥{{ item.agreement_price / 100 }}
                                </div>
                            </div>
                            <div class="price-box">
                                <div class="price-name">利润率</div>
                                <div class="price-num">
                                    ¥{{ $fn.changeMoneyY2F(item.market_rate) }}%
                                </div>
                            </div>
                        </div>
                        <div class="det-price">
                            <div class="det-price-box">
                                <div class="det-price-name">指导价</div>
                                <!-- <div class="det-price-num">¥{{ item.guide_price | formatF2Y }}</div> -->
                                <div class="det-price-num">
                                    ¥{{ item.guide_price / 100 }}
                                </div>
                            </div>
                            <div class="det-price-box">
                                <div class="det-price-name">最小利润</div>
                                <div class="det-price-num">
                                    ¥{{ item.min_profit / 100 }}
                                </div>
                            </div>
                            <div class="det-price-box">
                                <div class="det-price-name">折扣</div>
                                <!-- <div class="det-price-num">{{  item.profit | formatF2Y }}</div> -->
                                <div class="det-price-num">
                                    {{ item.profit / 100 }}
                                </div>
                            </div>
                            <div class="det-price-box">
                                <div class="det-price-name">毛利率</div>
                                <div class="det-price-num">
                                    {{
                                        $fn.changeMoneyY2F(
                                            item.gross_profit_rate,
                                        )
                                    }}%
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <skeletonGoods v-if="hasMore  && isLoading === false" v-for="item in 10"></skeletonGoods>
        </div>
        
        <!-- 分页 -->
        <!-- <div class="paging">
            <Pagination
                :current-page.sync="paginationValue.currentpage"
                :limit="paginationValue.limit"
                :page="paginationValue.page"
                :total="paginationValue.total"
                layout="total, prev, pager, next, jumper"
                @pagination="pagination"
            ></Pagination>
        </div> -->
        <!-- 导入对话框 -->
        <el-dialog
            title="导入商品"
            :visible.sync="centerDialogVisible"
            @close="nolead"
        >
            <div class="title-con" v-if="!centerDialogVisibleID">
                当前待导入商品数量：{{ selection_list.length }}
            </div>
            <div class="title-con" v-else>
                当前待导入商品数量：{{ paginationValue.total }}
            </div>
            <div class="title-text">
                点击导入，将待导入商品加入选品库，通过API对接您的系统，可一键导入您选品库的所有商品!
            </div>
            <div class="title-button">
                <el-button class="ackbutton" @click="acktrue">导入</el-button>
                <el-button @click="nolead">取消</el-button>
            </div>
        </el-dialog>
        <selectDialog ref="selectDialog" @getSelectAiList="getSelectAiList"></selectDialog>
    </div>
</template>

<script>
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue';
import SortButton from '@/components/sortButton/sortButton.vue';
import selectDialog from "./selectDialog.vue"
import { nextTick } from 'vue';
import scrollPaginationMixin from '@/mixin/scrollPaginationMixin.js';
import skeletonGoods from '@/components/mGoods/skeletonGoods.vue';

export default {
    name: 'list',
    components: { SortButtonGroup, SortButton, selectDialog,skeletonGoods },
    mixins: [scrollPaginationMixin],
    data() {
        return {
            searchbool: false, // 判断 搜索 状态
            centerDialogVisible: false, // 弹窗开关
            centerDialogVisibleID: 0, // 判断打开弹窗渲染的数据
            checked: false, // 全选商品的按钮
            sort: [
                // 排序数组
                { id: 1, name: '最新上架', value: 'created_at' },
                { id: 2, name: '协议价', value: 'agreement_price' },
                { id: 3, name: '指导价', value: 'guide_price' },
                { id: 4, name: '营销价', value: 'activity_price' },
                { id: 5, name: '利润率', value: 'market_rate' },
                { id: 6, name: '毛利率', value: 'gross_profit_rate' },
                { id: 7, name: '折扣', value: 'discount' },
            ],
            sortForm: {
                // 最新上架:'created_at', 协议价:agreement_price,
                // 指导价:guide_price, 营销价:activity_price,
                // 利润律:market_rate, 毛利率:gross_profit_rate
                value: '',
                sort: '1', // 1为升序，2为降序
            },
            sortid: 0, // 排序id
            // 分页数据
            paginationValue: {
                page: 1,
                limit: 25,
                total: 0,
                currentpage: 1,
            },
            merchandiseresources: [
                // 商品来源
                { name: '全部', id: 0 },
                { name: '供应商', id: 1 },
            ],
            value: {
                // 筛选数据
                terrace: -1, // 商品来源
                terraceName: '全部',
                fl1: '', // 一级 类目
                fl2: '', // 二级
                fl3: '', // 三级
                grossProfitRate: '', // 毛利率
                profitRate: '', // 利润率
                agreementPrice: '', // 协议价
                guidePrice: '', // 指导价
                marketingPrice: '', // 营销价
                self_is_import: '', // 是否已导入
                discount: null,
                goodsName: '', // 商品名称
            },
            shop: [], // 商品数据
            selection_list: [], // 选品库列表
            my_center_total: 0, // 我的选品库
            // 类目下拉框
            category1: [],
            category2: [],
            category3: [],
            // 毛利率
            grossProfitRate: [
                { label: '0-35%', value: 1 },
                { label: '35%-50%', value: 2 },
                { label: '50%-75%', value: 3 },
                { label: '75%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            // 利润率optios
            profitRateOptios: [
                { label: '0-50%', value: 1 },
                { label: '50%-150%', value: 2 },
                { label: '150%-300%', value: 3 },
                { label: '300%及以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            // 协议价optios
            agreementPriceOptions: [
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            // 指导价/营销价optios
            guide_marketing_PriceOptions: [
                { label: '0-200', value: 1 },
                { label: '200-500', value: 2 },
                { label: '500-1000', value: 3 },
                { label: '1000元以上', value: 4 },
            ],
            // 折扣
            discountList: [
                { label: '0-3折', value: 1 },
                { label: '3-5折', value: 2 },
                { label: '5-8折', value: 3 },
                { label: '8折以上', value: 4 },
                { label: '自定义区间', value: 5 },
            ],
            terraceKEY: '-1-0--1',
            ai_type: '',
            ai_key: '',
        };
    },
    mounted() {
        this.getAISetting()
    },
    methods: {
        loadNextPage() {
            this.paginationValue.page = this.paginationValue.page + 1; // 修改页数;
            this.getGoodsList(this.paginationValue.page,0,true)
        },
        // 获取AI配置信息
        async getAISetting() {
            const res = await this.$post('/ai/getSetting');
            if (res.code === 0) {
                this.ai_type = res.data.setting.value .ai_type
                this.ai_key = res.data.setting.value .silicon_flow_app_key
            }
        },
        // 点击商品来源立即搜索
        goodsresources(res) {
            this.value.terrace = res.gather_supply_id;
            this.value.terraceName = res.source_name;
            this.terraceKEY =
                res.gather_supply_id + '-' + res.type + '-' + res.source_id;
            if (this.value.goodsName && this.value.goodsName.trim() == '') {
                this.$message('请重新输入商品名称');
                return;
            }
            if (this.value.shop_words && this.value.shop_words.trim() == '') {
                this.$message('请重新输入商铺名称');
                return;
            }
            if (this.value.supplier_id && this.value.supplier_id.trim() == '') {
                this.$message('请重新输入商铺ID');
                return;
            }
            this.searchbool = true;
            this.getGoodsList(1, 1);
        },
        // 跳转商品详情
        goodsDetail(res) {
            this.$_blank(`/goodsDetail?goods_id=${res}`);
        },
        // 筛选排序
        onChangeSort(res) {
            let val = this.sort.find((item) => item.value == res.value);
            this.sortid = val.id;
            // 从新调调用商品数据
            this.getGoodsList(1, 0);
        },
        // 跳转到我的选品库
        toMySel() {
            this.$router.push('/personalCenter/mySelectionList');
        },
        // 排序最新
        newest() {
            (this.sortForm = {
                value: 'created_at',
                sort: '2',
            }),
                (this.sortid = 1);
            // 从新调调用商品数据
            this.getGoodsList(1, 0);
        },
        // 分页
        pagination(res) {
            this.paginationValue.page = res.page; // 修改页数
            // 从新调调用商品数据
            this.getGoodsList(res, 0);
        },
        // 开启导入对话框
        tolead(res) {
            this.centerDialogVisibleID = res;
            this.centerDialogVisible = true;
        },
        // 关闭导入对话框
        nolead() {
            this.centerDialogVisible = false;
        },
        // 全选按钮
        checkedfun() {
            let shop = this.shop.filter((item) => item.is_import != 1);
            let val;
            if (this.checked) {
                for (let index = 0; index < shop.length; index++) {
                    val = this.selection_list.find(
                        (item) => item == shop[index].id,
                    );
                    if (!val) {
                        this.selection_list.push(shop[index].id);
                    }
                }
            } else {
                for (let index = 0; index < shop.length; index++) {
                    val = this.selection_list.find(
                        (item) => item == shop[index].id,
                    );
                    if (val) {
                        this.selection_list = this.selection_list.filter(
                            (item) => item != shop[index].id,
                        );
                    }
                }
            }
        },
        // 商品导入
        acktrue() {
            let data = {};

            if (this.centerDialogVisibleID) {
                // 导入全部筛选商品

                // 上下架
                data.is_display = 1;

                // 排序条件
                data.type = this.sortForm.value;
                switch (this.sortForm.sort) {
                    case 1:
                        data.sort = true;
                        break;
                    case 2:
                        data.sort = false;
                        break;
                }

                // 商品来源
                if (this.value.terrace != -1) {
                    let str = this.merchandiseresources.find(
                        (item) => item.source_name == this.value.terraceName,
                    );
                    if (str.type) {
                        searchForm.gather_supply_id = str.gather_supply_id;
                    } else {
                        searchForm.source = str.source_id;
                    }
                }

                // 类目
                if (this.value.fl1) {
                    data.category_1_id = parseInt(this.value.fl1);
                }
                if (this.value.fl2) {
                    data.category_2_id = parseInt(this.value.fl2);
                }
                if (this.value.fl3) {
                    data.category_3_id = parseInt(this.value.fl3);
                }

                // 毛利率
                if (this.value.grossProfitRate) {
                    if (this.value.grossProfitRate == 5) {
                        data.gross_profit_rate = {
                            from: this.value.g1,
                            to: this.value.g2,
                        };
                    } else {
                        const rate = [
                            { from: 0, to: 35 },
                            { from: 35, to: 50 },
                            { from: 50, to: 75 },
                            { from: 75, to: 9999 },
                        ];
                        data.gross_profit_rate =
                            rate[this.value.grossProfitRate - 1];
                    }
                }

                // 利润率
                if (this.value.profitRate) {
                    if (this.value.profitRate == 5) {
                        data.market_rate = {
                            from: this.value.p1,
                            to: this.value.p2,
                        };
                    } else {
                        const rate = [
                            { from: 0, to: 50 },
                            { from: 50, to: 150 },
                            { from: 150, to: 300 },
                            { from: 300, to: 9999 },
                        ];
                        data.market_rate = rate[this.value.profitRate - 1];
                    }
                }

                // 协商价
                if (this.value.agreementPrice) {
                    if (this.value.agreementPrice == 5) {
                        data.agreement_price = {
                            from: this.value.a1,
                            to: this.value.a2,
                        };
                    } else {
                        const section = [
                            { from: 0, to: 200 },
                            { from: 200, to: 500 },
                            { from: 500, to: 1000 },
                            { from: 1000, to: 99999 },
                        ];
                        data.agreement_price =
                            section[this.value.agreementPrice - 1];
                    }
                }

                // 指导价
                if (this.value.guidePrice) {
                    const rate = [
                        { from: 0, to: 200 },
                        { from: 200, to: 500 },
                        { from: 500, to: 1000 },
                        { from: 1000, to: 99999 },
                    ];
                    data.guide_price = rate[this.value.guidePrice - 1];
                }

                // 营销价
                if (this.value.marketingPrice) {
                    const rate = [
                        { from: 0, to: 200 },
                        { from: 200, to: 500 },
                        { from: 500, to: 1000 },
                        { from: 1000, to: 99999 },
                    ];
                    data.activity_price = rate[this.value.marketingPrice - 1];
                }

                // 是否已导入
                if (this.value.self_is_import !== '') {
                    data.is_import = parseInt(this.value.self_is_import);
                }

                // 营销属性
                if (this.value.yxsx) {
                    data[this.value.yxsx] = 1;
                }

                // 折扣 discount
                if (this.value.discount) {
                    if (this.value.discount == 5) {
                        data.discount = {
                            from: Number(this.value.d1),
                            to: Number(this.value.d2)
                        }
                    } else {
                        const rate = [
                            {from: 0, to: 3},
                            {from: 3, to: 5},
                            {from: 5, to: 8},
                            {from: 8, to: 10},
                        ]
                        data.discount = rate[this.value.discount - 1]
                    }
                }

                // 商品名称
                if (this.value.goodsName) {
                    data.title = this.value.goodsName;
                }

                // 店铺名称
                if (this.value.shop_words) {
                    data.supplier_name = this.value.shop_words;
                }

                // 店铺id
                if (this.value.supplier_id) {
                    data.supplier_id = this.value.supplier_id;
                }

                console.log(data);

                this.$post('/product/importGoods', data)
                    .then((res) => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                            this.reset(); // 重置数据
                            // 重置待移除选品库的数据
                            this.selection_list = [];
                            this.centerDialogVisible = false; // 关闭弹窗
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .catch((res) => {
                        console.log(res);
                    });
            } else {
                // 待导入商品
                if (this.selection_list.length == 0) {
                    this.$message('至少选中一个商品');
                    return;
                }
                data = {
                    ids: this.selection_list,
                };

                this.$post('/product/addStorageCenter', data)
                    .then((res) => {
                        if (res.code == 0) {
                            this.$message.success(res.msg);
                            this.reset(); // 重置数据
                            this.selection_list = []; // 清空导入商品的数组
                            this.centerDialogVisible = false; // 关闭弹窗
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                    .catch((res) => {
                        console.log(res);
                    });
            }
        },
        // 搜索
        search() {
            let value = {
                // 筛选数据
                terrace: -1, // 商品来源
                terraceName: '全部',
                fl1: '', // 一级 类目
                fl2: '', // 二级
                fl3: '', // 三级
                grossProfitRate: '', // 毛利率
                profitRate: '', // 利润率
                agreementPrice: '', // 协议价
                guidePrice: '', // 指导价
                marketingPrice: '', // 营销价
                self_is_import: '', // 是否已导入
            };

            if (JSON.stringify(value) == JSON.stringify(this.value)) {
                this.$message('请输入筛选条件');
                return;
            }
            if (this.value.goodsName && this.value.goodsName.trim() == '') {
                this.$message('请重新输入商品名称');
                return;
            }
            if (this.value.shop_words && this.value.shop_words.trim() == '') {
                this.$message('请重新输入商铺名称');
                return;
            }
            if (this.value.supplier_id && this.value.supplier_id.trim() == '') {
                this.$message('请重新输入商铺ID');
                return;
            }

            this.searchbool = true;
            this.getGoodsList(1, 1);
        },
        // 获取 筛选/处理后 的商品数据
        // page 判断是否需要重置页数 1重置 其他不重置
        // sort 判断是否重置筛选条件 1重置 0不重置
        getGoodsList(page, sort,addList=false) {
            let searchForm = {};
            this.isLoading = true; // 加载中
            this.checked = false; // 改变全选状态
            if (addList) {
                this.disableScroll();
            }
            // 0 选品中心，1 我的选品库
            searchForm.is_my_storage_list = 0;

            // 判断是否需要重置页数
            if (page == 1) {
                this.paginationValue.page = 1;
            }

            // 页数
            searchForm.page = parseInt(this.paginationValue.page);
            searchForm.pageSize = parseInt(this.paginationValue.limit);

            // 上下架
            searchForm.is_display = 1;

            // 判断是否重置筛选条件
            if (sort) {
                this.sortForm = {
                    value: '',
                    sort: '1',
                };
                this.sortid = 0;
            }
            switch (this.sortForm.sort) {
                case '1':
                    searchForm.sort = true;
                    break;
                case '2':
                    searchForm.sort = false;
                    break;
            }
            searchForm.type = this.sortForm.value;

            let data; // 定义变量
            // 判断是否为搜索
            if (this.searchbool) {
                // 以搜索

                // 商品来源
                if (this.value.terrace != -1) {
                    console.log(this.value.terrace, 1111);

                    let str = this.merchandiseresources.find(
                        (item) => item.source_name == this.value.terraceName,
                    );
                    console.log(str.type, 1111);
                    if (str.type) {
                        searchForm.gather_supply_id = str.gather_supply_id;
                    } else {
                        searchForm.source = str.source_id;
                    }
                }

                // 类目
                if (this.value.fl1) {
                    searchForm.category_1_id = parseInt(this.value.fl1);
                }
                if (this.value.fl2) {
                    searchForm.category_2_id = parseInt(this.value.fl2);
                }
                if (this.value.fl3) {
                    searchForm.category_3_id = parseInt(this.value.fl3);
                }

                // 毛利率
                if (this.value.grossProfitRate) {
                    if (this.value.grossProfitRate == 5) {
                        searchForm.gross_profit_rate = {
                            from: Number(this.value.g1),
                            to: Number(this.value.g2),
                        };
                    } else {
                        const rate = [
                            { from: 0, to: 35 },
                            { from: 35, to: 50 },
                            { from: 50, to: 75 },
                            { from: 75, to: 9999 },
                        ];
                        searchForm.gross_profit_rate =
                            rate[this.value.grossProfitRate - 1];
                    }
                }

                // 利润率
                if (this.value.profitRate) {
                    if (this.value.profitRate == 5) {
                        searchForm.market_rate = {
                            from: Number(this.value.p1),
                            to: Number(this.value.p2),
                        };
                    } else {
                        const rate = [
                            { from: 0, to: 50 },
                            { from: 50, to: 150 },
                            { from: 150, to: 300 },
                            { from: 300, to: 9999 },
                        ];
                        searchForm.market_rate =
                            rate[this.value.profitRate - 1];
                    }
                }

                // 协商价
                if (this.value.agreementPrice) {
                    if (this.value.agreementPrice == 5) {
                        searchForm.agreement_price = {
                            from: Number(this.value.a1),
                            to: Number(this.value.a2),
                        };
                    } else {
                        const section = [
                            { from: 0, to: 200 },
                            { from: 200, to: 500 },
                            { from: 500, to: 1000 },
                            { from: 1000, to: 99999 },
                        ];
                        searchForm.agreement_price =
                            section[this.value.agreementPrice - 1];
                    }
                }

                // 指导价
                if (this.value.guidePrice) {
                    const rate = [
                        { from: 0, to: 200 },
                        { from: 200, to: 500 },
                        { from: 500, to: 1000 },
                        { from: 1000, to: 99999 },
                    ];
                    searchForm.guide_price = rate[this.value.guidePrice - 1];
                }

                // 折扣
                if (this.value.discount) {
                    if (this.value.discount == 5) {
                        searchForm.discount = {
                            from: Number(this.value.d1),
                            to: Number(this.value.d2)
                        }
                    } else {
                        const rate = [
                            {from: 0, to: 3},
                            {from: 3, to: 5},
                            {from: 5, to: 8},
                            {from: 8, to: 10},
                        ]
                        searchForm.discount = rate[this.value.discount - 1]
                    }
                }

                // 营销价
                if (this.value.marketingPrice) {
                    const rate = [
                        { from: 0, to: 200 },
                        { from: 200, to: 500 },
                        { from: 500, to: 1000 },
                        { from: 1000, to: 99999 },
                    ];
                    searchForm.activity_price =
                        rate[this.value.marketingPrice - 1];
                }

                // 是否已导入
                if (this.value.self_is_import !== '') {
                    searchForm.is_import = parseInt(this.value.self_is_import);
                }

                // 营销属性
                if (this.value.yxsx) {
                    searchForm[this.value.yxsx] = 1;
                }

                // 商品名称
                if (this.value.goodsName) {
                    searchForm.title = this.value.goodsName.trim();
                }

                // 店铺名称
                if (this.value.shop_words) {
                    searchForm.supplier_name = this.value.shop_words.trim();
                }

                // 店铺id
                if (this.value.supplier_id) {
                    searchForm.supplier_id = parseInt(this.value.supplier_id);
                }

                data = searchForm;
                this.$post('/product/getStorageCenterList', data)
                    .then((res) => {
                        if (addList) {
                            this.enableScroll();
                        }
                        this.isLoading = false; // 加载完成
                        this.paginationValue.total = res.data.total;
                        this.shop = addList === true ? this.shop.concat(res.data.list) : res.data.list || [];
                        this.hasMore = res.data.total > this.shop.length;

                        this.my_center_total = res.data.my_center_total;
                        if (page == 1 && sort == 1) {
                            if (res.code == 0) {
                                this.$message.success(res.msg);
                            } else {
                                this.$message.error(res.msg);
                            }
                        }
                    })
                    .catch((res) => {
                        this.isLoading = false; // 加载完成
                        if (addList) {
                            this.enableScroll();
                        }
                    });
            } else {
                this.isLoading = false;
                // 未搜索
                data = searchForm;
                this.$post('/product/getStorageCenterList', data)
                    .then((res) => {
                        if (addList) {
                            this.enableScroll();
                        }
                        this.paginationValue.total = res.data.total;
                        this.shop = addList === true ? this.shop.concat(res.data.list) : res.data.list ?? [];
                        this.hasMore = res.data.total > this.shop.length;
                        
                        this.my_center_total = res.data.my_center_total;
                    })
                    .catch((res) => {
                        if (addList) {
                            this.enableScroll();
                        }
                        console.log(res);
                    });
            }


        },
        // 重置按钮
        resetfun() {
            this.$message.success('重置成功');
            this.reset();
        },
        // 选品AI
        selectAi() {
            this.$refs.selectDialog.visible = true
            this.$refs.selectDialog.getSetting()
        },
        // 获取到的AI选品数据
        async getSelectAiList(item) {
            this.value = {
                fl1: '',
                fl2: '',
                fl3: '',
                grossProfitRate: '',
                g1: '',
                g2: '',
                profitRate: '',
                p1: '',
                p2: '',
                agreementPrice: '',
                a1: '',
                a2: '',
                discount: null,
                d1: '',
                d2: '',
                guidePrice: '',
                marketingPrice: '',
                goodsName: '',
                terrace: -1,
                terraceName: '全部'
            }
            // 一级分类
            if (item.category1) {
                const matchedCategory = this.category1.find(items => item.category1 == items.name);
                if (matchedCategory) {
                    this.value.fl1 = matchedCategory.id
                    await this.getCategory(2,this.value.fl1)                               
                }
            }
            // 二级分类
            if (item.category2) {        
                const matchedCategory = this.category2.find(items => items.name && items.name.includes(item.category2))                
                if (matchedCategory) {
                    this.value.fl2 = matchedCategory ? matchedCategory.id : '';
                    await this.getCategory(3, this.value.fl2); // 加载三级类目
                }
            }
            // 三级分类
            if (item.category3) {
                const matchedCategory = this.category3.find(items => items.name && items.name.includes(item.category3))
                if (matchedCategory) {
                    this.value.fl3 = matchedCategory ? matchedCategory.id : '';
                }
                
            }
            // 毛利率
            if (item.gross_profit_rate) {
                this.value.grossProfitRate = 5
                this.value.g1 = item.gross_profit_rate.from
                this.value.g2 = item.gross_profit_rate.to
            }
            // 利润率
            if (item.market_rate) {
                this.value.profitRate = 5
                this.value.p1 = item.market_rate.from
                this.value.p2 = item.market_rate.to
            }
            // 协议价
            if (item.agreement_price) {
                this.value.agreementPrice = 5
                this.value.a1 = item.agreement_price.from
                this.value.a2 = item.agreement_price.to
            }
            // 折扣
            if (item.discount) {
                this.value.discount = 5
                this.value.d1 = item.discount.from
                this.value.d2 = item.discount.to
            }
            // 指导价
            if (item.guide_price) {
                switch (item.guide_price.to) {
                    case 200:
                        this.value.guidePrice = 1
                        break;
                    case 500:
                        this.value.guidePrice = 2
                        break;
                    case 1000:
                        this.value.guidePrice = 3
                        break;
                    case item.guide_price.to > 1000:
                        this.value.guidePrice = 4
                        break;
                    default:
                        break;
                }
            }
            // 营销价
            if (item.activity_price) {
                switch (item.activity_price.to) {
                    case 200:
                        this.value.marketingPrice = 1
                        break;
                    case 500:
                        this.value.marketingPrice = 2
                        break;
                    case 1000:
                        this.value.marketingPrice = 3
                        break;
                    case item.guide_price.to > 1000:
                        this.value.marketingPrice = 4
                        break;
                    default:
                        break;
                }
            }
            // 商品名称
            if (item.title) {
                this.value.goodsName = item.title
            }
        },
        // 重置筛选数据
        reset() {
            this.searchbool = false; // 改为未搜索

            // 重置分页
            this.paginationValue = {
                page: 1,
                limit: 25,
                total: 0,
                currentpage: 1,
            };

            // 重置排序
            this.sortForm = {
                value: '',
                sort: '1',
            };
            this.sortid = 0;

            // 重置 筛选条件
            this.value = {
                // 筛选数据
                terrace: -1, // 商品来源
                terraceName: '全部',
                fl1: '', // 一级类目值
                fl2: '', // 二级
                fl3: '', // 三级
                grossProfitRate: '', // 毛利率
                profitRate: '', // 利润率
                agreementPrice: '', // 协议价
                guidePrice: '', // 指导价
                marketingPrice: '', // 营销价
                self_is_import: '', // 是否已导入
            };

            // 重置类目表
            this.category2 = [];
            this.category3 = [];
            this.terraceKEY = '-1-0--1';

            // 重新调用获取商品
            this.getGoods();
        },
        // 加入选品
        addOption(res) {
            let a = false;
            let val = this.selection_list.find((item) => item == res);
            if (!val) {
                this.selection_list.push(res);
            }

            let shop = this.shop.filter((item) => item.is_import != 1);
            // 判断 是否 是全选
            for (let index = 0; index < shop.length; index++) {
                if (
                    undefined ==
                    this.selection_list.find((item) => item == shop[index].id)
                ) {
                    a = true;
                    break;
                }
            }
            if (!a) {
                this.checked = true;
            }
        },
        // 移除选品
        delOption(res) {
            let val = this.selection_list.find((item) => item == res);
            this.checked = false;
            if (val) {
                this.selection_list = this.selection_list.filter(
                    (item) => item != res,
                );
            }
        },
        // 获取类目
        // level:几级类目 res:parent_id
        async getCategory(level, pid) {
            let params = {
                parent_id: pid,
            };
            let res = await this.$get('/category/getCategory', params)
            if (res.code === 0) {
                switch (level) {
                    case 1:
                        this.category1 = res.data.list;
                        
                        break;
                    case 2:
                        this.category2 = []
                        this.value.fl2 = ''
                        this.category2 = res.data.list;
                        this.category3 = []
                        this.value.fl3 = ''
                        break;
                    case 3:
                        this.category3 = []
                        this.value.fl3 = ''
                        this.category3 = res.data.list;
                        break;
                }
            }
        },
        // 获取商品
        getGoods() {
            let data = {};
            data.page = parseInt(this.paginationValue.page);
            data.PageSize = parseInt(this.paginationValue.limit);
            data.is_my_storage_list = 0;
            // 上下架
            data.is_display = 1;
            this.$post('/product/getStorageCenterList', data)
                .then((res) => {
                    console.log(res);
                    this.paginationValue.total = res.data.total;
                    this.shop = res.data.list;
                    this.my_center_total = res.data.my_center_total;
                })
                .catch((res) => {
                    console.log(res);
                });
        },
        // 获取商品来源
        goodSource() {
            this.merchandiseresources = [
                {
                    gather_supply_id: -1,
                    source_id: -1,
                    source_name: '全部',
                    type: 1,
                },
                {
                    gather_supply_id: 0,
                    source_id: -1,
                    source_name: '平台自营',
                    type: 1,
                },
            ];
            this.$get('/application/getMyGatherSupplyAndSource')
                .then((res) => {
                    this.merchandiseresources =
                        this.merchandiseresources.concat(res.data);
                })
                .catch((res) => {
                    console.log(res);
                });
        },
    },
    created() {
        // console.log(this.$fn.changeMoneyF2Y(-111));
        this.goodSource(); // 获取商品来源
        this.getCategory(1, 0); // 获取类目
        this.getGoods(); // 获取商品
    },
};
</script>

<style lang="scss" scoped>
.screening-card {
    width: 1200px;
    margin: 15px auto 0;
    font-size: 14px;
    background-color: #fff;
    border-radius: 12px;
    padding: 10px 10px;
    box-sizing: border-box;
}

.tag-name {
    width: 80px;
    height: 40px;
    display: flex;
    align-items: center;
}

.merchandise-resources {
    display: flex;
    flex-wrap: wrap;

    .merchandise-resources-style {
        margin-right: 30px;
        height: 40px;
        display: flex;
        align-items: center;
        cursor: pointer;
    }

    .merchandise-resources-col {
        color: #f42121;
    }
}

.merchandise-screen {
    display: flex;
    margin-top: 20px;
    flex-wrap: wrap;

    .merchandise-screen-select {
        width: 258px;
        height: 40px;
        border-radius: 8px;
        border: 1px solid #dee0e5;
        margin-right: 15px;
        display: flex;

        .select-name {
            padding-left: 16px;
            font-size: 14px;
            height: 40px;
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .selectc {
            width: 70px;
        }

        .selecl-sty {
            width: 192px;
            border: none;
        }

        .selectcs {
            width: 172px;
        }

        ::v-deep .el-input__inner {
            border: 0;
            border-radius: 8px;
        }
    }

    .merchandise-screen-select-eles {
        width: 400px;
        height: 40px;
        margin-right: 15px;
        display: flex;
        justify-content: center;

        .select-name {
            padding: 0 16px;
            font-size: 14px;
            height: 40px;
            width: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .input-sty {
            width: 140px;
            height: 40px;
        }

        .input-bin {
            width: 140px;
            height: 40px;
            display: flex;
            border: 1px solid #dee0e5;
            border-radius: 8px;

            .input {
                width: 100px;
                height: 40px;
            }

            ::v-deep .el-input__inner {
                border: 0;
                border-radius: 8px;
            }

            .bin {
                width: 40px;
                height: 40px;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 0 8px 8px 0;
                background-color: #f5f7fa;
            }
        }

        .line {
            font-size: 24px;
            padding: 0 5px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
    }

    .but-true {
        background-color: #f42121;
        color: #fff;
    }

    button {
        width: 100px;
        height: 40px;
        border-radius: 8px;
    }

    .selection-num {
        height: 40px;
        display: flex;
        align-items: center;
        color: #f42121;
        font-size: 14px;
        margin-left: 12px;
        cursor: pointer;
    }
}

.merchandise-screen-good {
    display: flex;
    margin-top: 20px;
    flex-wrap: wrap;

    .tag-name-good {
        width: 80px;
        height: 100px;
    }

    .f {
        width: calc(100% - 80px);
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap;

        .merchandise-screen-select {
            width: 258px;
            height: 40px;
            border-radius: 8px;
            border: 1px solid #dee0e5;
            margin-right: 15px;
            margin-bottom: 20px;
            display: flex;

            .select-name {
                padding-left: 16px;
                font-size: 14px;
                height: 40px;
                width: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .selectc {
                width: 70px;
            }

            .selecl-sty {
                width: 192px;
                border: none;
            }

            .selectcs {
                width: 172px;
            }

            ::v-deep .el-input__inner {
                border: 0;
                border-radius: 8px;
            }
        }

        .merchandise-screen-select-eles {
            width: 350px;
            height: 40px;
            margin-right: 15px;
            margin-bottom: 20px;
            display: flex;
            justify-content: center;

            .select-name {
                padding: 0 16px;
                font-size: 14px;
                height: 40px;
                width: 50px;
                display: flex;
                justify-content: center;
                align-items: center;
            }

            .input-sty {
                width: 115px;
                height: 40px;
            }

            .input-bin {
                width: 115px;
                height: 40px;
                display: flex;
                border: 1px solid #dee0e5;
                border-radius: 8px;

                .input {
                    width: 75px;
                    height: 40px;
                }

                ::v-deep .el-input__inner {
                    border: 0;
                    border-radius: 8px;
                }

                .bin {
                    width: 40px;
                    height: 40px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    border-radius: 0 8px 8px 0;
                    background-color: #f5f7fa;
                }
            }

            .line {
                font-size: 24px;
                padding: 0 5px;
                display: flex;
                justify-content: center;
                align-items: center;
            }
        }
    }
}

.check-all {
    width: 1200px;
    margin: 0 auto;
    height: 65px;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .check-all-left {
        display: flex;

        div {
            margin-right: 30px;
            cursor: pointer;
        }
    }

    .check-all-right {
        display: flex;

        div {
            margin-left: 30px;
            cursor: pointer;
        }
    }
}

.commodity {
    width: 1200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    grid-template-rows: repeat(1, 1fr);
    gap: 10px;

    .commodity-card {
        width: 225px;
        height: 338px;
        background-color: #fff;
        border-radius: 12px;
        margin-bottom: 15px;
        overflow: hidden;
        cursor: pointer;

        .commodity-card-true {
            display: block;

            .commodityimg {
                width: 225px;
                height: 225px;
            }

            .commodity-name {
                height: 38px;
                margin: 0 8px;
                font-size: 14px;
                margin-top: 12px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
            }

            .price {
                display: flex;
                margin-top: 15px;

                .price-box {
                    width: 112px;
                    height: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .price-name {
                        font-size: 12px;
                        color: #a4a4a4;
                    }

                    .price-num {
                        font-size: 14px;
                        color: #f42121;
                    }
                }

                .fen {
                    border-right: 1px #dee0e5 solid;
                }
            }
        }

        .commodity-card-hover {
            display: none;

            .commodityimg-hover {
                width: 225px;
                height: 189px;
                box-sizing: border-box;
                overflow: hidden;
                position: relative;

                img {
                    width: 225px;
                    height: auto;
                    position: absolute;
                    bottom: 0;
                    object-fit: cover;
                }
            }

            .commodityimg-hover-button {
                width: 225px;
                height: 32px;
                background-color: #f42121;
                opacity: 0.6;
                display: flex;
                align-items: center;
                color: #fff;
                position: absolute;
                bottom: 0px;

                div {
                    width: 112px;
                    height: 23px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                }
            }

            .commodity-name {
                height: 38px;
                margin: 0 8px;
                font-size: 14px;
                margin-top: 12px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;
            }

            .price {
                display: flex;
                margin-top: 15px;

                .price-box {
                    width: 114px;
                    height: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .price-name {
                        font-size: 12px;
                        color: #a4a4a4;
                    }

                    .price-num {
                        font-size: 14px;
                        color: #f42121;
                    }
                }

                .fen {
                    border-right: 1px #dee0e5 solid;
                }
            }

            .det-price {
                width: 100%;
                height: 46px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                background-color: #f7f7f7;
                margin-top: 8px;
                border-radius: 8px;

                .det-price-box {
                    width: 25%;
                    height: 46px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .det-price-name {
                        font-size: 11px;
                        color: #a4a4a4;
                    }

                    .det-price-num {
                        font-size: 11px;
                        color: #333333;
                    }
                }
            }
        }
    }

    .commodity-card:hover {
        border: 1px solid #f42121;
        box-sizing: border-box;
    }

    .commodity-card:hover .commodity-card-true {
        display: none;
    }

    .commodity-card:hover .commodity-card-hover {
        display: block;
    }
}

.paging {
    width: 1200px;
    margin: 0 auto 20px;
    display: flex;
    justify-content: end;
}

// 弹框属性
::v-deep .el-dialog .el-dialog__header {
    border: none;
}

::v-deep .el-dialog__title {
    font-size: 26px;
    font-weight: bold;
}

::v-deep .el-dialog,
.el-pager {
    width: 660px;
    // height: 236px;
    border-radius: 16px 16px 16px 16px;
}

.el-dialog__wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
}

.title-con {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 20px;
    font-weight: bold;
}

.title-text {
    width: 100%;
    display: flex;
    justify-content: center;
    font-size: 12px;
    margin-top: 12px;
}

.title-button {
    display: flex;
    width: 276px;
    margin: 0 auto;
    justify-content: space-between;
    margin-top: 30px;

    button {
        width: 130px;
        height: 40px;
        border-radius: 8px;
    }

    .ackbutton {
        background-color: #f42121;
        color: #ffffff;
    }
}

// 单选框样式
::v-deep .el-checkbox__input.is-checked .el-checkbox__inner,
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #f42121;
    border-color: #f42121;
}

::v-deep .el-checkbox__inner:hover {
    border-color: #f42121;
}

::v-deep .el-checkbox__inner:focus {
    border-color: #f42121;
}

::v-deep .el-checkbox__input.is-checked + .el-checkbox__label {
    color: #f42121;
}
</style>
