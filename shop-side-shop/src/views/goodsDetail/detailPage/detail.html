<div class="bgw pt20 pb20">
    <template v-if="Object.keys(product).length > 0">
        <el-collapse-transition>
            <div v-if="headIshow" class="detailHead">
                <div class="inner f fac head-box">
                    <div class="left-box">
                        <div class="f fac shop-top-box fjc">
                            <div class="shop-logo-box">
                                <img :src="supplier.shop_logo" class="img100" />
                            </div>
                            <div class="shop-ta-box">
                                <p class="shop-title-p">{{ supplier.name }}</p>
                                <p class="shop-addrs-p">
                                    {{ supplier.province }} {{ supplier.city }}
                                    {{ supplier.county }}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="right-box f1">
                        <el-tabs
                            v-model="goodsActiveName"
                            class="mt10"
                            @tab-click="handleTabsClick"
                        >
                            <el-tab-pane label="商品信息" name="1">
                            </el-tab-pane>
                            <el-tab-pane label="商品评论" name="2">
                            </el-tab-pane>
                            <el-tab-pane label="商品资质" name="3">
                            </el-tab-pane>
                        </el-tabs>
                    </div>
                </div>
            </div>
        </el-collapse-transition>
        <div class="inner goodsDetail-box">
            <!-- 商品图片/规格部分 -->
            <div class="f goods-head-box">
                <div class="m-swiper-box">
                    <!-- 大图 -->
                    <div class="big-img-box">
                        <div v-if="product.is_display === 0" class="img-mask">
                            <span class="img-mask-text"> 商品已下架 </span>
                        </div>
                        <!--                    <img :src="bigImgUrl" class="img100">-->
                        <m-video
                            ref="mvideo"
                            :src="bigImgUrl"
                            :imgType="bigImgType"
                        ></m-video>
                    </div>
                    <!-- 缩略图部分 -->
                    <div class="slt-box f fac fjsb">
                        <!-- 左侧按钮 -->
                        <a href="javascript:;" @click="tabBtn('last')">
                            <div class="tab-btn">
                                <i class="el-icon-arrow-left"></i>
                            </div>
                        </a>
                        <!-- 缩略图 -->
                        <div class="imgs-box">
                            <div class="f" ref="imgBox">
                                <a
                                    href="javascript:;"
                                    v-for="(item,index) in sltList"
                                    :key="item.id"
                                    @click="handleClickTab(item,index)"
                                >
                                    <div
                                        class="img-item"
                                        :class="imgIndex === index ? 'imgActive' : ''"
                                    >
                                        <video
                                            v-if="item.type === 2"
                                            class="img100"
                                            :src="item.src"
                                        ></video>
                                        <img
                                            v-else
                                            :src="item.src"
                                            class="img100"
                                        />
                                    </div>
                                </a>
                            </div>
                        </div>
                        <!-- 右侧按钮 -->
                        <a href="javascript:;" @click="tabBtn('next')">
                            <div class="tab-btn">
                                <i class="el-icon-arrow-right"></i>
                            </div>
                        </a>
                    </div>
                </div>
                <!-- 商品规格部分 -->
                <div class="f1 goods-head-right-box">
                    <!-- 商品标题 -->
                    <p class="goods-title">
                        {{ product.title }}
                        <span v-show="checkSkuText"> [{{checkSkuText}}]</span>
                    </p>
                    <p class="goods-desc">{{ product.desc }}</p>
                    <!-- 价格部分 -->
                    <div class="price-box f fac fjsb">
                        <div>
                            <p>
                                <span class="color-red" v-if="getFramework.page_setup_setting.details_setting.is_super_wholesal_price === 0">
                                    <span
                                        v-if="isLogin && $store.state.userLevelPrice.userLevelPriceTitle"
                                        class="money-span"
                                        ><span class="symbol-span">￥</span>{{
                                        $store.state.userLevelPrice.userLevelPriceTitle
                                        }}</span>
                                    <span
                                        v-else-if="isLogin && product.min_price==product.max_price"
                                        class="money-span"
                                        ><span class="symbol-span">￥</span>{{
                                        product.min_price | formatF2Y }}</span
                                    >
                                    <span
                                        v-else-if="isLogin && specificationID !== 0"
                                        class="money-span"
                                    >
                                        <span class="symbol-span">￥</span>{{ sku_price
                                        |formatF2Y }}
                                    </span>
                                    <span v-else-if="isLogin" class="money-span"
                                        ><span class="symbol-span">￥</span>{{
                                        product.min_price | formatF2Y }}<span
                                            class="x-span"
                                            >~</span
                                        ><span class="symbol-span">￥</span>{{
                                        product.max_price |formatF2Y }}</span
                                    >
                                    <el-button
                                        v-else
                                        type="text"
                                        class="color-red"
                                        @click="$router.push('login')"
                                        >登录后查看
                                    </el-button>
                                    <span style="font-size: 12px;">
                                        {{
                                            getFramework.page_setup_setting.language_setting.super_wholesal_price
                                            === '' ? '超级批发价' :
                                            getFramework.page_setup_setting.language_setting.super_wholesal_price
                                        }}
                                    </span>
                                </span>
                                <span style="margin-left: 16px;" v-if="getFramework.page_setup_setting.details_setting.is_price === 0">
                                    <span
                                        v-if="isLogin && $store.state.userLevelPrice.userLevelPriceTitle"
                                        class="money-span-black"
                                        ><span class="symbol-span">￥</span
                                        >{{
                                        $store.state.userLevelPrice.userLevelPriceTitle
                                        }}</span
                                    >
                                    <span
                                        v-else-if="isLogin && product.min_normal_price==product.max_normal_price"
                                        class="money-span-black"
                                        ><span class="symbol-span">￥</span
                                        >{{ product.min_normal_price
                                        |formatF2Y }}</span
                                    >
                                    <span
                                        v-else-if="isLogin && specificationID !== 0"
                                        class="money-span-black"
                                    >
                                        <span class="symbol-span">￥</span
                                        >{{ sku_normal_price |formatF2Y }}
                                    </span>
                                    <span
                                        v-else-if="isLogin"
                                        class="money-span-black"
                                        ><span class="symbol-span">￥</span
                                        >{{ product.min_normal_price |
                                        formatF2Y }}<span class="x-span"
                                            >~</span
                                        ><span class="symbol-span">￥</span
                                        >{{ product.max_normal_price
                                        |formatF2Y }}</span
                                    >
                                    <el-button
                                        v-else
                                        type="text"
                                        class="color-red"
                                        @click="$router.push('login')"
                                        >登录后查看
                                    </el-button>
                                    <span style="font-size: 12px;">
                                        {{
                                            getFramework.page_setup_setting.language_setting.price
                                            === '' ? '批发价' :
                                            getFramework.page_setup_setting.language_setting.price
                                        }}
                                    </span>
                                </span>
                            </p>
                            <p style="margin-top: 10px;font-size: 12px;" v-if="getFramework.page_setup_setting.details_setting.is_suggested_retail_price === 0">
                                {{
                                    getFramework.page_setup_setting.language_setting.suggested_retail_price
                                    === '' ? '建议零售价' :
                                    getFramework.page_setup_setting.language_setting.suggested_retail_price
                                }}:
                                <span
                                    v-if="isLogin && $store.state.userLevelPrice.userLevelPriceTitle"
                                    class="money-span-black"
                                    ><span class="symbol-span">￥</span>{{
                                    $store.state.userLevelPrice.userLevelPriceTitle
                                    }}</span
                                >
                                <span
                                    v-else-if="product.min_origin_price==product.max_origin_price"
                                    class="money-span-black"
                                    ><span class="symbol-span">￥</span>{{
                                    product.origin_price |formatF2Y }}</span
                                >
                                <span
                                    v-else-if="specificationID !== 0"
                                    class="money-span-black"
                                >
                                    <span class="symbol-span">￥</span>{{
                                    sku_origin_price |formatF2Y }}
                                </span>
                                <span v-else class="money-span-black"
                                    ><span class="symbol-span">￥</span>{{
                                    product.min_origin_price | formatF2Y }}<span
                                        class="x-span"
                                        >~</span
                                    ><span class="symbol-span">￥</span>{{
                                    product.max_origin_price |formatF2Y }}
                                </span>
                            </p>
                            <div class="f fac mt20" style="font-size: 12px;">
                                <div style="min-width: 140px;margin-right: 10px;">
                                    <span>利润率：</span>
                                    <span class="color-red" v-if="sku_profit_rate !== null">{{ sku_profit_rate }}%</span>
                                    <span class="color-red" v-else-if="product.min_profit_rate === product.max_profit_rate">{{ product.min_profit_rate }}%</span>
                                    <span class="color-red" v-else>{{ product.min_profit_rate }}% ~ {{ product.max_profit_rate }}%</span>
                                </div>
                                <div>
                                    <span>毛利率：</span>
                                    <span v-if="sku_gross_profit_rate !== null">{{ sku_gross_profit_rate }}%</span>
                                    <span v-else-if="product.min_gross_profit_rate === product.max_gross_profit_rate">{{ product.min_gross_profit_rate }}%</span>
                                    <span v-else>{{ product.min_gross_profit_rate }}% ~ {{ product.max_gross_profit_rate }}%</span>
                                </div>
                            </div>
                            <div class="f fac" style="font-size: 12px;margin-top: 10px;">
                                <div style="min-width: 140px;margin-right: 10px;">
                                    <span>折扣：</span>
                                    <span class="color-red" v-if="sku_min_discount !== null">{{ sku_min_discount | formatF2Y }}折</span>
                                    <span class="color-red" v-else-if="product.min_discount === product.max_discount">{{ product.max_discount | formatF2Y }}折</span>
                                    <span class="color-red" v-else>{{ product.min_discount | formatF2Y }}折 ~ {{ product.max_discount | formatF2Y }}折</span>

                                </div>
                                <div>
                                    <span>折扣率：</span>
                                    <span v-if="sku_min_discount_ratio !== null">{{ sku_min_discount_ratio | formatF2Y }}%</span>
                                    <span v-else-if="product.max_discount_ratio === product.min_discount_ratio">{{$fn.changeMoneyF2Y(product.min_discount_ratio)}}%</span>
                                    <span v-else>{{product.min_discount_ratio | formatF2Y}}% ~ {{ product.max_discount_ratio | formatF2Y }}%</span>
                                </div>
                            </div>
                        </div>
                        <div class="f fac price-right">
                            <div class="f fac">
                                <i class="iconfont icon-pc_line_hot"></i>
                                <div>
                                    <p>热销(件)</p>
                                    <p>{{ product.sales }}</p>
                                </div>
                            </div>
                            <div class="f fac">
                                <i
                                    class="iconfont icon-pc_line_givealike"
                                ></i>
                                <div>
                                    <p>好评率</p>
                                    <p class="text-center">
                                        {{ feedback_rate ? feedback_rate +
                                        '%' : '0%' }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- <div class="price-box">
                        <p
                            v-if="getFramework.page_setup_setting.details_setting.is_super_wholesal_price === 0"
                        >
                            {{
                            getFramework.page_setup_setting.language_setting.super_wholesal_price
                            === '' ? '超级批发价' :
                            getFramework.page_setup_setting.language_setting.super_wholesal_price
                            }}:
                            <span
                                v-if="isLogin && $store.state.userLevelPrice.userLevelPriceTitle"
                                class="money-span"
                                ><span class="symbol-span">￥</span>{{
                                $store.state.userLevelPrice.userLevelPriceTitle
                                }}</span
                            >
                            <span
                                v-else-if="isLogin && product.min_price==product.max_price"
                                class="money-span"
                                ><span class="symbol-span">￥</span>{{
                                product.min_price | formatF2Y }}</span
                            >
                            <span
                                v-else-if="isLogin && specificationID !== 0"
                                class="money-span"
                            >
                                <span class="symbol-span">￥</span>{{ sku_price
                                |formatF2Y }}
                            </span>
                            <span v-else-if="isLogin" class="money-span"
                                ><span class="symbol-span">￥</span>{{
                                product.min_price | formatF2Y }}<span
                                    class="x-span"
                                    >~</span
                                ><span class="symbol-span">￥</span>{{
                                product.max_price |formatF2Y }}</span
                            >
                            <el-button
                                v-else
                                type="text"
                                class="color-red"
                                @click="$router.push('login')"
                                >登录后查看
                            </el-button>
                        </p>
                        <div class="f fac fjsb">
                            <div class="f fab">
                                <template
                                    v-if="getFramework.page_setup_setting.details_setting.is_price === 0"
                                >
                                    <p class="text-j">
                                        {{
                                        getFramework.page_setup_setting.language_setting.price
                                        === '' ? '批发价' :
                                        getFramework.page_setup_setting.language_setting.price
                                        }}:
                                        <span
                                            v-if="isLogin && $store.state.userLevelPrice.userLevelPriceTitle"
                                            class="money-span"
                                            ><span class="symbol-span">￥</span
                                            >{{
                                            $store.state.userLevelPrice.userLevelPriceTitle
                                            }}</span
                                        >
                                        <span
                                            v-else-if="isLogin && product.min_normal_price==product.max_normal_price"
                                            class="money-span"
                                            ><span class="symbol-span">￥</span
                                            >{{ product.min_normal_price
                                            |formatF2Y }}</span
                                        >
                                        <span
                                            v-else-if="isLogin && specificationID !== 0"
                                            class="money-span"
                                        >
                                            <span class="symbol-span">￥</span
                                            >{{ sku_normal_price |formatF2Y }}
                                        </span>
                                        <span
                                            v-else-if="isLogin"
                                            class="money-span"
                                            ><span class="symbol-span">￥</span
                                            >{{ product.min_normal_price |
                                            formatF2Y }}<span class="x-span"
                                                >~</span
                                            ><span class="symbol-span">￥</span
                                            >{{ product.max_normal_price
                                            |formatF2Y }}</span
                                        >
                                        <el-button
                                            v-else
                                            type="text"
                                            class="color-red"
                                            @click="$router.push('login')"
                                            >登录后查看
                                        </el-button>
                                    </p>
                                </template>
                            </div>
                            <div class="f fac price-right">
                                <div class="f fac">
                                    <i class="iconfont icon-pc_line_hot"></i>
                                    <div>
                                        <p>热销(件)</p>
                                        <p>{{ product.sales }}</p>
                                    </div>
                                </div>
                                <div class="f fac">
                                    <i
                                        class="iconfont icon-pc_line_givealike"
                                    ></i>
                                    <div>
                                        <p>好评率</p>
                                        <p class="text-center">
                                            {{ feedback_rate ? feedback_rate +
                                            '%' : '0%' }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <p
                            v-if="getFramework.page_setup_setting.details_setting.is_suggested_retail_price === 0"
                            class="text-j"
                        >
                            {{
                            getFramework.page_setup_setting.language_setting.suggested_retail_price
                            === '' ? '建议零售价' :
                            getFramework.page_setup_setting.language_setting.suggested_retail_price
                            }}:
                            <span
                                v-if="isLogin && $store.state.userLevelPrice.userLevelPriceTitle"
                                class="money-span"
                                ><span class="symbol-span">￥</span>{{
                                $store.state.userLevelPrice.userLevelPriceTitle
                                }}</span
                            >
                            <span
                                v-else-if="product.min_origin_price==product.max_origin_price"
                                class="money-span"
                                ><span class="symbol-span">￥</span>{{
                                product.origin_price |formatF2Y }}</span
                            >
                            <span
                                v-else-if="specificationID !== 0"
                                class="money-span"
                            >
                                <span class="symbol-span">￥</span>{{
                                sku_origin_price |formatF2Y }}
                            </span>
                            <span v-else class="money-span"
                                ><span class="symbol-span">￥</span>{{
                                product.min_origin_price | formatF2Y }}<span
                                    class="x-span"
                                    >~</span
                                ><span class="symbol-span">￥</span>{{
                                product.max_origin_price |formatF2Y }}</span
                            >
                        </p>
                        <p
                            v-if="product.min_buy_qty && product.min_buy_qty > 1"
                            class="text-j"
                            style="margin-top: 10px"
                        >
                            起批量: {{ product.min_buy_qty }}{{ product.unit }}
                        </p>
                        <div v-if="false" class="mt10 f fac">
                            <p class="text-j">活动</p>
                            <p class="discounts-p">本店部分商品满199元或30</p>
                        </div>
                    </div> -->

                    <!-- 物流 -->
                    <div class="logistics-box f">
                        <p class="text-j">物流</p>
                        <div class="logistics-wrapper f fac">
                            <span>配送<span class="ml5 mr5">至</span></span>
                            <el-popover
                                ref="popoverRef"
                                placement="bottom"
                                width="860"
                                trigger="hover"
                            >
                                <div class="area-wrapper">
                                    <div class="f fjsb fac mb_15">
                                        <p style="color: #333333">常用地址</p>
                                        <i
                                            v-if="isAddressShow"
                                            @click="addressListShow"
                                            class="iconfont icon-icon_up1 pointer"
                                        ></i>
                                        <i
                                            v-else
                                            @click="addressListShow"
                                            class="iconfont icon-icon_down1 pointer"
                                        ></i>
                                    </div>
                                    <div
                                        v-for="item in addressList"
                                        :key="item.id"
                                        class="mt_10 pointer f fac"
                                        :class="{ 'selecteColor': item.id === selectId }"
                                        @click="chooseAddress(item)"
                                    >
                                        <div class="address-container">
                                            {{ item.realname }}
                                        </div>
                                        <div class="ml_10">
                                            {{ item.province }}{{ item.city }}{{
                                            item.county }}{{ item.town }}{{
                                            item.detail }}
                                        </div>
                                    </div>
                                    <el-divider class="mtb20"></el-divider>
                                    <p style="color: #333333">物流费用计算</p>
                                    <div
                                        v-for="(item,index) in areaList"
                                        :key="index"
                                        class="row-div mt20"
                                    >
                                        <div class="f">
                                            <div
                                                class="area-item"
                                                v-for="(item2,index2) in item"
                                                :key="item2.name"
                                            >
                                                <span
                                                    class="pointer"
                                                    :class="index === areaIndex && index2 === areaIndex2 ? 'active' : ''"
                                                    @click="handleProvinceClick(index,index2,item2.id)"
                                                    >{{ item2.name }}</span
                                                >
                                            </div>
                                        </div>
                                        <div
                                            class="city-div mt10 f fw"
                                            v-if="index === areaIndex"
                                        >
                                            <p
                                                class="pointer mr10 mt10"
                                                :class="cIndex === cityIndex ? 'active' : ''"
                                                v-for="(city,cIndex) in cityList"
                                                :key="city.name"
                                                @click="handleCityClick(cIndex,city)"
                                            >
                                                {{ city.name }}
                                            </p>
                                        </div>
                                    </div>
                                    <div class="bottom-wrapper mt20">
                                        <div
                                            class="purchase-wrapper f fac fjsb"
                                        >
                                            <div class="purchase-l color-gray">
                                                订购数量:
                                                <el-input-number
                                                    class="ml10"
                                                    size="mini"
                                                    v-model.number="logistics.qty"
                                                    :min="min"
                                                    @change="getFreight"
                                                ></el-input-number>
                                            </div>
                                            <div class="purchase-r f fac fjc">
                                                <div class="text-center">
                                                    <p class="color-gray">
                                                        快递
                                                    </p>
                                                    <p>
                                                        ￥{{ logistics.money |
                                                        formatF2Y }}
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                        <el-divider class="mtb20"></el-divider>
                                        <p class="color-gray">
                                            按1个商品数量计算，多规格商品取运费最小值显示！
                                        </p>
                                        <p class="mt10 color-gray">
                                            物流可在付款时进行修改，以上罗列的地区仅为预估运费所用，并不代表此商品可实际发往所有以上地区，可实际发往的地区以下单时的可供选项为准
                                        </p>
                                    </div>
                                </div>
                                <div slot="reference" class="selectAreaSlot">
                                    <span
                                        v-if="logistics.province.id && logistics.city.id"
                                        >{{ logistics.province.name }} {{
                                        logistics.city.name }}</span
                                    >
                                    <span v-else>请选择</span>
                                    <i class="el-icon-arrow-down ml5"></i>
                                </div>
                            </el-popover>
                            <el-divider
                                direction="vertical"
                                class="ml10 mr10"
                            ></el-divider>
                            <span class="mr10">快递</span>
                            <span
                                >¥ {{ logistics.province.id && logistics.city.id
                                && logistics.qty ?
                                $options.filters['formatF2Y'](logistics.money) :
                                '请先选择收货区域' }}</span
                            >
                        </div>
                    </div>

                    <!-- 套装 -->
                    <div class="goods-group-box mt20 f">
                        <p class="text-j">{{ sku_select.spec_title }}</p>
                        <el-radio-group v-model="goodsSkuGroupRadio">
                            <el-radio
                                v-for="item in sku_select.options"
                                :key="item.id"
                                :label="item.title"
                                @change="handleChangeGoodsSkuGroupRadio(item)"
                                >{{ item.title }}
                            </el-radio>
                        </el-radio-group>
                    </div>
                    <!-- 规格 -->
                    <div class="specification-box mt20 f">
                        <p class="text-j">{{ sku_select.sub_spec_title }}</p>
                        <div>
                            <el-row
                                :gutter="10"
                                v-for="item in sku_select_options"
                                :key="item.id"
                                v-if="item.title !== '暂无'"
                            >
                                <!-- <el-col :span="2">
                                <div class="skus-img-box" style="background-color: aqua;" @click="handleBigImg(item.image_url)">
                                    <m-image v-if="item.image_url" :src="item.image_url"
                                             :size="['100%','100%']"></m-image>
                                </div>
                            </el-col>
                            <el-col :span="4">
                                <p class="sku-p" @click="cutSpecification(item.describe)">{{ item.options[1].spec_item_name || "" }}</p>
                            </el-col> -->
                                <el-col :span="6">
                                    <div
                                        :class="['f', 'fac', specificationID === item.id ? 'red-specification' : '']"
                                        @click="cutSpecification(item)"
                                    >
                                        <div
                                            class="skus-img-box"
                                            @click="handleBigImg(item.image_url)"
                                        >
                                            <m-image
                                                v-if="item.image_url"
                                                :src="item.image_url"
                                                :size="['100%','100%']"
                                            ></m-image>
                                        </div>
                                        <p class="sku-p">
                                            {{ item.options[1].spec_item_name ||
                                            "" }}
                                        </p>
                                    </div>
                                </el-col>
                                <el-col :span="4">
                                    <p class="sku-p1" v-if="isLogin">
                                        {{ item.price | formatF2Y }}元
                                    </p>
                                    <el-button
                                        v-else
                                        type="text"
                                        class="color-red"
                                        @click="$router.push('login')"
                                        >登录后查看
                                    </el-button>
                                </el-col>
                                <el-col :span="4">
                                    <p class="sku-p2">
                                        {{ item.stock }}{{ product.unit || "" }}
                                        可售
                                    </p>
                                    <!-- <p class="sku-p2">{{ item.stock }}{{ item.unit || "" }}可售</p> -->
                                </el-col>
                                <el-col :span="8">
                                    <p class="place-p">
                                        <span
                                            >下单号: {{ item.purchase_sn
                                            }}</span
                                        >
                                        <a
                                            class="copy-a"
                                            href="javascript:;"
                                            @click="copyOrder_sn(item.purchase_sn)"
                                            ><span>复制</span></a
                                        >
                                    </p>
                                </el-col>
                                <el-col :span="5">
                                    <el-input-number
                                        size="mini"
                                        v-model.number="item.qty"
                                        :precision="0"
                                        :min="0"
                                        :max="item.stock"
                                    >
                                    </el-input-number>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                    <!-- 购买按钮部分 -->
                    <div
                        v-if="product.is_display === 1"
                        class="mt20 operation-box"
                    >
                        <el-button
                            :disabled="$store.state.userLevelPrice.userLevelPriceTitle !== ''"
                            type="danger"
                            class="shoping-btn"
                            @click="buy()"
                            >立即购买</el-button
                        >
                        <el-button :disabled="$store.state.userLevelPrice.userLevelPriceTitle  !== ''" class="btn-hollow" plain @click="addCart()"
                            >加入购物车</el-button
                        >
                        <el-button class="btn-hollow" plain @click="onCreate"
                            >加入选品专辑</el-button
                        >
                        <el-button
                            class="btn-hollow"
                            plain
                            @click="downloadMaterial"
                            >下载素材</el-button
                        >
                        <a
                            href="javascript:;"
                            @click="favorite()"
                            :class="isFavorite?'is_check':''"
                            style="margin-left: 100px"
                        >
                            <i class="iconfont icon-pc_solid_like"></i>
                            <span v-if="isFavorite">已收藏</span>
                            <span v-else>收藏</span>
                        </a>
                        <template v-if="isExist">
                            <el-button
                                class="btn-hollow mt10"
                                @click="onDeleteProduct"
                                >移出小店</el-button
                            >
                        </template>
                        <template v-else>
                            <el-button
                                class="btn-hollow mt10"
                                @click="onOpenPricePopup(product)"
                                >加入小店</el-button
                            >
                        </template>
                        <template v-if="is_storage">
                            <el-button
                                class="btn-hollow mt10"
                                @click="delSelection"
                                >移除选品库</el-button
                            >
                        </template>
                        <template v-else>
                            <el-button
                                class="btn-hollow mt10"
                                @click="addSelection"
                                >加入选品库</el-button
                            >
                        </template>
                        <p v-if="errHintIsShow" class="mt10 color-red">
                            <i class="el-icon-warning"></i
                            >请选择商品规格及采购数量
                        </p>
                        <p v-if="minBuyQtyErrTextIsShow" class="mt10 color-red">
                            <i class="el-icon-warning"></i
                            >数量不满足商家设置的起批量要求；
                        </p>
                    </div>
                    <!-- 查同款 -->
                    <div class="f fac" style="height: 50px;">
                        <p class="text-j">查同款</p>
                        <div class="logistics-wrapper f fac">
                            <div class="f fac mr_20 same-box" v-for="item in sameList" @click="sameInfo(item.id)">
                                <img :src="item.img" class="img18" />
                                <div class="ml_5">{{ item.name }}</div>
                            </div>
                        </div>
                    </div>
                    <!-- 查同款 -->
                    <sameDialog ref="sameDialog"></sameDialog>

                    <AlbumDialog :visible.sync="dialog.visible"></AlbumDialog>
                    <!-- 下架显示 -->
                    <div v-if="product.is_display === 0" class="mt_40">
                        <span class="is_display">
                            该商品已下架，请选购其他商品！
                        </span>
                        <a
                            href="javascript:;"
                            @click="favorite()"
                            :class="isFavorite?'is_display':''"
                            style="margin-left: 380px"
                        >
                            <i class="iconfont icon-pc_solid_like"></i>
                            <span v-if="isFavorite">已收藏</span>
                            <span v-else>收藏</span>
                        </a>
                    </div>
                    <!-- 单规格改价 start -->
                    <PricePopup
                        ref="pricePopup"
                        :isTip="isTip"
                        :pricePopupShow="pricePopupShow"
                        :editPopupShow="editPopupShow"
                        :price="price"
                        :product="product"
                        :headTitle="headTitle"
                        :ratioValues="ratioValues"
                        @onOpenEditPopup="onOpenEditPopup"
                        @onConfirmPricePopup="onConfirmPricePopup"
                        @onConfirmEditPopup="onConfirmEditPopup"
                        @onClosePricePopup="onClosePricePopup"
                        @onCloseEditPopup="onCloseEditPopup"
                    ></PricePopup>
                    <!-- 单规格改价 end -->
                </div>
            </div>
            <!-- 相关推荐 -->
            <div class="recommend-box">
                <h2 class="title-h2">相关推荐</h2>
                <m-goods>
                    <goods-item
                        v-for="item in relationList"
                        :key="item.id"
                        :link="`/goodsDetail?goods_id=${item.id}`"
                        :page_setup_setting="getFramework.page_setup_setting"
                        :url="item.thumb"
                        :price="item.min_price"
                        :title="item.title"
                        :originPrice="item.origin_price"
                    >
                    </goods-item>
                </m-goods>
            </div>
            <!-- 中间部分 -->
            <div
                class="goods-cent-box mt20 f"
                v-loading.fullscreen.lock="fullscreenLoading"
            >
                <!-- 左侧部分 -->
                <div class="goods-cent-l-box">
                    <!-- 店铺 -->
                    <div class="shop-box">
                        <div class="f fac shop-top-box">
                            <div class="shop-logo-box">
                                <img :src="supplier.shop_logo" class="img100" />
                            </div>
                            <div class="shop-ta-box">
                                <p class="shop-title-p">{{ supplier.name }}</p>
                                <p class="shop-addrs-p">
                                    {{ supplier.province }} {{ supplier.city }}
                                    {{ supplier.county }}
                                </p>
                            </div>
                        </div>
                        <div class="shop-c-box">
                            <p>
                                <span>商品总数</span>
                                <span>{{ supplier.goods_count }}</span>
                            </p>
                            <p class="mt10">
                                <span>热销(件)</span>
                                <span>{{ supplier.hot_sale }}</span>
                            </p>
                        </div>
                        <div class="hx"></div>
                        <div class="shop-b-box f fjsb">
                            <div class="score-box">
                                <p>描述相符</p>
                                <p>{{ supplier.describe_score || 0 }}</p>
                            </div>
                            <div class="score-box">
                                <p>卖家服务</p>
                                <p>{{ supplier.service_score || 0 }}</p>
                            </div>
                            <div class="score-box">
                                <p>物流服务</p>
                                <p>{{ supplier.shopping_score || 0 }}</p>
                            </div>
                        </div>
                        <div class="f fjsb btn-b">
                            <el-button
                                type="danger"
                                class="collect-btn"
                                size="small"
                                @click="$router.push({path:'/store', query:{sid:sid, type:supplierType}})"
                                >进入店铺
                            </el-button>
                            <!-- v-if="!isFavoriteStore" -->
                            <el-button
                                v-if="supplierType === 0 && !isFavoriteStore"
                                size="small"
                                class="collect-btn"
                                :disabled="supplier.id === 0"
                                @click="favoriteStore()"
                            >
                                收藏店铺
                            </el-button>
                            <el-button
                                v-if="isFavoriteStore"
                                type="danger"
                                @click="favoriteStore()"
                                class="entrance-btn"
                                size="small"
                                >取消收藏
                            </el-button>
                        </div>
                    </div>
                    <!-- 店铺推荐 -->
                    <div class="shop-recommend-box">
                        <h2 class="title-h2">店铺推荐</h2>
                        <m-goods v-if="supplierProList.length > 0" :rows="1">
                            <goods-item
                                v-for="item in relationList"
                                :key="item.id"
                                :page_setup_setting="getFramework.page_setup_setting"
                                :link="`/goodsDetail?goods_id=${item.id}`"
                                :url="item.thumb"
                                :price="item.min_price"
                                :title="item.title"
                                :originPrice="item.origin_price"
                            ></goods-item>
                        </m-goods>
                        <!-- <div class="special-offer-box one" v-if="supplierProList.length > 0">
                        <div class="special-offer-item" v-for="item in supplierProList" :key="item.id">
                            <router-link target="_blank" :to="`/goodsDetail?goods_id=${item.id}`">
                                <div class="special-offer-product-img-box">
                                    <img :src="item.thumb" class="img100">
                                </div>
                                <div class="special-offer-b-box">
                                    <p>{{$fn.cutString(item.title,54,"...")}}</p>
                                    <div class="f fac">
                                        <span class="special-offer-title mt10">零售价</span>
                                        <span class="special-offer-number mt10">￥{{item.price | formatF2Y}}</span>
                                    </div>
                                </div>
                            </router-link>
                        </div>
                    </div> -->
                        <div v-else class="text-center">暂无数据~</div>
                    </div>
                </div>
                <!-- 右侧部分 -->
                <div class="goods-cent-r-box f1" ref="goodsCentDiv">
                    <!--
                goodsActiveName: 1=商品信息 2=商品评价 3=商品资质
                 -->
                    <el-tabs
                        v-model="goodsActiveName"
                        class="mt10"
                        @tab-click="handleTabsClick"
                    >
                        <el-tab-pane label="商品信息" name="1"> </el-tab-pane>
                        <el-tab-pane label="商品评论" name="2"> </el-tab-pane>
                        <el-tab-pane label="商品资质" name="3"> </el-tab-pane>
                        <el-tab-pane label="商品素材" name="4"> </el-tab-pane>
                        <el-tab-pane label="视频素材" name="5"> </el-tab-pane>
                    </el-tabs>
                    <!-- 商品详情 -->
                    <goods-info
                        v-if="goodsActiveName === '1'"
                        ref="goodsInfo"
                    ></goods-info>
                    <!-- 商品详情 -->
                    <goods-comment
                        v-if="goodsActiveName === '2'"
                        ref="goodsComment"
                    ></goods-comment>
                    <!-- 商品资质 -->
                    <Qualification
                        v-if="goodsActiveName === '3'"
                        ref="qualification"
                    ></Qualification>
                    <!-- 商品素材 -->
                    <productMaterial 
                        v-if="goodsActiveName === '4'" 
                        ref="productMaterial"
                    ></productMaterial>
                    <!-- 视频素材 -->
                    <videoMaterial
                        v-if="goodsActiveName === '5'"
                        ref="videoMaterial"
                    ></videoMaterial>
                </div>
            </div>
        </div>
    </template>
    <m-empty v-else text="商品不存在"></m-empty>
</div>
