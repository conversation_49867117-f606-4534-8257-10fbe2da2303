<template>
    <div>
        <el-dialog width="912px" title="查同款" :visible="visible" @close="onClose">
            <el-form :model="formData" label-width="110px" ref="form">
                <el-form-item label="支持API对接:">可将供应链商品同步到商城，自主定价销售，赚取差价!</el-form-item>
                <el-form-item label="支持CPS对接:">商城端配置后，会员可搜索商品，跳转到京东、淘宝、拼多多、唯品会等平台购买，商城运营方获得佣金奖励!</el-form-item>
                <el-form-item label="所属平台:">
                    <div class="f fac">
                        <template v-for="item in sameList">
                            <div class="same-box red" v-if='item.id === sameID' :key='item.id' @click='sameFun(item.id)'>{{ item.name }}</div>
                            <div class="same-box" v-else :key='item.id' @click='sameFun(item.id)'>{{ item.name }}</div>
                        </template>
                    </div>
                </el-form-item>
                <el-form-item label="关键词:">
                    <div class="f fac">
                        <el-input
                            class="w360"
                            type="textarea"
                            autosize
                            resize="none"
                            v-model="formData.title"
                        ></el-input>
                        <el-button class="but-true ml_10" @click="search">搜索</el-button>
                    </div>
                </el-form-item>
                <el-form-item label="排序:">
                    <div class="f fac check-all">
                        <template v-for="item in sort">
                            <div
                                v-if="item.id == 1 && item.id == sortid"
                                :key="item.id"
                                style="cursor: pointer; color: #f42121"
                            >{{ item.name }}</div>
                            <div
                                v-else-if="item.id == 1 && item.id != sortid"
                                :key="item.id"
                                @click="newest"
                                style="cursor: pointer"
                            >{{ item.name }}</div>
                        </template>
                        <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                            <template v-for="item in sort">
                                <SortButton v-if="item.id != 1" :key="item.id" :value="item.value">
                                    <space-between
                                        v-if="item.id == sortid"
                                        style="color: #f42121"
                                    >{{ item.name }}</space-between>
                                    <span v-else @click="sortid = item.id">{{ item.name }}</span>
                                </SortButton>
                            </template>
                        </SortButtonGroup>
                    </div>
                </el-form-item>
            </el-form>
            <div v-loading="isLoading">
                <div class="commodity" v-if='list.length >0'>
                    <template v-for="item in list">
                        <div class="commodity-card" :key="item.id">
                            <div class="commodity-card-true">
                                <div style="position: relative;width: 206px;height: 206px;">
                                    <img class="commodityimg" :src="item.goodsImgURL" alt />
                                    <div class="commodityimg-hover-button" @click='promotion(item)'>立即推广</div>
                                </div>
                                <div class="commodity-name">
                                    <span class="tag">{{ sameID | sameName  }}</span>
                                    {{ item.goodsName }}
                                </div>
                                <div class="price">
                                    <div class="price-box">
                                        <div class="price-num">¥{{ item.price }}</div>
                                        <div class="price-name">现价</div>
                                    </div>
                                    <div class="price-box">
                                        <div
                                            class="price-num"
                                            style="text-decoration-line: line-through;color: #A5A5A5;"
                                        >¥{{ item.marketPrice }}</div>
                                        <div class="price-name">原价</div>
                                    </div>
                                    <div class="price-box">
                                        <div class="price-num">{{ $fn.changeMoneyF2Y(item.ec_cps_info.commission_rate ? item.ec_cps_info.commission_rate : 0) }}%</div>
                                        <div class="price-name">佣金比例</div>
                                    </div>
                                </div>
                                <div class="f fac fjsb sales">
                                    <div>销量:{{ item.productSales ? item.productSales : '0'  }}</div>
                                    <div style="color:#F42121">预估收益:¥{{ item.ec_cps_info.commission_amount }}</div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>
                <m-empty v-else></m-empty>
            </div>
            <div class="paging">
                <pagination
                    :limit="pageSize"
                    :page="page"
                    :total="total"
                    layout="total, prev, pager, next, jumper"
                    @pagination="pagination"
                ></Pagination>
            </div>
        </el-dialog>
        <!-- 推广链接 -->
        <promotionDialog ref='promotionDialog'></promotionDialog>
        <!-- mandateDialog 授权 -->
        <mandateDialog ref='mandateDialog'></mandateDialog>
    </div>
</template>

<script>
import SortButtonGroup from '@/components/sortButton/sortButtonGroup.vue';
import SortButton from '@/components/sortButton/sortButton.vue';
import promotionDialog from './promotionDialog.vue'
import mandateDialog from './mandateDialog.vue'

export default {
    components: { SortButtonGroup, SortButton, promotionDialog, mandateDialog },
    data() {
        return {
            visible: false,

            isLoading: false,
            list: [],
            total: 0,
            page: 1,
            pageSize: 12,

            formData: {
                title: ''
            },
            
            // 查同款
            sameList: [
                {name: '京东',id: 'jd'},
                {name: '淘宝',id: 'taobao'},
                {name: '拼多多',id: 'pdd'},
                {name: '唯品会',id: 'vip'},
            ],
            sameID: '',
            sort: [
                { id: 1, name: '综合', value: '' },
                { id: 2, name: '价格', value: 'PRICE' },
                { id: 3, name: '销量', value: 'SALES' },
                { id: 4, name: '佣金比例', value: 'DISCOUNT' },
            ],
            
            sortForm: {
                // 综合: '', 价格:PRICE,
                // 销量:SALES, 佣金比例:DISCOUNT,
                value: '',
                sort: '1', // 1正序，2为逆序
            },
            sortid: 1,
        };
    },
    filters: {
        sameName(title) {
            let str = '';
            switch (title) {
                case 'jd':
                    str = '京东'
                    break;
                case 'taobao':
                    str = '淘宝'
                    break;
                case 'pdd':
                    str = '拼多多'
                    break;
                case 'vip':
                    str = '唯品会'
                    break;
            }
            return str
        }
    },
    methods: {
        info(value,title) {
            this.visible = true;
            this.sameID = value;
            this.formData.title = title;
            this.getList();
        },
        // 搜索
        search() {
            this.page = 1;
            this.getList()
        },
        // 获取商品
        async getList() {
            this.list = [];
            // goodsId: 商品id; goodsName: 商品名称 
            // goodsImgURL: 商品图片;  ec_cps_info: 佣金;
            // productSales: 销量;  marketPrice: 原价；
            // price: 现价;  materialUrl: 落地页;
            switch (this.sameID) {
                case 'jd':
                    this.getListJD();
                    break;
                case 'taobao':
                    this.getListTaobao();
                    break;
                case 'pdd':
                    this.getListPdd();
                    break;
                case 'vip':
                    this.getListVip();
                    break;
            }        
        },
        // 获取商品 JD
        async getListJD() {
            let data = {
                pageIndex: this.page,
                pageSize: this.pageSize,
                keyword: this.formData.title,
                // keyword: '衣服',
            }
            if (this.sortid !== 1) {
                if (this.sortForm.value === 'PRICE') {
                    // 价格
                    data.sortName = 'price'
                } else if (this.sortForm.value === 'SALES') {
                    // 销量
                    data.sortName = 'inOrderCount30Days'
                } else if (this.sortForm.value === 'DISCOUNT') {
                    // 佣金比例
                    data.sortName = 'commissionShare'
                }
                data.sort = this.sortForm.sort == 1 ? 'asc' : 'desc'
            }
            this.isLoading = true;
            let res = await this.$post('/cps/jd/queryGoods',data);
            this.isLoading = false;
            if (res.code === 200) {
                this.total = res.total_results;
                res.data.forEach(element => {
                    this.list.push({
                        goodsId: element.spuid, 
                        goodsImgURL: element.imageInfo.imageList[0].url, // 图片
                        goodsName: element.skuName,
                        marketPrice: element.priceInfo.price, // 原价
                        productSales: element.inOrderCount30Days, // 销量
                        price: element.purchasePriceInfo.purchasePrice, // 现价
                        ec_cps_info: element.ec_cps_info,// 佣金
                        materialUrl: element.materialUrl, // 落地页
                    })
                });
            }
            if (res.code === 4001) {
                this.$message.error('请联系管理员，进行配置');
            }
        },
        // 获取商品 taobao
        async getListTaobao() {
            let data = {
                q: this.formData.title,
                page_no: this.page,
                page_size: this.pageSize,
            };
            if (this.sortid !== 1) {
                if (this.sortForm.value === 'PRICE') {
                    // 价格
                    data.sort = 'final_promotion_price'
                } else if (this.sortForm.value === 'SALES') {
                    // 销量
                    data.sort = 'total_sales'
                } else if (this.sortForm.value === 'DISCOUNT') {
                    // 佣金比例
                    data.sort = 'tk_total_commi'
                }
                data.sort += this.sortForm.sort == 1 ? '_asc' : '_des'
            }
            this.isLoading = true;
            let res = await this.$post('/cps/taobao/superSearchMaterial',data);
            this.isLoading = false;
            if (res.code === 200) {
                this.total = res.total_results;
                res.data.forEach(element => {
                    this.list.push({
                        goodsId: element.item_id,
                        goodsImgURL: element.item_basic_info.pict_url,
                        goodsName: element.item_basic_info.title,
                        marketPrice: element.price_promotion_info.reserve_price,
                        productSales: element.item_basic_info.annual_vol,
                        price: element.price_promotion_info.final_promotion_price,
                        ec_cps_info: element.ec_cps_info,
                    })
                })
            }
            if (res.code === 4001) {
                this.$message.error('请联系管理员，进行配置');
            }
        },
        // 获取商品 pdd
        async getListPdd() {
            let data = {
                keyword: this.formData.title,
                page: this.page,
                page_size: this.pageSize,
            };
            if (this.sortForm.value === 'PRICE') {
                // 价格
                data.sort_type = this.sortForm.sort == 1 ? 3 : 4
            } else if (this.sortForm.value === 'SALES') {
                // 销量
                data.sort_type = this.sortForm.sort == 1 ? 5 : 6
            } else if (this.sortForm.value === 'DISCOUNT') {
                // 佣金比例
                data.sort_type = this.sortForm.sort == 1 ? 1 : 2
            }
            this.isLoading = true;
            let res = await this.$post('/cps/pdd/goodsSearch',data);
            this.isLoading = false;
            // 未授权 获取授权url
            if (res.code === -1) {
                if (res.data.sub_code === "60001") {
                    let {code,data} = await this.$post('/cps/pdd/promUrlGenerate',{});
                    if (code === 200) {
                        // 打开授权弹窗
                        this.$refs.mandateDialog.info(data.url_list[0].url);
                    }
                }
            }
            // 已授权 获取列表
            if (res.code === 200) {
                res.data.forEach(element => {
                    this.list.push({
                        goodsId: element.goods_sign, 
                        goodsImgURL: element.goods_image_url, // 图片
                        goodsName: element.goods_name,
                        marketPrice: this.$fn.changeMoneyF2Y(element.min_normal_price), // 原价
                        productSales: element.sales_tip, // 销量
                        price: this.$fn.changeMoneyF2Y(element.min_group_price), // 现价
                        ec_cps_info: element.ec_cps_info,// 佣金
                    })
                });
            }
            if (res.code === 4001) {
                this.$message.error('请联系管理员，进行配置');
            }
        },
        // 获取商品 vip
        async getListVip() {
            let data = {
                page: this.page,
                pageSize: this.pageSize,
                keyword: this.formData.title,
            }
            if (this.sortid !== 1) {
                data.fieldName = this.sortForm.value;
                data.order = this.sortForm.sort == 1 ? 0 : 1
            }
            this.isLoading = true;
            let res = await this.$post('/cps/vip/query',data);
            this.isLoading = false;
            if (res.code === 200) {
                this.total = res.total_results;
                res.data.forEach(element => {
                    this.list.push({
                        goodsId: element.goodsId, 
                        goodsImgURL: element.goodsMainPicture, // 图片
                        goodsName: element.goodsName,
                        marketPrice: element.marketPrice, // 原价
                        productSales: element.productSales, // 销量
                        price: element.vipPrice, // 现价
                        ec_cps_info: element.ec_cps_info,// 佣金
                    })
                });
            }
            if (res.code === 4001) {
                this.$message.error('请联系管理员，进行配置');
            }
        },
        // 分页
        pagination(res) {
            this.page = res.page; // 修改页数
            // 从新调调用商品数据
            this.getList()
        },
        // 切换所属平台
        sameFun(value) {
            this.sameID = value;
            this.list = [];
            this.total = 0;
            this.page = 1;
            this.getList();
        },
        // 排序最新
        newest() {
            (this.sortForm = {
                value: '',
                sort: '1',
            }),
                (this.sortid = 1);
            // 从新调调用商品数据
            this.getList();
        },
        // 筛选排序
        onChangeSort(res) {
            let val = this.sort.find((item) => item.value == res.value);
            this.sortid = val.id;
            this.page = 1;
            // 从新调调用商品数据
            this.getList();
        },
        // 立即推广
        promotion(res) {
            this.$refs.promotionDialog.info({...res,same: this.sameID})
        },
        // 关闭
        onClose() {
            this.visible = false;
            this.page = 1;
            this.pageSize = 12;
            this.list = [];
            this.total = 0;
            this.sortForm = {
                // 综合: '', 价格:PRICE,
                // 销量:SALES, 佣金比例:DISCOUNT,
                value: '',
                sort: '1', // 1正序，2为逆序
            }
            this.sortid = 1;
            this.sameID = '';

            this.formData = {
                title: ''
            }
        },
    },
};
</script>

<style lang="scss" scoped>
.same-box {
    cursor: pointer;
    margin-right: 30px;
}

.red {
    color: #F42121;
}

.but-true {
    background-color: #f42121;
    color: #fff;
}

.w360 {
    width: 360px;
}

.mr_20 {
    margin-right: 25px;
}

.check-all {
    display: flex;

    div {
        margin-right: 25px;
        cursor: pointer;
    }
}

.commodity {
    width: 880px;
    min-height: 200px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(1, 1fr);

    .commodity-card {
        width: 206px;
        height: 324px;
        background-color: #fff;
        margin-bottom: 15px;
        overflow: hidden;
        cursor: pointer;
        border: 1px solid #e9e9e9;

        .commodity-card-true {
            .commodityimg {
                width: 206px;
                height: 206px;
            }

            .commodityimg-hover-button {
                display: none;
                width: 206px;
                height: 30px;
                position: absolute;
                bottom: 0px;
                background-color: #f42121;
                color: #FFFFFF;
                font-size: 14px;
                line-height: 30px;
                text-align: center;
            }

            .commodity-name {
                height: 32px;
                margin: 6px;
                font-size: 12px;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 2;

                .tag {
                    padding: 0 5px;
                    background-color: rgba($color: #f42121, $alpha: 0.1);
                    color: #f42121;
                }
            }

            .price {
                display: flex;
                margin-top: 15px;

                .price-box {
                    width: 69px;
                    height: 26px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    flex-direction: column;

                    .price-name {
                        font-size: 10px;
                        color: #333333;
                    }

                    .price-num {
                        font-size: 12px;
                        color: #f42121;
                    }
                }
            }

            .sales {
                font-size: 10px;
                width: 190px;
                margin: 13px auto 0;
            }
        }
    }

    .commodity-card:hover .commodity-card-true .commodityimg-hover-button {
        display: block;
    }
}


.paging {
    width: 100%;
    margin: 0 auto 20px;
    display: flex;
    justify-content: end;
}

::v-deep .el-dialog__body {
    padding: 10px 20px 20px 20px !important;
}

::v-deep .el-dialog__header {
    border-bottom: 0px !important;
}
</style>