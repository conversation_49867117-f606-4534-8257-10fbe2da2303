<template>
    <div>
        <el-dialog width="912px" title="推广链接" :visible="isShow" @close="onClose">
            <p>链接:【商品标颎】{{ goods.goodsName }}</p>
            <p>【优惠价】￥{{ goods.price }}</p>
            <p>【下单链接】{{ goods.url }}</p>
            <el-button type="text" class="red" @click="resetfun">复制链接</el-button>
            <div style="width: 260px;margin: 20px auto;">
                <el-image style="width: 260px;margin: 0 auto;" :src="image_url"></el-image>
            </div>
            <div style="width: 100px;margin: 0 auto;">
                <el-button class="but-true" @click="copyPosterInfo">下载海报</el-button>
            </div>
        </el-dialog>
        <!-- mandateDialog 授权 -->
        <mandateDialog ref="mandateDialog"></mandateDialog>
    </div>
</template>

<script>
import mandateDialog from './mandateDialog.vue'
export default {
    components: {
        mandateDialog
    },
    data() {
        return {
            isShow: false,
            id: null, // 订单id
            same: '', // jd taobao pdd vip
            image_url: '', // 海报数据

            goods: {}, // 页面展示数据
            goodsImgURL: '', // 商品图片
            linkURL: '', // 下单链接
        };
    },
    methods: {
        info(res) {
            this.id = res.goodsId;
            this.same = res.same
            this.goodsImgURL = res.goodsImgURL
            this.getGoods(res); // 获取商品信息
            this.isShow = true;
        },
        // 获取商品
        async getGoods(res) {
            switch(this.same) {
                case 'jd':
                    this.getGoodsJd(res);
                    break;
                case 'taobao':
                    this.getGoodsTaobao(res);
                    break;
                case 'pdd':
                    this.getGoodsPdd(res)
                    break;
                case 'vip':
                    this.getGoodsVip();
                    break;
            }

        },
        // jd 获取商品
        async getGoodsJd(goods) {
            // 获取转链
            let linkRes = await this.$post('/cps/jd/urlPrivilege',{materialId: goods.materialUrl});
            if (linkRes.code === 200) {
                this.linkURL = linkRes.data.shortURL;
                this.goods = {
                    goodsName: goods.goodsName,
                    price: goods.price,
                    marketPrice: goods.marketPrice,
                    url: linkRes.data.shortURL
                }

                // 获取海报
                this.getPosterInfo();
            }
        },
        // taobao 获取商品
        async getGoodsTaobao(goods) {
            let res = await this.$post('/cps/taobao/isAccess');
            // 未授权
            if (res.code === 7) {
                let {code,data} = await this.$post('/cps/taobao/getFiles');
                if (code === 0) {
                    let url = "https://oauth.taobao.com/authorize?response_type=code&client_id=" + data.client_id + "&redirect_uri=" + data.redirect_uri + "&state=" + data.state;
                    this.isShow = false;
                    this.$refs.mandateDialog.info(url)
                };
            }
            // 已授权
            if (res.code === 0) {
                // 获取转链
                let {code, data} = await this.$post('/cps/taobao/idPrivilege',{relation_id:res.data.relationId,id:this.id});
                if(code === 200) {
                    this.goods = {
                        goodsName: goods.goodsName,
                        price: goods.price,
                        marketPrice: goods.marketPrice,
                        url: data.item_url
                    };
                    this.linkURL = data.item_url;
                    // 获取海报
                    this.getPosterInfo();
                }
            }
        },
         // pdd 获取商品
        async getGoodsPdd() {
            let data = {
                goods_sign: this.id
            };
            let res = await this.$post('/cps/pdd/goodsDetail2',data);
            if (res.code === 200) {
                // 获取转链
                let linkRes = await this.$post('/cps/pdd/convert',{goods_sign: this.id});
                if (linkRes.code === 200) {
                    this.goods = {
                        goodsName: res.data[0].goods_name,
                        price: this.$fn.changeMoneyF2Y(res.data[0].min_group_price),
                        marketPrice: this.$fn.changeMoneyF2Y(res.data[0].min_normal_price), 
                        url: linkRes.data.url
                    };
                    this.linkURL = linkRes.data.url;
                    // 获取海报
                    this.getPosterInfo()
                }
            }
        },
        // vip 获取商品
        async getGoodsVip() {
            let data = {
                id: this.id
            };
            let res = await this.$post('/cps/vip/itemInfo',data);
            if (res.code === 200) {
                // 获取转链
                let linkRes = await this.$post('/cps/vip/idPrivilege',{id: this.id});
                if (linkRes.code === 200) {
                    this.linkURL = linkRes.data.url;
                    this.goods = {
                        goodsName: res.data.goodsName,
                        price: res.data.vipPrice,
                        marketPrice: res.data.marketPrice,
                        url: linkRes.data.url
                    };
                    // 获取海报
                    this.getPosterInfo()
                }
            }
        },
        // 获取海报
        async getPosterInfo() {
            let data = {
                product_id: String(this.id),
                product_title: this.goods.goodsName,
                product_image: this.goodsImgURL,
                product_link: this.linkURL,
                product_price: String(this.goods.price),
                product_price_2: String(this.goods.marketPrice),
            }
            let res = await this.$post('/cps/getPosterInfo',data);
            if (res.code === 0) {
                this.image_url =  res.data.qrcode;
            }
        },
        // 复制链接
        resetfun() {
            this.$fn.copyFn(this.goods.url)
        },
        // 下载海报
        copyPosterInfo() {
            var aElement = document.createElement('a');
            aElement.download = "海报";
            aElement.href = this.image_url;
            aElement.click();
        },
        // 关闭连接
        onClose() {
            this.isShow = false;
            this.id = null;
            this.same = '';
            this.image_url = '';
            this.goods = {};
            this.linkURL = '';
            this.$emit('onClose')
        },
    },
};
</script>

<style lang="scss" scoped>
p {
    font-size: 14px;
    color: #666666;
    line-height: 28px;
}

.red {
    color: #f42121;
}


.but-true {
    background-color: #f42121;
    color: #fff;
    margin: 0 auto;
}
</style>