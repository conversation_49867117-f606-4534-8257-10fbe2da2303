<!--
 * @Author: 蒋文婷 <EMAIL>
 * @Date: 2022-07-19 15:49:12
 * @LastEditors: 蒋文婷 <EMAIL>
 * @LastEditTime: 2022-07-20 16:40:45
 * @FilePath: \demo\shop-side-shop\src\views\goodsList\albumGoodsPage\albumGoods.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<div class="inner">
    <Breadcrumb :data="[{name:'首页'},{name:'专辑列表'}]"></Breadcrumb>
    <!-- 搜索 -->
    <div class="search-box bgw">
        <el-form ref="formData" :model="formData" label-width="85px" label-position="left">
            <el-form-item label="类目：">
                <div class="f" style="width: 100%;">
                    <div>
                        <el-select
                            class="merchandise-screen-select"
                            v-model="formData.category_1_id"
                            placeholder="请选择一级分类"
                            filterable
                            @change="getCategory(2, formData.category_1_id)"
                        >
                            <el-option
                                v-for="item in category1"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="merchandise-screen-select"
                            v-model="formData.category_2_id"
                            placeholder="请选择二级分类"
                            filterable
                            @change="getCategory(3, formData.category_2_id)"
                        >
                            <el-option
                                v-for="item in category2"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div>
                        <el-select
                            class="merchandise-screen-select"
                            v-model="formData.category_3_id"
                            placeholder="请选择三级分类"
                            filterable
                        >
                            <el-option
                                v-for="item in category3"
                                :key="item.id"
                                :label="item.name"
                                :value="item.id"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="来源：">
                <el-select
                    class="merchandise-screen-select"
                    v-model="formData.source"
                    placeholder="全部"
                >
                    <el-option
                        v-for="item in sourceList"
                        :key="item.source_name"
                        :label="item.source_name"
                        :value="item.gather_supply_id + '-' + item.type + '-' + item.source_name"
                    >
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="商品属性：">
                <div class="f fac fw" style="width: 1100px;">
                    <div class="f fac merchandise-screen-select-title " v-if="formData.grossProfitRate != 5">
                        <div class="select-name">毛利率</div>
                        <el-select
                            class="selecl-sty"
                            v-model="formData.grossProfitRate"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in grossProfitRate"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="f merchandise-screen-select-eles">
                        <div class="select-name">毛利率</div>
                        <el-input
                            class="input-sty"
                            v-model="formData.g1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="formData.g2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">%</div>
                        </div>
                    </div>
                    <div class="f fac merchandise-screen-select-title" v-if="formData.profitRate != 5">
                        <div class="select-name">利润率</div>
                        <el-select
                            class="selecl-sty"
                            v-model="formData.profitRate"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in profitRateOptios"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="f merchandise-screen-select-eles">
                        <div class="select-name">利润率</div>
                        <el-input
                            class="input-sty"
                            v-model="formData.p1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="formData.p2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">%</div>
                        </div>
                    </div>
                    <div class="f fac merchandise-screen-select-title" v-if="formData.agreementPrice != 5">
                        <div class="select-name">协议价</div>
                        <el-select
                            class="selecl-sty"
                            v-model="formData.agreementPrice"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in agreementPriceOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="f merchandise-screen-select-eles">
                        <div class="select-name">协议价</div>
                        <el-input
                            class="input-sty"
                            v-model="formData.a1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="formData.a2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">元</div>
                        </div>
                    </div>
                    <div class="f fac merchandise-screen-select-title">
                        <div class="select-name">指导价</div>
                        <el-select
                            class="selecl-sty"
                            v-model="formData.guidePrice"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in guide_marketing_PriceOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div class="f fac merchandise-screen-select-title" v-if="formData.discount != 5">
                        <div class="select-name">折扣</div>
                        <el-select
                            class="selecl-sty"
                            v-model="formData.discount"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in discountList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div v-else class="f merchandise-screen-select-eles">
                        <div class="select-name">折扣</div>
                        <el-input
                            class="input-sty"
                            v-model="formData.d1"
                            placeholder="请选择"
                        ></el-input>
                        <div class="line">-</div>
                        <div class="input-bin">
                            <el-input
                                class="input"
                                v-model="formData.d2"
                                placeholder="请选择"
                            ></el-input>
                            <div class="bin">折</div>
                        </div>
                    </div>
                    <div class="f fac merchandise-screen-select-title">
                        <div class="select-name">营销价</div>
                        <el-select
                            class="selecl-sty"
                            v-model="formData.marketingPrice"
                            placeholder="请选择"
                        >
                            <el-option
                                v-for="item in guide_marketing_PriceOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                    <div class="f fac merchandise-screen-select-title">
                        <div class="select-name selectc">是否已导入</div>
                        <el-select
                            class="selecl-sty selectcs"
                            v-model="formData.self_is_import"
                            placeholder="请选择"
                        >
                            <el-option label="全部" value=""></el-option>
                            <el-option label="已导入" value="1"></el-option>
                            <el-option label="未导入" value="0"></el-option>
                        </el-select>
                    </div>
                    <div class="f fac merchandise-screen-select-title">
                        <div class="select-name" style="width: 60px">营销属性</div>
                        <el-select
                            class="selecl-sty"
                            style="width: 182px"
                            v-model="formData.marketing"
                            placeholder="请选择"
                        >
                            <el-option label="默认" value=""></el-option>
                            <el-option label="新品" value="is_new"></el-option>
                            <el-option label="热卖" value="is_hot"></el-option>
                            <el-option label="促销" value="is_promotion"></el-option>
                        </el-select>
                    </div>
                </div>
            </el-form-item>
            <el-form-item label="">
                <div class="f fac">
                    <el-button class="but-true" @click="goSearch">搜索</el-button>
                    <el-button @click="resetfun">重置</el-button>
                    <div class="selection-num" @click="toMySel">
                        我的选品库（{{ my_center_total ? my_center_total : 0 }}）
                    </div>
                    <div class="selection-num" @click="addAlbum">加入商品专辑</div>
                </div>
            </el-form-item>
        </el-form>
    </div>
    <!-- 排序 -->
    <div class="check-all f fac fjsb">
        <div class="f">
            <el-checkbox
                style="margin-right: 30px"
                v-model="checked"
                @change="checkedfun"
                >全选</el-checkbox
            >
            <template v-for="item in sort">
                <div
                    v-if="item.id == 1 && item.id == sortid"
                    :key="item.id"
                    style="cursor: pointer; color: #f42121"
                >
                    {{ item.name }}
                </div>
                <div
                    v-else-if="item.id == 1 && item.id != sortid"
                    :key="item.id"
                    @click="newest"
                    style="cursor: pointer"
                >
                    {{ item.name }}
                </div>
            </template>
            <SortButtonGroup v-model="sortForm" @change="onChangeSort">
                <template v-for="item in sort">
                    <SortButton
                        v-if="item.id != 1"
                        :key="item.id"
                        :value="item.value"
                    >
                        <space-between
                            v-if="item.id == sortid"
                            style="color: #f42121"
                        >
                            {{ item.name }}
                        </space-between>
                        <span v-else @click="sortid = item.id">{{
                            item.name
                        }}</span>
                    </SortButton>
                </template>
            </SortButtonGroup>
        </div>
        <div class="check-all-right">
            <div @click="tolead(0)">
                待导入商品（{{ selection_list.length }}）
            </div>
            <div @click="tolead(1)">
                导入全部筛选商品（{{
                    total ? total : 0
                }}）
            </div>
        </div>
    </div>
    <!-- 商品列表部分 -->
    <div v-loading="loading">
        <!-- <m-goods v-if="goodsList.length > 0" class="mt10">
            <goods-item v-for="item in goodsList" :key="item.id" :link="`/goodsDetail?goods_id=${item.id}`"
                        :page_setup_setting="getFramework.page_setup_setting"
                :url="item.thumb" :price="item.min_price" :title="item.title" :originPrice="item.origin_price">
            </goods-item>
        </m-goods> -->
        <div v-if="goodsList.length > 0" class="commodity">
            <div class="commodity-card"  v-for="item in goodsList" :key="item.id">
                <div class="commodity-card-true">
                    <img
                        class="commodityimg"
                        :src="item.image_url"
                        alt=""
                    />
                    <div class="commodity-name">{{ item.title }}</div>
                    <div class="price">
                        <div class="price-box fen">
                            <div class="price-name">供货价</div>
                            <!-- <div class="price-num">¥{{ item.agreement_price | formatF2Y }}</div> -->
                            <div class="price-num" v-if="$ls.getUserId()">
                                ¥{{ item.agreement_price / 100 }}
                            </div>
                            <div v-else class="price-num" @click.prevent="$router.push('/login')">登录查看</div>
                        </div>
                        <div class="price-box">
                            <div class="price-name">利润率</div>
                            <div class="price-num">
                                ¥{{ $fn.changeMoneyY2F(item.market_rate) }}%
                            </div>
                        </div>
                    </div>
                </div>
                <div class="commodity-card-hover">
                    <div style="position: relative">
                        <div class="commodityimg-hover">
                            <img :src="item.image_url" alt="" />
                        </div>
                        <div class="commodityimg-hover-button">
                            <div
                                style="border-right: 1px solid #ffffff"
                                @click="goodsDetail(item.id)"
                            >
                                查看详情
                            </div>
                            <div v-if="item.is_import == 1">已导入</div>
                            <div
                                v-else-if="
                                    !selection_list.find(
                                        (val) => val == item.id,
                                    )
                                "
                                @click="addOption(item.id)"
                            >
                                加入选品
                            </div>
                            <div v-else @click="delOption(item.id)">
                                移除选品
                            </div>
                        </div>
                    </div>
                    <div class="commodity-name">{{ item.title }}</div>
                    <div class="price">
                        <div class="price-box fen">
                            <div class="price-name">供货价</div>
                            <div class="price-num" v-if="$ls.getUserId()">
                                ¥{{ item.agreement_price / 100 }}
                            </div>
                            <div v-else class="price-num" @click.prevent="$router.push('/login')">登录查看</div>
                        </div>
                        <div class="price-box">
                            <div class="price-name">利润率</div>
                            <div class="price-num">
                                ¥{{ $fn.changeMoneyY2F(item.market_rate) }}%
                            </div>
                        </div>
                    </div>
                    <div class="det-price">
                        <div class="det-price-box">
                            <div class="det-price-name">指导价</div>
                            <!-- <div class="det-price-num">¥{{ item.guide_price | formatF2Y }}</div> -->
                            <div class="det-price-num">
                                ¥{{ item.guide_price / 100 }}
                            </div>
                        </div>
                        <div class="det-price-box">
                            <div class="det-price-name">最小利润</div>
                            <div class="det-price-num">
                                ¥{{ item.min_profit / 100 }}
                            </div>
                        </div>
                        <div class="det-price-box">
                            <div class="det-price-name">折扣</div>
                            <!-- <div class="det-price-num">{{  item.profit | formatF2Y }}</div> -->
                            <div class="det-price-num">
                                {{ item.profit / 100 }}
                            </div>
                        </div>
                        <div class="det-price-box">
                            <div class="det-price-name">毛利率</div>
                            <div class="det-price-num">
                                {{
                                    $fn.changeMoneyY2F(
                                        item.gross_profit_rate,
                                    )
                                }}%
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <skeletonGoods v-if="hasMore  && isLoading === false" v-for="item in 10"></skeletonGoods>

        </div>
        <m-empty v-else></m-empty>
    </div>
    <!-- <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next, jumper">
    </Pagination> -->
    <!-- 导入对话框 -->
    <el-dialog
        title="导入商品"
        :visible.sync="centerDialogVisible"
        @close="nolead"
    >
        <div class="title-con" v-if="!centerDialogVisibleID">
            当前待导入商品数量：{{ selection_list.length }}
        </div>
        <div class="title-con" v-else>
            当前待导入商品数量：{{ total }}
        </div>
        <div class="title-text">
            点击导入，将待导入商品加入选品库，通过API对接您的系统，可一键导入您选品库的所有商品!
        </div>
        <div class="title-button">
            <el-button class="ackbutton" @click="acktrue">导入</el-button>
            <el-button @click="nolead">取消</el-button>
        </div>
    </el-dialog>
    <!-- 加入商品专辑 -->
    <albumDialog ref="albumDialog" :visible.sync="dialog.visible" :propRow="dialog.propRow"></albumDialog>
</div>