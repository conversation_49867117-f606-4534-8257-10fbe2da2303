<div>
  <div class="bg-grey">
    <div class="pt20 pb20">
      <div class="inner goodsDetail-box">
        <m-card class="relative">
          <div class="grey-2 time">{{albumData.time_name}}发布</div>
          <div class="f">
            <img class="thumbnail" v-if="albumData.covers[0].src" :src="albumData.covers[0].src">
            <div class="w100">
              <div class="mb12 con-tit">
                <p class="thumbtitle">{{albumData.name}}</p>
              </div>
              <div class="con-tag">
                <template v-for="member in albumData.relations">
                  <el-tag
                    v-if="member.tag.name"
                    :key="member.tag_id"
                    type="warning"
                    size="small">
                    {{ member.tag.name }}
                  </el-tag>
                </template>
              </div>
              <div class="con-user mt10">
                <div>
                  <el-avatar icon="el-icon-user-solid" size="small"></el-avatar>
                  <span class="grey-1 bold" v-if="albumData.user_info">{{albumData.user_info.nickname}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="con-count">
            <div class="item-count">
              <p class="count-num">{{albumData.product_count}}</p>
              <p class="count-txt">商品数量</p>
            </div>
            <div class="item-count">
              <p class="count-num">{{albumData.import_count}}</p>
              <p class="count-txt">累计导入</p>
            </div>
            <div class="item-count">
              <p class="count-num">{{albumData.browse_count}}</p>
              <p class="count-txt">累计访问</p>
            </div>
            <div class="item-count">
              <p class="count-num">{{albumData.sales_total}}</p>
              <p class="count-txt">累计销量</p>
            </div>
            <div>
              <template>
                <el-button class="new-btn" @click="addSelection">加入选品库</el-button>
              </template>
              <template v-if="isExist">
                <el-button class="white-btn" @click="onDeleteAlbum">移出小店</el-button>
              </template>
              <template v-else>
                <el-button class="white-btn" @click="onOpenPriceBatch">加入小店</el-button>
              </template>
              <el-button class="red-btn" @click="exportAlbum">导出报价单</el-button>
            </div>
          </div>

        </m-card>
        <m-card v-if="albumData.describe" class="mt20">
          <div class="grey-3 lh24"><strong>描述：</strong>{{albumData.describe}}</div>
        </m-card>
        <!-- 商品列表 start -->
        <div class="con-good mt20" >
          <template v-for="item in albumList">
            <div v-if="item.product.id!=0" class="item-good" :key="item.id" @click="$_blank('/goodsDetail', {goods_id: item.product_id})">
              <div><img class="good-img" :src="item.product.image_url"></div>
              <div class="item-infor">
                <div class="mb12">
                  <el-tooltip 
                    effect="dark" 
                    :content="item.product.title" 
                    v-if="item.product.title.length > 35"
                    placement="top-start">
                    <p class="thumbtitle-2 black">{{item.product.title.slice(0, 35) + '...'}}</p>
                  </el-tooltip>
                  <p v-else class="thumbtitle-2">{{item.product.title}}</p>
                </div>
                <div class="con-price">
                  <p v-if="$ls.getUserId()">
                    {{ 
                      getFramework.page_setup_setting.language_setting.super_wholesal_price === '' ? '超级批发价' : 
                      getFramework.page_setup_setting.language_setting.super_wholesal_price 
                    }}：<span class="price">¥ {{formatPrice(item.product.min_price)}}</span></p>
                  <p v-else>批发价：<span class="price black">¥ {{formatPrice(item.product.normal_price)}}</span></p>
                  <p>建议零售价/指导价：<span class="price">¥ {{formatPrice(item.product.min_guide_price)}}</span></p>
                  <!-- <p>利润率：<span class="price">{{profitPercent(item.product.guide_price, item.product.min_price)}}</span></p> -->
                  <p>利润率：<span class="price">{{item.product.cost_rate.toFixed(2) + '%'}}</span></p>
                </div>
              </div>
            </div>
          </template>
          <skeletonGoods class="item-good" v-if="hasMore  && isLoading === false" v-for="item in 10"></skeletonGoods>

        </div>
        <!-- 商品列表 end -->
        <!-- <Pagination :total="total" @pagination="pagination" :limit="pageSize" layout="total, prev, pager, next">
        </Pagination> -->
        <!-- 单规格改价 start -->
        <!-- <PricePopup
            ref="pricePopup"
            :pricePopupShow="pricePopupShow"
            :editPopupShow="editPopupShow"
            :price="price"
            :product="product"
            :headTitle="headTitle"
            @onOpenEditPopup="onOpenEditPopup"
            @onConfirmPricePopup="onConfirmPricePopup"
            @onConfirmEditPopup="onConfirmEditPopup"
            @onClosePricePopup="onClosePricePopup"
            @onCloseEditPopup="onCloseEditPopup"
        ></PricePopup> -->
        <!-- 单规格改价 end -->
        <!-- 批量改价 start -->
        <PriceBatchPopup
            :isTip="isTip"
            :priceBatchShow="priceBatchShow"
            :checkboxValue="checkboxValue"
            :ratioValues = "ratioValues"
            @onOpenPriceBatch="onOpenPriceBatch"
            @onConfirmPriceBatch="onConfirmPriceBatch"
            @onClosePriceBatch="onClosePriceBatch"
        >
        </PriceBatchPopup>
        <!-- 批量改价 end -->       
      </div>
    </div>
  </div>

  
</div>