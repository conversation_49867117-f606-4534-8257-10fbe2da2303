package service

import (
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	shopOrder "order/model"
	"payment/service"
	"small-shop/model"
	"small-shop/request"
	"time"
	"yz-go/component/log"
	"yz-go/source"
)

type Award struct {
	source.Model
	SID            uint              `json:"sid" form:"sid" gorm:"column:sid;comment:商店id;"`
	SUID           uint              `json:"suid" form:"suid" gorm:"column:suid;comment:店主会员id;"`
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:店铺会员id;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:店铺订单id;"`
	ShopOrderID    uint              `json:"shop_order_id" gorm:"column:shop_order_id;comment:中台订单id;"`
	ShopPayTypeID  int               `json:"shop_pay_type_id" form:"shop_pay_type_id" gorm:"column:shop_pay_type_id;comment:中台订单支付方式;type:smallint;size:3;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;"`
	OrderCompleted int               `json:"order_completed" gorm:"column:order_completed;comment:订单是否完成 0:未完成 1：已完成;"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	SettleAt       *source.LocalTime `json:"settle_at" gorm:"column:settle_at;comment:结算时间;"`
	PaidAt         *source.LocalTime `json:"paid_at" gorm:"column:statement_at;comment:支付时间;"`
	IsSplit        int               `json:"is_split" gorm:"column:is_split;default:0;comment:是否分账0否1是;"`
	SplitStatus    int               `json:"split_status" gorm:"column:split_status;default:0;comment:分账状态 0待结算 1成功;"`
	// 店主会员
	User model.User `json:"user" gorm:"foreignKey:SUID"`
	// 下单会员
	ShopUserInfo model.SmallShopUser `json:"shop_user_info" gorm:"foreignKey:Uid"`
	// 中台订单
	ShopOrder Order `json:"shop_order_info" gorm:"foreignKey:ShopOrderID"`
	// 小商店订单
	SmallShopOrder SmallShopOrder `json:"small_shop_order" gorm:"foreignKey:OrderID"`
}

func (Award) TableName() string {
	return "small_shop_awards"
}

type Order struct {
	ID        uint   `json:"id" form:"id" gorm:"primarykey"`
	PayTypeID int    `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"` // 订单支付方式
	OrderSN   uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;"`
	PayType   string `json:"pay_type"`
}

func (b *Order) AfterFind(tx *gorm.DB) (err error) {
	err, payType := service.GetPayType()
	if err != nil {
		return
	}
	for _, v := range payType {
		if b.PayTypeID == v.Code {
			b.PayType = v.Name
		}
	}
	if b.PayTypeID == -1 {
		b.PayType = "后台支付"
	}

	if b.PayType == "" {
		b.PayType = "未知"
	}
	return
}

func GetAwardsList(info request.AwardSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&Award{})
	var awards []Award

	if info.SID != 0 {
		db.Where("`sid` = ?", info.SID)
	}

	err = db.Count(&total).Error

	err = db.Preload(clause.Associations).Order("id desc").Limit(limit).Offset(offset).Find(&awards).Error
	return err, awards, total
}

func SettleAward(award model.SettleAward) (err error) {
	//如果是分账状态是2
	if award.IsSplit == 1 {
		award.Status = 2
	} else {
		award.Status = 1
	}
	award.SettleAt = &source.LocalTime{Time: time.Now()}
	err = source.DB().Model(&award).Updates(map[string]interface{}{"status": award.Status, "settle_at": award.SettleAt}).Error
	return err
}

func CreateAward(award model.Award) (err error) {
	err = source.DB().Create(&award).Error
	return err
}

// 创建收益记录单独出来 避免 循环调用包的问题
func AwardCreate(order shopOrder.Order, smallShopOrder model.SmallShopOrder, smallShop model.SmallShop, splitAmount uint, types int) (err error, award model.Award) {
	// 基础设置
	var setting model.Setting
	err, setting = GetSetting()
	if err != nil || setting.Values.Switch != 1 {
		//log.Log().Error("查询小商店基础设置报错,返回")
		//log.Log().Error("查询小商店基础设置报错", zap.Any("err", err))
		log.Log().Error("查询小商店基础设置报错", zap.Any("Switch", setting.Values.Switch), zap.Any("err", err))
		err = errors.New("查询小商店基础设置报错")
		return
	}
	// 产生奖励
	var amount uint
	amount = 0
	if setting.Values.OrderPayType == 1 {
		//如果是聚合分账
		if types == 1 {
			amount = splitAmount
		} else {
			amount = smallShopOrder.Amount
		}
	} else {
		//如果是聚合分账
		if types == 1 {
			if splitAmount > order.Amount {
				amount = splitAmount - order.Amount
			}
		} else {
			if smallShopOrder.Amount > order.Amount {
				amount = smallShopOrder.Amount - order.Amount
			}
		}
	}
	// amount = 100
	if amount <= 0 {
		//log.Log().Error("店主奖励为0,返回")
		err = errors.New("店主奖励为0")
		return
	}
	award.SID = smallShop.ID
	award.SUID = smallShop.Uid
	award.Uid = smallShopOrder.SmallShopUserID
	award.OrderID = smallShopOrder.ID
	award.ShopOrderID = order.ID
	award.ShopPayTypeID = order.PayTypeID
	award.PaidAt = smallShopOrder.PaidAt
	award.Amount = amount
	award.Status = 0
	award.OrderCompleted = 0
	award.OrderPayType = setting.Values.OrderPayType
	award.SettleDays = setting.Values.SettleDays
	if types == 1 {
		award.IsSplit = 1
		award.SplitStatus = 0 //聚合支付-分账结算的时候自动分账
	}
	err = CreateAward(award)
	if err != nil {
		//log.Log().Error("创建店主收益失败,返回")
		log.Log().Error("创建店主收益失败", zap.Any("err", err))
		err = errors.New("创建店主收益失败" + err.Error())
		return
	}
	return
}
