package service

import (
	"errors"
	"fmt"
	"github.com/360EntSecGroup-Skylar/excelize"
	"github.com/shopspring/decimal"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	shopOrder "order/model"
	orderService "order/service"
	"os"
	"region/mapping"
	express2 "shipping/express"
	"small-shop/model"
	"small-shop/request"
	"small-shop/response"
	"small-shop/small-shop-order"
	"strconv"
	"strings"
	"time"
	"yz-go/component/log"
	"yz-go/config"
	"yz-go/source"
	"yz-go/utils"
)

type GatherSupply struct {
	source.Model
	CategoryID uint `json:"category_id" form:"category_id" gorm:"column:category_id;default:0;"`
}

type CloudOrder struct {
	OrderId      uint   `json:"order_id" form:"order_id" gorm:"column:order_id;comment:中台订单id;"`                   //中台订单id
	OrderSn      uint   `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:中台订单编号;"`                   //中台订单编号
	CloudOrderSn string `json:"cloud_order_sn" form:"cloud_order_sn" gorm:"column:cloud_order_sn;comment:云仓订单号;"`  //云仓订单号
	CloudOrderId int    `json:"cloud_order_id"  form:"cloud_order_id" gorm:"column:cloud_order_id;comment:云仓订单id"` // 云仓订单id
}

type SmallShopOrder struct {
	ID            uint `json:"id" form:"id" gorm:"primarykey"`
	Amount        uint `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"` // 订单总金额
	OrderSN       uint `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;index;"`
	SmallShopID   uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;default:0;comment:小商店id;"`
	SupplyOrderID uint `json:"supply_order_id" form:"supply_order_id" gorm:"column:supply_order_id;comment:供应链订单id;index;"` // 供应链中台订单id
}

func GetOrderNum(UserID uint) (err error, info response.OrderNum) {
	err, info.ClosedNum = getOrderNum(UserID, model.Closed)
	if err != nil {
		return
	}
	err, info.WaitReceiveNum = getOrderNum(UserID, model.WaitReceive)
	if err != nil {
		return
	}
	err, info.WaitPayNum = getOrderNum(UserID, model.WaitPay)
	if err != nil {
		return
	}
	err, info.WaitSendNum = getOrderNum(UserID, model.WaitSend)
	if err != nil {
		return
	}
	err, info.CompletedNum = getOrderNum(UserID, model.Completed)
	if err != nil {
		return
	}
	return
}

func getOrderNum(userID uint, status model.OrderStatus) (err error, orderNum int64) {
	err = source.DB().Model(&model.SmallShopOrder{}).Where("small_shop_user_id = ?", userID).Where("status = ?", status).Count(&orderNum).Error
	return
}

func GetOrderByUID(uid, orderID uint) (err error, order SmallShopOrder) {
	var smallShop SmallShop
	err, smallShop = FindSmallShop(uid)
	if err != nil {
		return
	}
	err = source.DB().Model(&SmallShopOrder{}).Where("small_shop_id = ? and id = ?", smallShop.ID, orderID).First(&order).Error
	return
}

func GetSupplyOrderID(orderID uint) (err error, supplyOrderID uint) {
	var order SmallShopOrder
	err = source.DB().Model(&SmallShopOrder{}).Where("id = ?", orderID).First(&order).Error
	return err, order.SupplyOrderID
}

func GetList(info request.SmallShopOrderSearch) (err error, list interface{}, total int64, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	smallShopOrder := response.SmallShopOrder{}
	db := source.DB().Model(&smallShopOrder)
	var all []response.SmallShopOrder
	// 如果有条件搜索 下方会自动创建搜索语句
	var WaitPayNumDb = source.DB().Model(&smallShopOrder)
	var WaitSendNumDb = source.DB().Model(&smallShopOrder)
	var WaitReceiveNumDb = source.DB().Model(&smallShopOrder)
	var CompletedNumDb = source.DB().Model(&smallShopOrder)
	var ClosedNumDb = source.DB().Model(&smallShopOrder)
	var BackNumDb = source.DB().Model(&smallShopOrder)
	var RefundNumDb = source.DB().Model(&smallShopOrder)

	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`pay_info_id` in ?", payInfoIds)
		WaitPayNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitSendNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitReceiveNumDb.Where("`pay_info_id` in ?", payInfoIds)
		CompletedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		ClosedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		BackNumDb.Where("`pay_info_id` in ?", payInfoIds)
		RefundNumDb.Where("`pay_info_id` in ?", payInfoIds)
	}
	if info.PayTypeID != 0 {
		db.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitPayNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitSendNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitReceiveNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		CompletedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		ClosedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		BackNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		RefundNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
	}
	if info.IsWxMiniSend != 0 {
		db.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		WaitPayNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		WaitSendNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		WaitReceiveNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		CompletedNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		ClosedNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		BackNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		RefundNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
	}
	if info.SSID != 0 {
		db.Where("`small_shop_id` = ?", info.SSID)
		WaitPayNumDb.Where("`small_shop_id` = ?", info.SSID)
		WaitSendNumDb.Where("`small_shop_id` = ?", info.SSID)
		WaitReceiveNumDb.Where("`small_shop_id` = ?", info.SSID)
		CompletedNumDb.Where("`small_shop_id` = ?", info.SSID)
		ClosedNumDb.Where("`small_shop_id` = ?", info.SSID)
		BackNumDb.Where("`small_shop_id` = ?", info.SSID)
		RefundNumDb.Where("`small_shop_id` = ?", info.SSID)
	}
	if info.SmallShopUserID > 0 {
		db.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		WaitPayNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		WaitSendNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		WaitReceiveNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		CompletedNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		ClosedNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		BackNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		RefundNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		db.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitPayNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitSendNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitReceiveNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		CompletedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		ClosedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		BackNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		RefundNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
	}
	if info.Status != nil {
		if *info.Status == 5 {
			db.Where("`refund_status` = ?", model.Refunding)
			WaitPayNumDb.Where("`refund_status` = ?", model.Refunding)
			WaitSendNumDb.Where("`refund_status` = ?", model.Refunding)
			WaitReceiveNumDb.Where("`refund_status` = ?", model.Refunding)
			CompletedNumDb.Where("`refund_status` = ?", model.Refunding)
			ClosedNumDb.Where("`refund_status` = ?", model.Refunding)
			BackNumDb.Where("`refund_status` = ?", model.Refunding)
			RefundNumDb.Where("`refund_status` = ?", model.Refunding)
		} else if *info.Status == 6 {
			db.Where("`refund_status` = ?", model.RefundComplete)
			WaitPayNumDb.Where("`refund_status` = ?", model.RefundComplete)
			WaitSendNumDb.Where("`refund_status` = ?", model.RefundComplete)
			WaitReceiveNumDb.Where("`refund_status` = ?", model.RefundComplete)
			CompletedNumDb.Where("`refund_status` = ?", model.RefundComplete)
			ClosedNumDb.Where("`refund_status` = ?", model.RefundComplete)
			BackNumDb.Where("`refund_status` = ?", model.RefundComplete)
			RefundNumDb.Where("`refund_status` = ?", model.RefundComplete)
		} else {
			db.Where("`status` = ?", info.Status)

		}
	}
	if info.RefundStatus != nil {
		db.Where("`refund_status` = ?", info.RefundStatus)
		WaitPayNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitSendNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitReceiveNumDb.Where("`refund_status` = ?", info.RefundStatus)
		CompletedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		ClosedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		BackNumDb.Where("`refund_status` = ?", info.RefundStatus)
		RefundNumDb.Where("`refund_status` = ?", info.RefundStatus)
	}
	if info.OrderSN != "" {
		db.Where("`order_sn` = ?", info.OrderSN)
		WaitPayNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitSendNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitReceiveNumDb.Where("`order_sn` = ?", info.OrderSN)
		CompletedNumDb.Where("`order_sn` = ?", info.OrderSN)
		ClosedNumDb.Where("`order_sn` = ?", info.OrderSN)
		BackNumDb.Where("`order_sn` = ?", info.OrderSN)
		RefundNumDb.Where("`order_sn` = ?", info.OrderSN)
	}
	if info.SupplySN != "" {
		db.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		WaitPayNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		WaitSendNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		WaitReceiveNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		CompletedNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		ClosedNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		BackNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		RefundNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
	}
	if info.ThirdOrderSN != "" {
		db.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		WaitPayNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		WaitSendNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		WaitReceiveNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		CompletedNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		ClosedNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		BackNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		RefundNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
	}
	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitPayNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitSendNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitReceiveNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		CompletedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		ClosedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		BackNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		RefundNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitPayNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitSendNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitReceiveNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		CompletedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		ClosedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		BackNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		RefundNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	if info.ProductTitle != "" {
		orderIds := []uint{}
		err = source.DB().Model(model.SmallShopOrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("small_shop_order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)
	}
	if info.NickName != "" {
		userIds := []uint{}
		err = source.DB().Model(response.SmallShopUser{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`small_shop_user_id` in ?", userIds)
		WaitPayNumDb.Where("`small_shop_user_id` in ?", userIds)
		WaitSendNumDb.Where("`small_shop_user_id` in ?", userIds)
		WaitReceiveNumDb.Where("`small_shop_user_id` in ?", userIds)
		CompletedNumDb.Where("`small_shop_user_id` in ?", userIds)
		ClosedNumDb.Where("`small_shop_user_id` in ?", userIds)
		BackNumDb.Where("`small_shop_user_id` in ?", userIds)
		RefundNumDb.Where("`small_shop_user_id` in ?", userIds)
	}
	if info.UserName != "" {
		shippingIds := []uint{}
		err = source.DB().Model(model.SmallShopShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`shipping_address_id` in ?", shippingIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", shippingIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		BackNumDb.Where("`shipping_address_id` in ?", shippingIds)
		RefundNumDb.Where("`shipping_address_id` in ?", shippingIds)
	}
	if info.UserMobile != "" {
		userIds := []uint{}
		err = source.DB().Model(model.SmallShopShippingAddress{}).Where("mobile like ?", "%"+strings.Trim(info.UserMobile,
			" ")+"%").Pluck("id",
			&userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`shipping_address_id` in ?", userIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", userIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", userIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", userIds)
		BackNumDb.Where("`shipping_address_id` in ?", userIds)
		RefundNumDb.Where("`shipping_address_id` in ?", userIds)
	}
	if info.ShippingSN != "" {
		var orderIds []uint
		err = source.DB().Model(shopOrder.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)
	}
	if info.CloudOrderId != 0 {
		var orderIds []uint
		source.DB().Model(&CloudOrder{}).Where("cloud_order_id = ?", info.CloudOrderId).Pluck("order_id", &orderIds)
		db.Where("id in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)
	}

	if info.GatherSupplierID != nil || info.SupplierID != nil {
		var supplyOrderIDs, orderIDs []int64
		err = source.DB().Model(&response.SmallShopOrder{}).Pluck("supply_order_id", &supplyOrderIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 指定供应链
		if info.GatherSupplierID != nil {
			err = source.DB().Model(&response.Order{}).Where("id in ?", supplyOrderIDs).Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0").Pluck("id", &orderIDs).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
		}
		if info.SupplierID != nil {
			if *info.SupplierID == 999999 {
				err = source.DB().Model(&response.Order{}).Where("id in ?", supplyOrderIDs).Where("`supplier_id` > 0").Pluck("id", &orderIDs).Error
			} else {
				err = source.DB().Model(&response.Order{}).Where("id in ?", supplyOrderIDs).Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0").Pluck("id", &orderIDs).Error
			}
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
		}
		db.Where("supply_order_id in ?", orderIDs)
		WaitPayNumDb.Where("`supply_order_id` in ?", orderIDs)
		WaitSendNumDb.Where("`supply_order_id` in ?", orderIDs)
		WaitReceiveNumDb.Where("`supply_order_id` in ?", orderIDs)
		CompletedNumDb.Where("`supply_order_id` in ?", orderIDs)
		ClosedNumDb.Where("`supply_order_id` in ?", orderIDs)
		BackNumDb.Where("`supply_order_id` in ?", orderIDs)
		RefundNumDb.Where("`supply_order_id` in ?", orderIDs)
	}

	db.Preload(clause.Associations)
	err = db.Count(&total).Error
	if info.Status != nil {
		if *info.Status == model.WaitReceive {
			db.Order("sent_at DESC")
		} else if *info.Status == model.WaitSend {
			db.Order("paid_at DESC")
		} else {
			db.Order("created_at DESC")
		}
	} else {
		db.Order("created_at DESC")
	}

	err = db.Limit(limit).Offset(offset).Find(&all).Error
	err = WaitPayNumDb.Where("status = ?", model.WaitPay).Count(&WaitPayNum).Error
	err = WaitSendNumDb.Where("status = ?", model.WaitSend).Count(&WaitSendNum).Error
	err = WaitReceiveNumDb.Where("status = ?", model.WaitReceive).Count(&WaitReceiveNum).Error
	err = CompletedNumDb.Where("status = ?", model.Completed).Count(&CompletedNum).Error
	err = ClosedNumDb.Where("status = ?", model.Closed).Count(&ClosedNum).Error
	err = BackNumDb.Where("refund_status = ?", model.Refunding).Count(&BackNum).Error
	err = RefundNumDb.Where("refund_status = ?", model.RefundComplete).Count(&RefundNum).Error

	return err, all, total, WaitPayNum, WaitSendNum, WaitReceiveNum, CompletedNum, ClosedNum, BackNum, RefundNum
}

func ExportOrder(info request.SmallShopOrderSearch) (err error, link string) {
	info.PageSize = 5000
	smallShopOrder := response.SmallShopOrder{}
	db := source.DB().Model(&smallShopOrder)
	var all []response.SmallShopOrder
	var total int64
	// 如果有条件搜索 下方会自动创建搜索语句
	var WaitPayNumDb = source.DB().Model(&smallShopOrder)
	var WaitSendNumDb = source.DB().Model(&smallShopOrder)
	var WaitReceiveNumDb = source.DB().Model(&smallShopOrder)
	var CompletedNumDb = source.DB().Model(&smallShopOrder)
	var ClosedNumDb = source.DB().Model(&smallShopOrder)
	var BackNumDb = source.DB().Model(&smallShopOrder)
	var RefundNumDb = source.DB().Model(&smallShopOrder)

	if info.PaySN != "" {
		var payInfoIds []uint
		err = source.DB().Model(&response.PayInfo{}).Where("pay_sn = ?", info.PaySN).Pluck("id", &payInfoIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}

		db.Where("`pay_info_id` in ?", payInfoIds)
		WaitPayNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitSendNumDb.Where("`pay_info_id` in ?", payInfoIds)
		WaitReceiveNumDb.Where("`pay_info_id` in ?", payInfoIds)
		CompletedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		ClosedNumDb.Where("`pay_info_id` in ?", payInfoIds)
		BackNumDb.Where("`pay_info_id` in ?", payInfoIds)
		RefundNumDb.Where("`pay_info_id` in ?", payInfoIds)
	}
	if info.PayTypeID != 0 {
		db.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitPayNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitSendNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		WaitReceiveNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		CompletedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		ClosedNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		BackNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
		RefundNumDb.Where("`pay_type_id` = ?", info.PayTypeID)
	}
	if info.IsWxMiniSend != 0 {
		db.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		WaitPayNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		WaitSendNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		WaitReceiveNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		CompletedNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		ClosedNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		BackNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
		RefundNumDb.Where("`is_wx_mini_send` = ?", info.IsWxMiniSend)
	}
	if info.SSID != 0 {
		db.Where("`small_shop_id` = ?", info.SSID)
		WaitPayNumDb.Where("`small_shop_id` = ?", info.SSID)
		WaitSendNumDb.Where("`small_shop_id` = ?", info.SSID)
		WaitReceiveNumDb.Where("`small_shop_id` = ?", info.SSID)
		CompletedNumDb.Where("`small_shop_id` = ?", info.SSID)
		ClosedNumDb.Where("`small_shop_id` = ?", info.SSID)
		BackNumDb.Where("`small_shop_id` = ?", info.SSID)
		RefundNumDb.Where("`small_shop_id` = ?", info.SSID)
	}
	if info.SmallShopUserID > 0 {
		db.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		WaitPayNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		WaitSendNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		WaitReceiveNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		CompletedNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		ClosedNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		BackNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
		RefundNumDb.Where("`small_shop_user_id` = ?", info.SmallShopUserID)
	}
	if info.GatherSupplierStatus != "" && info.GatherSupplierID != nil {
		db.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitPayNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitSendNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		WaitReceiveNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		CompletedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		ClosedNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		BackNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
		RefundNumDb.Where("`gather_supply_sn` = '' or `gather_supply_sn` = null")
	}
	if info.Status != nil {
		if *info.Status == 5 {
			db.Where("`refund_status` = ?", model.Refunding)
			WaitPayNumDb.Where("`refund_status` = ?", model.Refunding)
			WaitSendNumDb.Where("`refund_status` = ?", model.Refunding)
			WaitReceiveNumDb.Where("`refund_status` = ?", model.Refunding)
			CompletedNumDb.Where("`refund_status` = ?", model.Refunding)
			ClosedNumDb.Where("`refund_status` = ?", model.Refunding)
			BackNumDb.Where("`refund_status` = ?", model.Refunding)
			RefundNumDb.Where("`refund_status` = ?", model.Refunding)
		} else if *info.Status == 6 {
			db.Where("`refund_status` = ?", model.RefundComplete)
			WaitPayNumDb.Where("`refund_status` = ?", model.RefundComplete)
			WaitSendNumDb.Where("`refund_status` = ?", model.RefundComplete)
			WaitReceiveNumDb.Where("`refund_status` = ?", model.RefundComplete)
			CompletedNumDb.Where("`refund_status` = ?", model.RefundComplete)
			ClosedNumDb.Where("`refund_status` = ?", model.RefundComplete)
			BackNumDb.Where("`refund_status` = ?", model.RefundComplete)
			RefundNumDb.Where("`refund_status` = ?", model.RefundComplete)
		} else {
			db.Where("`status` = ?", info.Status)

		}
	}
	if info.RefundStatus != nil {
		db.Where("`refund_status` = ?", info.RefundStatus)
		WaitPayNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitSendNumDb.Where("`refund_status` = ?", info.RefundStatus)
		WaitReceiveNumDb.Where("`refund_status` = ?", info.RefundStatus)
		CompletedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		ClosedNumDb.Where("`refund_status` = ?", info.RefundStatus)
		BackNumDb.Where("`refund_status` = ?", info.RefundStatus)
		RefundNumDb.Where("`refund_status` = ?", info.RefundStatus)
	}
	if info.OrderSN != "" {
		db.Where("`order_sn` = ?", info.OrderSN)
		WaitPayNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitSendNumDb.Where("`order_sn` = ?", info.OrderSN)
		WaitReceiveNumDb.Where("`order_sn` = ?", info.OrderSN)
		CompletedNumDb.Where("`order_sn` = ?", info.OrderSN)
		ClosedNumDb.Where("`order_sn` = ?", info.OrderSN)
		BackNumDb.Where("`order_sn` = ?", info.OrderSN)
		RefundNumDb.Where("`order_sn` = ?", info.OrderSN)
	}
	if info.SupplySN != "" {
		db.Where("gather_supply_sn like ?", "%"+info.SupplySN+"%")
		WaitPayNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		WaitSendNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		WaitReceiveNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		CompletedNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		ClosedNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		BackNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
		RefundNumDb.Where("`gather_supply_sn` = ?", info.SupplySN)
	}
	if info.ThirdOrderSN != "" {
		db.Where("third_order_sn like ?", "%"+info.ThirdOrderSN+"%")
		WaitPayNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		WaitSendNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		WaitReceiveNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		CompletedNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		ClosedNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		BackNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
		RefundNumDb.Where("`third_order_sn` = ?", info.ThirdOrderSN)
	}
	var timeType string
	timeType = "created_at"
	if info.TimeType != nil {
		switch *info.TimeType {
		case 0:
			timeType = "created_at"
			break
		case 1:
			timeType = "paid_at"
			break
		case 2:
			timeType = "sent_at"
			break
		case 3:
			timeType = "received_at"
			break
		default:
			timeType = "created_at"
			break
		}
	}
	if info.StartAT != "" {
		db.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitPayNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitSendNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		WaitReceiveNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		CompletedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		ClosedNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		BackNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
		RefundNumDb.Where("`"+timeType+"` >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitPayNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitSendNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		WaitReceiveNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		CompletedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		ClosedNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		BackNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
		RefundNumDb.Where("`"+timeType+"` <= ?", info.EndAT)
	}
	if info.ProductTitle != "" {
		orderIds := []uint{}
		err = source.DB().Model(model.SmallShopOrderItem{}).Where("title like ?", "%"+info.ProductTitle+"%").Pluck("small_shop_order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)
	}
	if info.NickName != "" {
		userIds := []uint{}
		err = source.DB().Model(response.SmallShopUser{}).Where("nick_name like ?", "%"+info.NickName+"%").Pluck("id", &userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`small_shop_user_id` in ?", userIds)
		WaitPayNumDb.Where("`small_shop_user_id` in ?", userIds)
		WaitSendNumDb.Where("`small_shop_user_id` in ?", userIds)
		WaitReceiveNumDb.Where("`small_shop_user_id` in ?", userIds)
		CompletedNumDb.Where("`small_shop_user_id` in ?", userIds)
		ClosedNumDb.Where("`small_shop_user_id` in ?", userIds)
		BackNumDb.Where("`small_shop_user_id` in ?", userIds)
		RefundNumDb.Where("`small_shop_user_id` in ?", userIds)
	}
	if info.UserName != "" {
		shippingIds := []uint{}
		err = source.DB().Model(model.SmallShopShippingAddress{}).Where("realname like ?", "%"+info.UserName+"%").Pluck("id", &shippingIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`shipping_address_id` in ?", shippingIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", shippingIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", shippingIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", shippingIds)
		BackNumDb.Where("`shipping_address_id` in ?", shippingIds)
		RefundNumDb.Where("`shipping_address_id` in ?", shippingIds)
	}
	if info.UserMobile != "" {
		userIds := []uint{}
		err = source.DB().Model(model.SmallShopShippingAddress{}).Where("mobile like ?", "%"+strings.Trim(info.UserMobile,
			" ")+"%").Pluck("id",
			&userIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`shipping_address_id` in ?", userIds)
		WaitPayNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitSendNumDb.Where("`shipping_address_id` in ?", userIds)
		WaitReceiveNumDb.Where("`shipping_address_id` in ?", userIds)
		CompletedNumDb.Where("`shipping_address_id` in ?", userIds)
		ClosedNumDb.Where("`shipping_address_id` in ?", userIds)
		BackNumDb.Where("`shipping_address_id` in ?", userIds)
		RefundNumDb.Where("`shipping_address_id` in ?", userIds)
	}
	if info.ShippingSN != "" {
		var orderIds []uint
		err = source.DB().Model(shopOrder.OrderExpress{}).Where("express_no = ?", info.ShippingSN).Pluck("order_id", &orderIds).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		db.Where("`id` in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)
	}
	if info.CloudOrderId != 0 {
		var orderIds []uint
		source.DB().Model(&CloudOrder{}).Where("cloud_order_id = ?", info.CloudOrderId).Pluck("order_id", &orderIds)
		db.Where("id in ?", orderIds)
		WaitPayNumDb.Where("`id` in ?", orderIds)
		WaitSendNumDb.Where("`id` in ?", orderIds)
		WaitReceiveNumDb.Where("`id` in ?", orderIds)
		CompletedNumDb.Where("`id` in ?", orderIds)
		ClosedNumDb.Where("`id` in ?", orderIds)
		BackNumDb.Where("`id` in ?", orderIds)
		RefundNumDb.Where("`id` in ?", orderIds)
	}

	if info.GatherSupplierID != nil || info.SupplierID != nil {
		var supplyOrderIDs, orderIDs []int64
		err = source.DB().Model(&response.SmallShopOrder{}).Pluck("supply_order_id", &supplyOrderIDs).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			return
		}
		// 指定供应链
		if info.GatherSupplierID != nil {
			err = source.DB().Model(&response.Order{}).Where("id in ?", supplyOrderIDs).Where("gather_supply_id = ?", info.GatherSupplierID).Where("supplier_id = 0").Pluck("id", &orderIDs).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
		}
		if info.SupplierID != nil {
			if *info.SupplierID == 999999 {
				err = source.DB().Model(&response.Order{}).Where("id in ?", supplyOrderIDs).Where("`supplier_id` > 0").Pluck("id", &orderIDs).Error
			} else {
				err = source.DB().Model(&response.Order{}).Where("id in ?", supplyOrderIDs).Where("`supplier_id` = ?", &info.SupplierID).Where("`gather_supply_id` = 0").Pluck("id", &orderIDs).Error
			}
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				return
			}
		}
		db.Where("supply_order_id in ?", orderIDs)
		WaitPayNumDb.Where("`supply_order_id` in ?", orderIDs)
		WaitSendNumDb.Where("`supply_order_id` in ?", orderIDs)
		WaitReceiveNumDb.Where("`supply_order_id` in ?", orderIDs)
		CompletedNumDb.Where("`supply_order_id` in ?", orderIDs)
		ClosedNumDb.Where("`supply_order_id` in ?", orderIDs)
		BackNumDb.Where("`supply_order_id` in ?", orderIDs)
		RefundNumDb.Where("`supply_order_id` in ?", orderIDs)
	}

	db.Preload(clause.Associations)
	err = db.Count(&total).Error
	excelPage := total/int64(info.PageSize) + 1
	var links []string
	timeString := time.Now().Format("20060102150405")
	path := config.Config().Local.Path + "/export_order"
	for ei := 1; ei <= int(excelPage); ei++ {
		err = db.
			Preload("SupplyOrder").
			Preload("SmallShopOrderItems.Sku").
			Preload("SmallShopOrderItems.Product").
			Preload("SmallShopOrderItems").
			Preload("PayInfo").
			Preload("User").
			Order("created_at DESC").Limit(info.PageSize).Offset(info.PageSize * (ei - 1)).Find(&all).Error
		if err != nil {
			return
		}
		f := excelize.NewFile()
		// 创建一个工作表
		index := f.NewSheet("Sheet1")
		// 设置单元格的值
		f.SetCellValue("Sheet1", "A1", "订单id")
		f.SetCellValue("Sheet1", "B1", "订单编号")
		f.SetCellValue("Sheet1", "C1", "三方单号")
		f.SetCellValue("Sheet1", "D1", "支付单号")
		f.SetCellValue("Sheet1", "E1", "子订单号")
		f.SetCellValue("Sheet1", "F1", "会员ID")
		f.SetCellValue("Sheet1", "G1", "粉丝昵称")
		f.SetCellValue("Sheet1", "H1", "收货人姓名")
		f.SetCellValue("Sheet1", "I1", "联系电话")
		f.SetCellValue("Sheet1", "J1", "省")
		f.SetCellValue("Sheet1", "K1", "市")
		f.SetCellValue("Sheet1", "L1", "区")
		f.SetCellValue("Sheet1", "M1", "收货地址")
		f.SetCellValue("Sheet1", "N1", "商品简码")
		f.SetCellValue("Sheet1", "O1", "商品名称")
		f.SetCellValue("Sheet1", "P1", "规格条码")
		f.SetCellValue("Sheet1", "Q1", "规格编号")
		f.SetCellValue("Sheet1", "R1", "商品编号")
		f.SetCellValue("Sheet1", "S1", "商品数量")
		f.SetCellValue("Sheet1", "T1", "支付方式")
		f.SetCellValue("Sheet1", "U1", "技术服务费")
		f.SetCellValue("Sheet1", "V1", "商品单价")
		f.SetCellValue("Sheet1", "W1", "商品小计")
		f.SetCellValue("Sheet1", "X1", "运费")
		f.SetCellValue("Sheet1", "Y1", "应收款")
		f.SetCellValue("Sheet1", "Z1", "状态")
		f.SetCellValue("Sheet1", "AA1", "下单时间")
		f.SetCellValue("Sheet1", "AB1", "付款时间")
		f.SetCellValue("Sheet1", "AC1", "发货时间")
		f.SetCellValue("Sheet1", "AD1", "完成时间")
		f.SetCellValue("Sheet1", "AE1", "快递公司")
		f.SetCellValue("Sheet1", "AF1", "快递单号")
		f.SetCellValue("Sheet1", "AG1", "订单备注")
		f.SetCellValue("Sheet1", "AH1", "用户备注")
		f.SetCellValue("Sheet1", "AI1", "附加")
		f.SetCellValue("Sheet1", "AJ1", "供货金额")
		f.SetCellValue("Sheet1", "AK1", "开票名称")
		f.SetCellValue("Sheet1", "AL1", "税收分类编码")
		f.SetCellValue("Sheet1", "AM1", "采购税率")
		f.SetCellValue("Sheet1", "AN1", "成本价")
		f.SetCellValue("Sheet1", "AO1", "销售单价")
		f.SetCellValue("Sheet1", "AP1", "店铺")
		i := 2
		for _, v := range all {
			// 剩余技术服务费(元)
			restTechnicalServicesFee := decimal.NewFromInt(int64(v.TechnicalServicesFee)).DivRound(decimal.NewFromInt(100), 2)
			for ii, item := range v.SmallShopOrderItems {
				var wValue = "" //W位置的值
				//如果是代发货状态使用子订单的发货状态
				if v.Status == model.WaitSend {
					wValue = model.GetItemSendStatusName(item.SendStatus)
				} else {
					wValue = model.GetStatusName(v.Status)
				}
				var companyName string
				var expressNo string
				expressNo = v.OrderExpress.ExpressNo
				err, companyName = express2.GetCompanyByCode(v.OrderExpress.CompanyCode)
				if err != nil {
					companyName = err.Error()
				}
				var sentAt string
				if v.SentAt != nil {
					sentAt = v.SentAt.Format("2006-01-02 15:04:05")
				}
				var createAt string
				if v.CreatedAt != nil {
					createAt = v.CreatedAt.Format("2006-01-02 15:04:05")
				}
				var payAt string
				if v.PaidAt != nil {
					payAt = v.PaidAt.Format("2006-01-02 15:04:05")
				}
				var receivedAt string
				if v.ReceivedAt != nil {
					receivedAt = v.ReceivedAt.Format("2006-01-02 15:04:05")
				}
				// 服务费(元)
				var technicalServicesFee decimal.Decimal
				if ii+1 == len(v.SmallShopOrderItems) {
					technicalServicesFee = restTechnicalServicesFee
				} else {
					if v.ItemAmount-v.RefundAmount != 0 {
						technicalServicesFee = decimal.NewFromInt(int64(item.Amount*v.TechnicalServicesFee)).DivRound(decimal.NewFromInt(int64(v.ItemAmount-v.RefundAmount)*100), 2)
					}
					restTechnicalServicesFee = restTechnicalServicesFee.Sub(technicalServicesFee)
				}
				// 商品单价
				var skuPrice decimal.Decimal
				if item.Qty != 0 {
					skuPrice = decimal.NewFromInt(int64(item.Amount)).DivRound(decimal.NewFromInt(int64(item.Qty)), 2).DivRound(decimal.NewFromInt(100), 2)
				} else {
					skuPrice = decimal.NewFromInt(0)
				}
				// 应收款(元)
				var amount decimal.Decimal

				amount = decimal.NewFromInt(int64(item.Amount)).DivRound(decimal.NewFromInt(100), 2).Add(technicalServicesFee)
				println(item.Amount, technicalServicesFee.String())
				println("应收款(元)", amount.String())
				var taxProductName, taxCode, taxRate, taxShortCode string
				if item.Product.BillPosition == 1 {
					taxShortCode = item.Product.ShortCode
					taxProductName = item.Product.TaxProductName
					taxCode = item.Product.TaxCode
					taxRate = strconv.Itoa(item.Product.TaxRate)
				} else {
					taxShortCode = item.Sku.ShortCode
					taxProductName = item.Sku.TaxProductName
					taxCode = item.Sku.TaxCode
					taxRate = strconv.Itoa(item.Sku.TaxRate)
				}
				f.SetCellValue("Sheet1", "A"+strconv.Itoa(i), v.ID)
				f.SetCellValue("Sheet1", "B"+strconv.Itoa(i), strconv.Itoa(int(v.OrderSN)))
				f.SetCellValue("Sheet1", "C"+strconv.Itoa(i), strconv.Itoa(int(v.SupplyOrder.OrderSN)))
				f.SetCellValue("Sheet1", "D"+strconv.Itoa(i), strconv.Itoa(int(v.PayInfo.PaySN)))
				f.SetCellValue("Sheet1", "E"+strconv.Itoa(i), item.ID) //子订单号
				f.SetCellValue("Sheet1", "F"+strconv.Itoa(i), v.User.ID)
				f.SetCellValue("Sheet1", "G"+strconv.Itoa(i), v.User.NickName)
				f.SetCellValue("Sheet1", "H"+strconv.Itoa(i), v.ShippingAddress.Realname)
				f.SetCellValue("Sheet1", "I"+strconv.Itoa(i), v.ShippingAddress.Mobile)
				f.SetCellValue("Sheet1", "J"+strconv.Itoa(i), v.ShippingAddress.Province)
				f.SetCellValue("Sheet1", "K"+strconv.Itoa(i), v.ShippingAddress.City)
				f.SetCellValue("Sheet1", "L"+strconv.Itoa(i), v.ShippingAddress.County)
				// 如果详细地址包含了省市区
				var shippingAddressContainsRegion bool
				shippingAddressContainsRegion, err = mapping.HasMatchingRegionsWhichScoreGreaterThan("ali_region", v.ShippingAddress.Detail, 25.0)
				if err != nil {
					err = nil
				}
				if strings.HasPrefix(v.ShippingAddress.Detail, v.ShippingAddress.Province+v.ShippingAddress.City+v.ShippingAddress.County+v.ShippingAddress.Town) || shippingAddressContainsRegion {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.ShippingAddress.Detail)
				} else {
					f.SetCellValue("Sheet1", "M"+strconv.Itoa(i), v.ShippingAddress.Province+v.ShippingAddress.City+v.ShippingAddress.County+v.ShippingAddress.Town+v.ShippingAddress.Detail)
				}
				f.SetCellValue("Sheet1", "N"+strconv.Itoa(i), taxShortCode)
				f.SetCellValue("Sheet1", "O"+strconv.Itoa(i), item.Title+"["+item.SkuTitle+"]")
				f.SetCellValue("Sheet1", "P"+strconv.Itoa(i), item.Sku.Barcode)
				f.SetCellValue("Sheet1", "Q"+strconv.Itoa(i), item.Sku.Sn)
				f.SetCellValue("Sheet1", "R"+strconv.Itoa(i), item.Product.Sn)
				f.SetCellValue("Sheet1", "S"+strconv.Itoa(i), strconv.Itoa(int(item.Qty)))
				f.SetCellValue("Sheet1", "T"+strconv.Itoa(i), v.PayType)
				f.SetCellValue("Sheet1", "U"+strconv.Itoa(i), technicalServicesFee)
				f.SetCellValue("Sheet1", "V"+strconv.Itoa(i), skuPrice)
				f.SetCellValue("Sheet1", "W"+strconv.Itoa(i), float64(item.Amount)/100)
				f.SetCellValue("Sheet1", "X"+strconv.Itoa(i), float64(v.Freight)/100)
				f.SetCellValue("Sheet1", "Y"+strconv.Itoa(i), amount)
				f.SetCellValue("Sheet1", "Z"+strconv.Itoa(i), wValue)
				f.SetCellValue("Sheet1", "AA"+strconv.Itoa(i), createAt)
				f.SetCellValue("Sheet1", "AB"+strconv.Itoa(i), payAt)
				f.SetCellValue("Sheet1", "AC"+strconv.Itoa(i), sentAt)
				f.SetCellValue("Sheet1", "AD"+strconv.Itoa(i), receivedAt)
				f.SetCellValue("Sheet1", "AE"+strconv.Itoa(i), companyName)
				f.SetCellValue("Sheet1", "AF"+strconv.Itoa(i), expressNo)
				f.SetCellValue("Sheet1", "AG"+strconv.Itoa(i), v.Note)
				f.SetCellValue("Sheet1", "AH"+strconv.Itoa(i), v.Remark)
				f.SetCellValue("Sheet1", "AI"+strconv.Itoa(i), "")
				f.SetCellValue("Sheet1", "AJ"+strconv.Itoa(i), float64(item.SupplyAmount)/100)
				f.SetCellValue("Sheet1", "AK"+strconv.Itoa(i), taxProductName)
				f.SetCellValue("Sheet1", "AL"+strconv.Itoa(i), taxCode)
				f.SetCellValue("Sheet1", "AM"+strconv.Itoa(i), taxRate)
				f.SetCellValue("Sheet1", "AN"+strconv.Itoa(i), float64(item.CostAmount)/100)
				f.SetCellValue("Sheet1", "AO"+strconv.Itoa(i), float64(item.Sku.GuidePrice)/100)
				f.SetCellValue("Sheet1", "AP"+strconv.Itoa(i), v.SmallShop.Title)

				i++
			}
		}
		// 设置工作簿的默认工作表
		f.SetActiveSheet(index)
		exist, _ := utils.PathExists(path)
		if !exist {
			// 创建文件夹
			err = os.Mkdir(path, os.ModePerm)
			if err != nil {
				fmt.Printf("mkdir failed![%v]\n", err)
			} else {
				fmt.Printf("mkdir success!\n")
			}
		}
		link = path + "/" + timeString + "-" + strconv.Itoa(ei) + "订单导出.xlsx"
		if err = f.SaveAs(link); err != nil {
			log.Log().Error("订单导出错误4：", zap.Any("err", err.Error()))
			return
		}
		links = append(links, link)
	}
	if excelPage > 1 {
		link = path + "/" + timeString + "订单导出.zip"
		err = orderService.Zip(link, links)
	}
	return err, link
}

func GetShippingAddressLog(orderID uint) (err error, logs []model.ShippingAddressLog) {
	err = source.DB().Where("order_id = ?", orderID).Order("created_at desc").Find(&logs).Error
	return
}

func UpdateShippingAddress(updateShippingAddress request.UpdateShippingAddress, isMq int) (err error) {
	if updateShippingAddress.OrderID == 0 {
		err = errors.New("请提交订单id")
		return
	}
	if updateShippingAddress.Province == "" || updateShippingAddress.ProvinceId == 0 {
		err = errors.New("请选择省")
		return
	}
	if updateShippingAddress.City == "" || updateShippingAddress.CityId == 0 {
		err = errors.New("请选择市")
		return
	}
	if updateShippingAddress.County == "" || updateShippingAddress.CountyId == 0 {
		err = errors.New("请选择区")
		return
	}
	if updateShippingAddress.Town == "" || updateShippingAddress.TownId == 0 {
		err = errors.New("请选择街道")
		return
	}
	if updateShippingAddress.Mobile == "" {
		err = errors.New("请填写联系方式")
		return
	}
	if updateShippingAddress.Realname == "" {
		err = errors.New("请填写收货人姓名")
		return
	}
	var smallShopOrder model.SmallShopOrder
	err = source.DB().Where("id = ?", updateShippingAddress.OrderID).First(&smallShopOrder).Error
	if smallShopOrder.IsUpdateShippingAddress == 0 {
		log.Log().Error("订单无法修改收货地址", zap.Any("err", err))
		err = errors.New("订单无法修改收货地址")
		return
	}
	if smallShopOrder.Status != model.WaitPay && smallShopOrder.Status != model.WaitSend {
		err = errors.New("订单状态不支持修改收货地址")
		return
	}
	if err != nil {
		err = errors.New("订单不存在")
		return
	}
	var shippingAddressLog model.ShippingAddressLog
	var shippingAddress model.SmallShopShippingAddress
	err = source.DB().Where("id = ?", smallShopOrder.ShippingAddressID).First(&shippingAddress).Error
	if err != nil {
		err = errors.New("订单收货地址不存在")
		return
	}
	//修改前
	shippingAddressLog.OldRealname = shippingAddress.Realname
	shippingAddressLog.OldMobile = shippingAddress.Mobile
	shippingAddressLog.OldCountryId = shippingAddress.CountryId
	shippingAddressLog.OldProvinceId = shippingAddress.ProvinceId
	shippingAddressLog.OldCityId = shippingAddress.CityId
	shippingAddressLog.OldTownId = shippingAddress.TownId
	shippingAddressLog.OldProvince = shippingAddress.Province
	shippingAddressLog.OldCity = shippingAddress.City
	shippingAddressLog.OldCounty = shippingAddress.County
	shippingAddressLog.OldCountyId = shippingAddress.CountyId
	shippingAddressLog.OldTown = shippingAddress.Town
	shippingAddressLog.OldDetail = shippingAddress.Detail
	shippingAddressLog.OldLng = shippingAddress.Lng
	shippingAddressLog.OldLat = shippingAddress.Lat
	//修改后
	shippingAddressLog.Realname = updateShippingAddress.Realname
	shippingAddressLog.Mobile = updateShippingAddress.Mobile
	shippingAddressLog.CountryId = updateShippingAddress.CountryId
	shippingAddressLog.ProvinceId = updateShippingAddress.ProvinceId
	shippingAddressLog.CityId = updateShippingAddress.CityId
	shippingAddressLog.TownId = updateShippingAddress.TownId
	shippingAddressLog.Province = updateShippingAddress.Province
	shippingAddressLog.City = updateShippingAddress.City
	shippingAddressLog.County = updateShippingAddress.County
	shippingAddressLog.CountyId = updateShippingAddress.CountyId

	shippingAddressLog.Town = updateShippingAddress.Town
	shippingAddressLog.Detail = updateShippingAddress.Detail
	shippingAddressLog.Lng = updateShippingAddress.Lng
	shippingAddressLog.Lat = updateShippingAddress.Lat
	shippingAddressLog.OrderID = smallShopOrder.ID
	shippingAddressLog.UserID = smallShopOrder.SmallShopUserID
	shippingAddressLog.ThirdOrderSN = strconv.Itoa(int(smallShopOrder.OrderSN))
	err = source.DB().Create(&shippingAddressLog).Error
	if err != nil {
		log.Log().Error("创建收货信息修改记录失败", zap.Any("updateShippingAddress", updateShippingAddress))
		err = errors.New("创建收货信息修改记录失败")
		return
	}
	err = source.DB().Save(&updateShippingAddress.SmallShopShippingAddress).Error
	return
}

func GetOrderByID(id uint) (err error, smallShopOrder small_shop_order.SmallShopOrder) {
	err = source.DB().Preload("SmallShopOrderItems").Preload("SmallShopOrderItems.SmallShopAfterSales").Preload("SmallShopOrderItems.SmallShopAfterSales.AfterSalesAudit").Preload("SmallShopUser").Preload("ShippingAddress").Preload("OrderExpress").Preload("OrderExpress.OrderItems").Where("id = ?", id).First(&smallShopOrder).Error
	if err != nil {
		return
	}
	for ok, orderE := range smallShopOrder.OrderExpress {
		var itemExpress []small_shop_order.ItemExpress
		err = source.DB().Where("order_express_id = ?", orderE.ID).Find(&itemExpress).Error
		if err != nil {
			return
		}
		var itemExpressMap = make(map[uint]small_shop_order.ItemExpress)
		for _, itemExp := range itemExpress {
			itemExpressMap[itemExp.OrderItemID] = itemExp
		}
		for k, orderItem := range orderE.OrderItems {
			orderE.OrderItems[k].SendNum = itemExpressMap[orderItem.ID].Num
		}
		if strings.Contains(orderE.ExpressNo, ":") {
			orderE.ExpressNo = orderE.ExpressNo[:len(orderE.ExpressNo)-5]
		}
		smallShopOrder.OrderExpress[ok] = orderE
	}
	return
}

func Note(order model.SmallShopOrder) (err error) {
	err = source.DB().Model(&model.SmallShopOrder{}).Where("id = ?", order.ID).Update("note", order.Note).Error
	return
}

func GetSmallShopOrderByOrderSN(orderSN string) (err error, order model.SmallShopOrder) {
	err = source.DB().Model(&model.SmallShopOrder{}).Where("order_sn = ?", orderSN).First(&order).Error
	return
}

func UpdateSupplyOrderIDByOrderSN(orderSN string, supplyOrderID uint) (err error) {
	err = source.DB().Model(&model.SmallShopOrder{}).Where("order_sn = ? and supply_order_id = 0", orderSN).Update("supply_order_id", supplyOrderID).Error
	return
}

// 通过中台订单id获取小商店订单
func GetSmallShopOrderBySupplyOrderID(supplyOrderID uint) (err error, order model.SmallShopOrder) {
	err = source.DB().Model(&model.SmallShopOrder{}).Where("supply_order_id = ?", supplyOrderID).First(&order).Error
	return
}

func GetOrderItem(id uint) (err error, item model.SmallShopOrderItem) {
	err = source.DB().Where("id = ?", id).First(&item).Error
	return
}
