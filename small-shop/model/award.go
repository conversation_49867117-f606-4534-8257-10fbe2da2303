package model

import "yz-go/source"

type AwardMigration struct {
	source.Model
	SID            uint              `json:"sid" form:"sid" gorm:"column:sid;comment:商店id;"`
	SUID           uint              `json:"suid" form:"suid" gorm:"column:suid;comment:店主会员id;"`
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:店铺会员id;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:店铺订单id;"`
	ShopOrderID    uint              `json:"shop_order_id" gorm:"column:shop_order_id;comment:中台订单id;"`
	ShopPayTypeID  int               `json:"shop_pay_type_id" form:"shop_pay_type_id" gorm:"column:shop_pay_type_id;comment:中台订单支付方式;type:smallint;size:3;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;"`
	OrderCompleted int               `json:"order_completed" gorm:"column:order_completed;comment:订单是否完成 0:未完成 1：已完成;"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	OrderPayType   int               `json:"order_pay_type" gorm:"column:order_pay_type;comment:小商店订单支付方式 1:店主金额支付, 2:小商店分账支付;"`
	SettleAt       *source.LocalTime `json:"settle_at" gorm:"column:settle_at;comment:结算时间;"`
	PaidAt         *source.LocalTime `json:"paid_at" gorm:"column:statement_at;comment:支付时间;"`
	IsSplit        int               `json:"is_split" gorm:"column:is_split;default:0;comment:是否分账0否1是;"`
	IncomeAmount   uint              `json:"income_amount" gorm:"column:income_amount;comment:收入金额,分成结算之后放到这里;"`
	SplitStatus    int               `json:"split_status" gorm:"column:split_status;default:0;comment:分账状态 0待分账 1 成功;"`
	IncomeStatus   uint              `json:"income_status" gorm:"column:income_status;comment:收入状态 0 待结算 1成功 这里沟通之后分成结算到收入之后这里标记成功;"`
}

func (AwardMigration) TableName() string {
	return "small_shop_awards"
}

type Award struct {
	source.Model
	SID            uint              `json:"sid" form:"sid" gorm:"column:sid;comment:商店id;"`
	SUID           uint              `json:"suid" form:"suid" gorm:"column:suid;comment:店主会员id;"`
	Uid            uint              `json:"uid" form:"uid" gorm:"column:uid;comment:店铺会员id;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:店铺订单id;"`
	ShopOrderID    uint              `json:"shop_order_id" gorm:"column:shop_order_id;comment:中台订单id;"`
	ShopPayTypeID  int               `json:"shop_pay_type_id" form:"shop_pay_type_id" gorm:"column:shop_pay_type_id;comment:中台订单支付方式;type:smallint;size:3;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 -1:失效;"`
	OrderCompleted int               `json:"order_completed" gorm:"column:order_completed;comment:订单是否完成 0:未完成 1：已完成;"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	OrderPayType   int               `json:"order_pay_type" gorm:"column:order_pay_type;comment:小商店订单支付方式 1:店主金额支付, 2:小商店分账支付;"`
	SettleAt       *source.LocalTime `json:"settle_at" gorm:"column:settle_at;comment:结算时间;"`
	PaidAt         *source.LocalTime `json:"paid_at" gorm:"column:statement_at;comment:支付时间;"`
	SmallShop      SmallShop         `json:"small_shop" gorm:"foreignKey:SID"`
	ShopUserInfo   SmallShopUser     `json:"shop_user_info" gorm:"foreignKey:Uid"`
	IsSplit        int               `json:"is_split" gorm:"column:is_split;default:0;comment:是否分账0否1是;"`
	IncomeAmount   uint              `json:"income_amount" gorm:"column:income_amount;comment:收入金额,分成结算之后放到这里;"`
	SplitStatus    int               `json:"split_status" gorm:"column:split_status;default:0;comment:分账状态 0待分账 1 成功;"`
	IncomeStatus   uint              `json:"income_status" gorm:"column:income_status;comment:收入状态 0 待结算 1成功 这里沟通之后分成结算到收入之后这里标记成功;"`
}

func (Award) TableName() string {
	return "small_shop_awards"
}

type SettleAward struct {
	source.Model
	SID            uint              `json:"sid" form:"sid" gorm:"column:sid;comment:商店id;"`
	SUID           uint              `json:"suid" form:"suid" gorm:"column:suid;comment:店主会员id;"`
	ShopOrderID    uint              `json:"shop_order_id" gorm:"column:shop_order_id;comment:中台订单id;"`
	OrderID        uint              `json:"order_id" gorm:"column:order_id;comment:店铺订单id;"`
	Amount         uint              `json:"amount" gorm:"column:amount;comment:分成金额;"`
	Status         int               `json:"status" gorm:"column:status;comment:分红状态 0:未结算 1：已结算 2 子商户 -1:失效;"`
	OrderCompleted int               `json:"order_completed" gorm:"column:order_completed;comment:订单是否完成 0:未完成 1：已完成;"`
	SettleDays     int               `json:"settle_days" gorm:"column:settle_days;comment:结算天数;"`
	SettleAt       *source.LocalTime `json:"settle_at" gorm:"column:settle_at;comment:结算时间;"`
	IsSplit        int               `json:"is_split" gorm:"column:is_split;default:0;comment:是否分账0否1是;"`
	SplitStatus    int               `json:"split_status" gorm:"column:split_status;default:0;comment:分账状态 0待结算 1成功;"`
	SmallShopInfo  SmallShop         `json:"small_shop_info" gorm:"foreignKey:SID"`
}

func (SettleAward) TableName() string {
	return "small_shop_awards"
}
