package model

import (
	"gorm.io/gorm"
	"order/model"
	productModel "product/model"
	"region/address"
	"region/service"
	"yz-go/response"
	"yz-go/source"
)

type OrderStatus int8

const (
	WaitPay OrderStatus = iota
	WaitSend
	WaitReceive
	Completed
	Closed OrderStatus = -1
)

type RefundStatus int8

const (
	ClosedRefund RefundStatus = -1
	NotRefund    RefundStatus = iota
	Refunding
	RefundComplete
)

type SendStatus int8

const (
	NotSend SendStatus = iota
	Sending
	Sent
)

type SmallShopOrderMigration struct {
	source.Model
	OrderSN              uint         `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;index;"`                                                                            // 编号
	Key                  string       `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(100);size:100;"`                                                                      // 标识
	Title                string       `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);size:100;"`                                                                // 标题
	Status               OrderStatus  `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;index;"`                                                   // 订单状态
	Amount               uint         `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"`                                                                              // 订单总金额
	RefundAmount         uint         `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额(分);"`                                                           // 订单总金额
	ItemAmount           uint         `json:"item_amount" form:"item_amount" gorm:"column:item_amount;comment:商品市场价(分);"`                                                               // 商品市场价
	SupplyAmount         uint         `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(分);"`                                                           // 供货金额
	CostAmount           uint         `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(分);"`                                                                 // 成本金额
	Freight              uint         `json:"freight" form:"freight" gorm:"column:freight;comment:运费(分);"`                                                                                 // 运费(单位:分)
	ServiceFee           uint         `json:"service_fee" form:"service_fee" gorm:"column:service_fee;comment:服务费(分);"`                                                                   // 服务费
	GoodsCount           uint         `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:商品总数;"`                                                           // 商品总数
	SendStatus           SendStatus   `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态;type:smallint;size:3;"`                                      // 发货状态
	RefundStatus         RefundStatus `json:"refund_status" form:"refund_status" gorm:"column:refund_status;default:1;comment:退货退款状态 -1关闭 1正常 2售后中 3完成;type:smallint;size:3;"` // 退货退款状态状态
	Remark               string       `json:"remark" form:"remark" gorm:"column:desc;comment:备注（买家留言）;type:varchar(200);size:200;"`                                                     // 备注
	Note                 string       `json:"note" form:"note" gorm:"column:note;comment:商家备注;type:varchar(200);size:200;"`                                                               // 备注
	TechnicalServicesFee uint         `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"`                              // 技术服务费

	AmountDetail response.AmountDetail `json:"amount_detail"`
	PaidAt       *source.LocalTime     `json:"paid_at" gorm:"index;"`
	SentAt       *source.LocalTime     `json:"sent_at" gorm:"index;"`
	ReceivedAt   *source.LocalTime     `json:"received_at" gorm:"index;"`
	ClosedAt     *source.LocalTime     `json:"closed_at" gorm:"index;"`

	SupplyOrderID      uint `json:"supply_order_id" form:"supply_order_id" gorm:"column:supply_order_id;comment:供应链订单id;index;"`                   // 供应链中台订单id
	SmallShopUserID    uint `json:"small_shop_user_id" form:"small_shop_user_id" gorm:"column:small_shop_user_id;comment:用户id;index;"`                // 用户id
	TradeID            uint `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`                                                    // 交易id
	PayTypeID          int  `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"`                // 订单支付方式
	SmallShopPayInfoID uint `json:"small_shop_pay_info_id" form:"small_shop_pay_info_id" gorm:"column:small_shop_pay_info_id;comment:支付记录id;"`      // 支付记录id
	ShippingMethodID   int  `json:"shipping_method_id" form:"shipping_method_id" gorm:"column:shipping_method_id;comment:配送方式id;"`                  // 配送方式id
	ShippingAddressID  uint `json:"shipping_address_id" form:"shipping_address_id" gorm:"column:shipping_address_id;comment:收货地址id;"`               // 收货地址id
	SendTypeID         int  `json:"send_type_id" form:"send_type_id" gorm:"column:send_type_id;comment:订单发货方式;type:smallint;size:3;"`             // 订单发货方式
	CommentStatus      int  `json:"comment_status" form:"comment_status" gorm:"column:comment_status;comment:是否评价;default:0;type:smallint;size:3;"` // 是否评价
	CanRefund          int  `json:"can_refund" form:"can_refund" gorm:"column:can_refund;comment:可以退货（1是0否）;default:0;type:smallint;size:3;"`     // 可以退款（1是，0否）
	Lock               int  `json:"lock" form:"lock" gorm:"column:lock;default:0;type:int;"`
	//OrderBill               OrderBill             `json:"order_bill" gorm:"foreignKey:OrderID;references:ID"`
	IsUpdateShippingAddress int  `json:"is_update_shipping_address" form:"is_update_shipping_address" gorm:"column:is_update_shipping_address;comment:可以修改地址（1是，0否）;default:0;type:smallint;size:3;"` // 可以修改地址（1是，0否）
	SmallShopID             uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;default:0;comment:小商店id;"`
	Shopkeeper              int  `json:"shopkeeper" form:"shopkeeper" gorm:"column:shopkeeper;default:0;type:int;comment:1:店主自己的订单不产生店主提成;"`
	IsWxMiniSend            int  `json:"is_wx_mini_send" form:"is_wx_mini_send" gorm:"column:is_wx_mini_send;comment:是否推送发货信息到微信小程序成功0待推送1推送成功-1推送失败;default:0;index;"`
	ShareLiveRoomId         uint `json:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id 存在代表直播间下单;type:int;default:0;"` // 直播间id 存在代表直播间下单
	PluginID                int  `json:"plugin_id" gorm:"column:plugin_id;default:0;"`                                                                //商品表的插件id用于区别是不是课程商品 课程：18

}

func (SmallShopOrderMigration) TableName() string {
	return "small_shop_orders"
}

type SmallShopOrder struct {
	source.Model
	OrderSN              uint         `json:"order_sn" form:"order_sn" gorm:"column:order_sn;comment:编号;index;"`                                                                            // 编号
	Key                  string       `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(100);size:100;"`                                                                      // 标识
	Title                string       `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);size:100;"`                                                                // 标题
	Status               OrderStatus  `json:"status" form:"status" gorm:"column:status;default:0;comment:状态;type:smallint;size:3;index;"`                                                   // 订单状态
	Amount               uint         `json:"amount" form:"amount" gorm:"column:amount;comment:订单总金额(分);"`                                                                              // 订单总金额
	RefundAmount         uint         `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额(分);"`                                                           // 订单总金额
	ItemAmount           uint         `json:"item_amount" form:"item_amount" gorm:"column:item_amount;comment:商品市场价(分);"`                                                               // 商品市场价
	SupplyAmount         uint         `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(分);"`                                                           // 供货金额
	CostAmount           uint         `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(分);"`                                                                 // 成本金额
	Freight              uint         `json:"freight" form:"freight" gorm:"column:freight;comment:运费(分);"`                                                                                 // 运费(单位:分)
	ServiceFee           uint         `json:"service_fee" form:"service_fee" gorm:"column:service_fee;comment:服务费(分);"`                                                                   // 服务费
	GoodsCount           uint         `json:"goods_count" form:"goods_count" gorm:"column:goods_count;default:0;comment:商品总数;"`                                                           // 商品总数
	SendStatus           SendStatus   `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态;type:smallint;size:3;"`                                      // 发货状态
	RefundStatus         RefundStatus `json:"refund_status" form:"refund_status" gorm:"column:refund_status;default:1;comment:退货退款状态 -1关闭 1正常 2售后中 3完成;type:smallint;size:3;"` // 退货退款状态状态
	Remark               string       `json:"remark" form:"remark" gorm:"column:desc;comment:备注（买家留言）;type:varchar(200);size:200;"`                                                     // 备注
	Note                 string       `json:"note" form:"note" gorm:"column:note;comment:商家备注;type:varchar(200);size:200;"`                                                               // 备注
	TechnicalServicesFee uint         `json:"technical_services_fee" form:"technical_services_fee" gorm:"column:technical_services_fee;comment:技术服务费(分);"`                              // 技术服务费

	PaidAt     *source.LocalTime `json:"paid_at" gorm:"index;"`
	SentAt     *source.LocalTime `json:"sent_at" gorm:"index;"`
	ReceivedAt *source.LocalTime `json:"received_at" gorm:"index;"`
	ClosedAt   *source.LocalTime `json:"closed_at" gorm:"index;"`

	SupplyOrderID       uint                     `json:"supply_order_id" form:"supply_order_id" gorm:"column:supply_order_id;comment:供应链订单id;index;"`                   // 供应链中台订单id
	SmallShopUserID     uint                     `json:"small_shop_user_id" form:"small_shop_user_id" gorm:"column:small_shop_user_id;comment:用户id;index;"`                // 用户id
	TradeID             uint                     `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`                                                    // 交易id
	PayTypeID           int                      `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"`                // 订单支付方式
	SmallShopPayInfoID  uint                     `json:"small_shop_pay_info_id" form:"small_shop_pay_info_id" gorm:"column:small_shop_pay_info_id;comment:支付记录id;"`      // 支付记录id
	ShippingMethodID    int                      `json:"shipping_method_id" form:"shipping_method_id" gorm:"column:shipping_method_id;comment:配送方式id;"`                  // 配送方式id
	ShippingAddressID   uint                     `json:"shipping_address_id" form:"shipping_address_id" gorm:"column:shipping_address_id;comment:收货地址id;"`               // 收货地址id
	SendTypeID          int                      `json:"send_type_id" form:"send_type_id" gorm:"column:send_type_id;comment:订单发货方式;type:smallint;size:3;"`             // 订单发货方式
	CommentStatus       int                      `json:"comment_status" form:"comment_status" gorm:"column:comment_status;comment:是否评价;default:0;type:smallint;size:3;"` // 是否评价
	CanRefund           int                      `json:"can_refund" form:"can_refund" gorm:"column:can_refund;comment:可以退货（1是0否）;default:0;type:smallint;size:3;"`     // 可以退款（1是，0否）
	Lock                int                      `json:"lock" form:"lock" gorm:"column:lock;default:0;type:int;"`
	AmountDetail        response.AmountDetail    `json:"amount_detail"`
	SmallShopOrderItems SmallShopOrderItems      `json:"small_shop_order_items"`
	OrderExpress        model.OrderExpress       `json:"order_express" gorm:"foreignKey:SupplyOrderID"`
	ShippingAddress     SmallShopShippingAddress `json:"shipping_address" gorm:"foreignKey:ShippingAddressID"`
	SmallShop           SmallShop                `json:"small_shop" gorm:"foreignKey:SmallShopID"`
	//OrderBill               OrderBill             `json:"order_bill" gorm:"foreignKey:OrderID;references:ID"`
	IsUpdateShippingAddress int  `json:"is_update_shipping_address" form:"is_update_shipping_address" gorm:"column:is_update_shipping_address;comment:可以修改地址（1是，0否）;default:0;type:smallint;size:3;"` // 可以修改地址（1是，0否）
	SmallShopID             uint `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;default:0;comment:小商店id;"`
	Shopkeeper              int  `json:"shopkeeper" form:"shopkeeper" gorm:"column:shopkeeper;default:0;type:int;comment:1:店主自己的订单不产生店主提成;"`
	IsWxMiniSend            int  `json:"is_wx_mini_send" form:"is_wx_mini_send" gorm:"column:is_wx_mini_send;comment:是否推送发货信息到微信小程序成功0待推送1推送成功-1推送失败;default:0;index;"`
	ShareLiveRoomId         uint `json:"share_live_room_id" gorm:"column:share_live_room_id;comment:直播间id 存在代表直播间下单;type:int;default:0;"` // 直播间id 存在代表直播间下单	PluginID        int             `json:"plugin_id" gorm:"column:plugin_id;default:0;"` //商品表的插件id用于区别是不是课程商品 课程：18
	PluginID                int  `json:"plugin_id" gorm:"column:plugin_id;default:0;"`                                                                //商品表的插件id用于区别是不是课程商品 课程：18

}

func (o *SmallShopOrder) AfterCreate(tx *gorm.DB) (err error) {
	timestamp := uint(o.CreatedAt.Unix())
	orderSn := o.ID + timestamp*110
	err = tx.Model(&o).Update("order_sn", orderSn).Error
	return
}

type SmallShopOrderItemMigration struct {
	source.Model
	Key             string `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(100);size:100;"`                      // 标识
	Title           string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);size:100;"`                // 名
	SkuTitle        string `json:"sku_title" form:"sku_title" gorm:"column:sku_title;comment:sku标题;type:varchar(100);size:100;"` // 规格名
	Unit            string `json:"unit" form:"unit" gorm:"column:unit;comment:单位;type:varchar(20);size:20;"`                     // 单位
	Qty             uint   `json:"qty" form:"qty" gorm:"column:qty;comment:商品数量;"`                                             // 商品数量
	Amount          uint   `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`                                        // 总价(分)
	RefundAmount    uint   `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额;"`               // 退款金额 (分)                           // 总价(元)
	DiscountAmount  uint   `json:"discount_amount" form:"discount_amount" gorm:"column:discount_amount;comment:优惠金额(元);"`     // 优惠金额(分)
	DeductionAmount uint   `json:"deduction_amount" form:"deduction_amount" gorm:"column:deduction_amount;comment:抵扣金额(元);"`  // 抵扣金额(分)

	CostAmount    uint                  `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(元);"`                                                      // 成本(分)
	PaymentAmount uint                  `json:"payment_amount" form:"payment_amount" gorm:"column:payment_amount;comment:均摊支付金额(元);"`                                         // 均摊支付金额(分)
	SupplyAmount  uint                  `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(元);"`                                                // 成本(分)
	ImageUrl      string                `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`                                     // 图片地址
	SendStatus    int                   `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态 0未发货 1已发货 2部分发货;type:smallint;size:3;"` // 发货状态
	AmountDetail  response.AmountDetail `json:"amount_detail"`
	RefundStatus  RefundStatus          `json:"refund_status" form:"refund_status" gorm:"column:refund_status;default:0;comment:退货退款状态状态 1正常 2售后中 3完成;type:smallint;size:3;"` // 退货退款状态状态

	TradeID                uint          `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`                                                             // 交易id
	SmallShopUserID        uint          `json:"small_shop_user_id" form:"small_shop_user_id" gorm:"column:small_shop_user_id;comment:用户id;"`                               // 用户id
	SmallShopProductSaleID uint          `json:"small_shop_product_sale_id" form:"small_shop_product_sale_id" gorm:"column:small_shop_product_sale_id;comment:小商店商品id;"` // 小商店商品id
	SmallShopOrderID       uint          `json:"small_shop_order_id" form:"small_shop_order_id" gorm:"column:small_shop_order_id;comment:订单id;"`                            // 小商店订单id
	SmallShopID            uint          `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店id;"`                                            // 小商店id
	ProductID              uint          `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;"`                                                       // 产品id
	SkuID                  uint          `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku id;"`                                                                   // sku id
	OriginalSkuID          uint          `json:"original_sku_id" form:"original_sku_id" gorm:"column:original_sku_id;comment:第三方sku id;"`                                  // 第三方sku id
	CanRefund              uint          `json:"can_refund" form:"can_refund" gorm:"column:can_refund;default:0;comment:可以退款（1是0否）;type:smallint;size:3;"`              // 可以退款（1是0否）
	CommentStatus          int           `json:"comment_status" form:"comment_status" gorm:"column:comment_status;comment:是否评价;default:0;type:smallint;size:3;"`          // 是否评价
	Options                model.Options `json:"options"`
}

func (SmallShopOrderItemMigration) TableName() string {
	return "small_shop_order_items"
}

type SmallShopOrderItems []SmallShopOrderItem
type SmallShopOrderItem struct {
	source.Model
	Key             string `json:"key" form:"key" gorm:"column:key;comment:标识;type:varchar(100);size:100;"`                      // 标识
	Title           string `json:"title" form:"title" gorm:"column:title;comment:标题;type:varchar(100);size:100;"`                // 名
	SkuTitle        string `json:"sku_title" form:"sku_title" gorm:"column:sku_title;comment:sku标题;type:varchar(100);size:100;"` // 规格名
	Unit            string `json:"unit" form:"unit" gorm:"column:unit;comment:单位;type:varchar(20);size:20;"`                     // 单位
	Qty             uint   `json:"qty" form:"qty" gorm:"column:qty;comment:商品数量;"`                                             // 商品数量
	Amount          uint   `json:"amount" form:"amount" gorm:"column:amount;comment:总价;"`                                        // 总价(分)
	RefundAmount    uint   `json:"refund_amount" form:"refund_amount" gorm:"column:refund_amount;comment:退款金额;"`               // 退款金额 (分)                           // 总价(元)
	DiscountAmount  uint   `json:"discount_amount" form:"discount_amount" gorm:"column:discount_amount;comment:优惠金额(元);"`     // 优惠金额(分)
	DeductionAmount uint   `json:"deduction_amount" form:"deduction_amount" gorm:"column:deduction_amount;comment:抵扣金额(元);"`  // 抵扣金额(分)

	CostAmount    uint   `json:"cost_amount" form:"cost_amount" gorm:"column:cost_amount;comment:成本金额(元);"`                                                      // 成本(分)
	PaymentAmount uint   `json:"payment_amount" form:"payment_amount" gorm:"column:payment_amount;comment:均摊支付金额(元);"`                                         // 均摊支付金额(分)
	SupplyAmount  uint   `json:"supply_amount" form:"supply_amount" gorm:"column:supply_amount;comment:供货金额(元);"`                                                // 成本(分)
	ImageUrl      string `json:"image_url" form:"image_url" gorm:"column:image_url;comment:图片地址;type:varchar(255);size:255;"`                                     // 图片地址
	SendStatus    int    `json:"send_status" form:"send_status" gorm:"column:send_status;default:0;comment:发货状态 0未发货 1已发货 2部分发货;type:smallint;size:3;"` // 发货状态

	RefundStatus RefundStatus `json:"refund_status" form:"refund_status" gorm:"column:refund_status;default:0;comment:退货退款状态状态 1正常 2售后中 3完成;type:smallint;size:3;"` // 退货退款状态状态

	TradeID                uint                  `json:"trade_id" form:"trade_id" gorm:"column:trade_id;comment:交易id;"`                                                             // 交易id
	SmallShopUserID        uint                  `json:"small_shop_user_id" form:"small_shop_user_id" gorm:"column:small_shop_user_id;comment:用户id;"`                               // 用户id
	SmallShopProductSaleID uint                  `json:"small_shop_product_sale_id" form:"small_shop_product_sale_id" gorm:"column:small_shop_product_sale_id;comment:小商店商品id;"` // 小商店商品id
	SmallShopOrderID       uint                  `json:"small_shop_order_id" form:"small_shop_order_id" gorm:"column:small_shop_order_id;comment:订单id;"`                            // 小商店订单id
	SmallShopID            uint                  `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店id;"`                                            // 小商店id
	ProductID              uint                  `json:"product_id" form:"product_id" gorm:"column:product_id;comment:产品id;"`                                                       // 产品id
	SkuID                  uint                  `json:"sku_id" form:"sku_id" gorm:"column:sku_id;comment:sku id;"`                                                                   // sku id
	OriginalSkuID          uint                  `json:"original_sku_id" form:"original_sku_id" gorm:"column:original_sku_id;comment:第三方sku id;"`                                  // 第三方sku id
	CanRefund              uint                  `json:"can_refund" form:"can_refund" gorm:"column:can_refund;default:0;comment:可以退款（1是0否）;type:smallint;size:3;"`              // 可以退款（1是0否）
	CommentStatus          int                   `json:"comment_status" form:"comment_status" gorm:"column:comment_status;comment:是否评价;default:0;type:smallint;size:3;"`          // 是否评价
	AmountDetail           response.AmountDetail `json:"amount_detail"`
	Options                model.Options         `json:"options"`
	Sku                    productModel.Sku      `json:"sku"`
	Product                productModel.Product  `json:"product"`
}

type SmallShopShippingAddress struct {
	address.Address
}

func (a *SmallShopShippingAddress) AfterFind(tx *gorm.DB) (err error) {
	a.Province = service.GetRegionName(a.ProvinceId)
	a.City = service.GetRegionName(a.CityId)
	a.County = service.GetRegionName(a.CountyId)
	if a.TownId == 0 {
		a.Town = ""
	}
	return
}

// ShippingAddressLog 订单收货地址修改日志
type ShippingAddressLog struct {
	address.Address
	CreatedAt     *source.LocalTime `json:"created_at"`
	UpdatedAt     *source.LocalTime `json:"updated_at"`
	UserID        uint              `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`                    // 用户id
	OrderID       uint              `json:"order_id" form:"order_id" gorm:"column:order_id;comment:订单id;"`                 // 订单id
	ThirdOrderSN  string            `json:"third_order_sn" form:"third_order_sn" gorm:"column:third_order_sn;comment:编号;"` // 采购端单号
	OldRealname   string            `json:"old_realname"`
	OldMobile     string            `json:"old_mobile"`
	OldCountryId  int               `json:"old_country_id"`  // 国id
	OldProvinceId int               `json:"old_province_id"` // 省id
	OldCityId     int               `json:"old_city_id"`     // 市id
	OldCountyId   int               `json:"old_county_id"`   // 区id
	OldTownId     int               `json:"old_town_id"`     // 街id
	OldProvince   string            `json:"old_province" `   // 省
	OldCity       string            `json:"old_city"`        // 市
	OldCounty     string            `json:"old_county"`      //区
	OldTown       string            `json:"old_town"`        //街
	OldDetail     string            `json:"old_detail"`      // 地址
	OldLng        string            `json:"old_lng"`         // 经度
	OldLat        string            `json:"old_lat"`         // 维度
}

func (ShippingAddressLog) TableName() string {
	return "small_shop_address_log"
}

func GetItemSendStatusName(status int) string {
	statusNameMap := map[int]string{
		0: "待发货",
		1: "已发货",
		2: "部分发货",
	}

	return statusNameMap[status]

}

func GetStatusName(status OrderStatus) string {
	statusNameMap := map[OrderStatus]string{
		WaitPay:     "待付款",
		WaitSend:    "待发货",
		WaitReceive: "待收货",
		Completed:   "已完成",
		Closed:      "已关闭",
	}
	return statusNameMap[status]
}

type Button struct {
	Title string `json:"title"`
	Url   string `json:"url"`
}

func GetButton(status OrderStatus) interface{} {
	buttonMap := map[OrderStatus][]Button{
		// WaitPay:     {{Title: "确认付款", Url: "smallShop/order/pay"}, {Title: "关闭订单", Url: "smallShop/order/close"}},
		WaitPay:     {{Title: "关闭订单", Url: "smallShop/order/close"}},
		WaitSend:    {{Title: "等待发货", Url: ""}, {Title: "全额退款", Url: "smallShop/order/refund"}},
		WaitReceive: {{Title: "确认收货", Url: "smallShop/order/receive"}, {Title: "修改物流", Url: "order/updateOrderExpress"}, {Title: "全额退款", Url: "smallShop/order/refund"}},
		Completed:   {{Title: "已完成", Url: ""}, {Title: "全额退款", Url: "smallShop/order/refund"}},
		Closed:      {{Title: "已关闭", Url: ""}},
	}
	return buttonMap[status]
}

type SmallShopOrderPayInfo struct {
	source.Model
	SmallShopOrderID   uint `json:"small_shop_order_id"`
	SmallShopPayInfoID uint `json:"small_shop_pay_info_id"`
}
