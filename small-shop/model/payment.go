package model

import (
	"gorm.io/gorm"
	"time"
	"yz-go/source"
)

type PayStatus int

const (
	NotPay PayStatus = iota
	Paid
	InvalidPay   = -1
	Refunded     = -2
	PartRefunded = -3
)

// SmallShopPayInfo
// 支付信息
type SmallShopPayInfo struct {
	source.Model
	Status            PayStatus         `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"`
	PaySN             uint              `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;"`
	Amount            uint              `json:"amount" form:"amount" gorm:"column:amount;comment:支付总金额(分);"`
	RefundedAmount    uint              `json:"refunded_amount" form:"refunded_amount" gorm:"column:refunded_amount;comment:已退款金额(分);"`
	SmallShopUserID   uint              `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`
	SmallShopID       uint              `json:"small_shop_id" form:"small_shop_id" gorm:"column:small_shop_id;comment:小商店id;"`
	PayTypeID         int               `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"`
	TransactionId     string            `json:"transaction_id" form:"transaction_id" gorm:"column:transaction_id;comment:微信支付订单号;"`
	Type              uint              `json:"type" form:"type" gorm:"column:type;comment:用来区分什么支付: 30000 数据通-聚合支付（无论pay_type_id是多少都是聚合支付）;"`
	ExpireAt          *source.LocalTime `json:"expire_at"`
	PaidAt            *source.LocalTime `json:"paid_at"`
	RefundAt          *source.LocalTime `json:"refund_at"`
	PaymentStoreCode  string            `json:"payment_store_code" gorm:"column:payment_store_code"` // 聚合支付数据通商户号
	IsSplitStatus     int               `json:"is_split_status" form:"is_split_status" gorm:"column:is_split_status;comment:是否是分账支付单:0不是1是;default:0;type:int(11);"`
	SplitCreateStatus int               `json:"split_create_status" form:"split_create_status" gorm:"column:split_create_status;comment:分账创建状态:0待支付1已支付待创建记录，2已创建分账记录;default:0;type:int(11);"`
	ErrorMsg          string            `json:"error_msg" form:"error_msg" gorm:"column:error_msg;comment:错误分账创建的内容;type:text;"` //错误内容
}

func (p *SmallShopPayInfo) AfterCreate(tx *gorm.DB) (err error) {
	p.ExpireAt = &source.LocalTime{Time: time.Now().Add(time.Hour)}

	timestamp := uint(p.CreatedAt.Unix())
	paySn := p.ID + timestamp*110
	err = tx.Model(&p).Update("pay_sn", paySn).Error
	return
}

type SmallShopWechatRefund struct {
	source.Model
	RefundId  string    `json:"refund_id" form:"refund_id" gorm:"column:refund_id;comment:微信支付退款号;"`
	PaySN     string    `json:"pay_sn" form:"pay_sn" gorm:"column:pay_sn;comment:编号;"`
	Status    PayStatus `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"`
	RefundSN  uint      `json:"refund_sn" form:"refund_sn" gorm:"column:refund_sn;comment:退款编号;"`
	Amount    uint      `json:"amount" form:"amount" gorm:"column:amount;comment:支付总金额(分);"`
	UserID    uint      `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户id;"`
	PayTypeID int       `json:"pay_type_id" form:"pay_type_id" gorm:"column:pay_type_id;comment:订单支付方式;type:smallint;size:3;"`
	Msg       string    `json:"msg" form:"msg" gorm:"column:msg;comment:退款失败原因;type:text;"`
}

func (as *SmallShopWechatRefund) AfterCreate(tx *gorm.DB) (err error) {
	timestamp := uint(as.CreatedAt.Unix())
	RefundSN := as.ID + timestamp*110
	err = tx.Model(&as).Update("refund_sn", RefundSN).Error
	return
}

func GetPayStatusName(status PayStatus) string {
	Names := map[PayStatus]string{
		Refunded:   "已退款",
		InvalidPay: "已关闭",
		NotPay:     "待支付",
		Paid:       "已付款",
	}
	return Names[status]
}
