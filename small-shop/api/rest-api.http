### 获取基础设置
GET {{api}}/smallShop/findSetting
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

### 更新基础设置
PUT {{api}}/smallShop/updateSetting
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

{
  "id": 0,
  "value": {
    "switch": 1,
    "level_limit": [
      {"id": 1}
    ],
    "order_pay_type": 1,
    "settle_days": 7,
    "pay_types": [
      {"id": 12}
    ],
    "independent": 1,
    "free_review": 1,
    "open_pay": 1
  }
}

### 获取会员等级列表
GET {{api}}/user/getUserLevelListNotPage
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 获取支付方式
GET {{api}}/smallShop/getPayTypes
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 获取小程序设置
GET {{api}}/smallShop/findWxSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 修改小程序设置
PUT {{api}}/smallShop/updateWxSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

{
  "id": 0,
  "value": {
    "isopen": 1
  }
}

### 获取全部小商店
GET {{api}}/smallShop/getAllSmallShop
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjQ0NDEwOTUsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzgzNTI5NX0.j9kmev6fXHBLQ7BvKb7MaAEYH8ERv3StF0S16uz8cdo
x-User-Id: 1

### 获取小商店管理
GET {{api}}/smallShop/getSmallShopsList?suid=1
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 基础设置获取小商店列表
GET {{api}}/smallShop/getSmallShopListBySetting?shop_title=小店1
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZh5YSIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY3NTUwMDA0NSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjc0ODk0MjQ1fQ.Q1kP2jgI8OVng3jUA8S-vwdZOTuu7Vp7WRDTxLcy48E
x-User-Id: 1

### 获取店主管理
GET {{api}}/smallShop/getShopkeepersList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 获取收益管理
GET {{api}}/smallShop/getAwardsList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 获取申请管理
GET {{api}}/smallShop/getAppliersList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 审核通过
PUT {{api}}/smallShop/passApply
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjU5MTQwMzgsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTMwODIzOH0.5an_q740_nhRgGrRsFHRqGQ7GQsgzApuum-tkqI-JnQ
x-User-Id: 1

{
  "id": 2
}

### 驳回审核
PUT {{api}}/smallShop/failApply
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

{
  "id": 1
}

### 小商店账号登录
POST {{api}}/api/smallShop/user/login
Content-Type: application/json
x-User-Id: 1

{
  "username": "123456789",
  "password": "123456789"
}

### 小商店账号注册
POST {{api}}/api/smallShop/user/register
Content-Type: application/json
x-User-Id: 1

{
  "username": "123456789",
  "password": "123456789",
  "nickname": "123456789",
  "captcha_code": "0000",
  "captcha_key": "175195122301669799615"
}

### 小商店发送验证码
POST {{api}}/api/smallShop/user/sendCode
Content-Type: application/json
x-User-Id: 1

{
  "mobile": "17519512230",
  "type": "register"
}

### 获取小程序用户详情
POST {{api}}/api/smallShop/user/info
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1



### 添加收货地址
POST {{api}}/api/smallShop/user/address/add
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "realname": "科技创新城",
  "mobile": "17519512230",
  "country_id": 86,
  "province_id": 11,
  "province": "北京市",
  "city_id": 1101,
  "city": "市辖区",
  "county_id": 110101,
  "county": "东城区",
  "town_id": 110101001,
  "town": "东华门街道",
  "detail": "均信担保大厦",
  "is_default": true
}

### 小商店会员添加购物车
POST {{api}}/api/smallShop/shoppingCart/add
Content-Type: application/json
x-token: {{wxmi-token}}
x-user-id: 1

{
  "shopping_carts": [
    {
      "small_shop_id": 1,
      "product_id": 7,
      "small_shop_product_sale_id": 1,
      "sku_id": 7,
      "qty": 1,
      "checked": 1
    }
  ]
}

### 小商店会员获取分类
GET {{api}}/api/smallShop/category/getListByParentID?level=1&sid=5
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

### 小商店会员获取购物车列表
GET {{api}}/api/smallShop/shoppingCart/list?small_shop_id=5
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

### 小商店会员批量删除购物车
POST {{api}}/api/smallShop/shoppingCart/delete/batch
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

{
  "ids": [2]
}

### 小商店会员更新购物车
POST {{api}}/api/smallShop/shoppingCart/update
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

{
  "id": 2,
  "qty": 2
}

### 小商店购物车结算
POST {{api}}/api/smallShop/trade/checkout
Content-Type: application/json
x-token: {{wxmi-token}}
x-user-id: 1

{
  "buy_id": 0
}

###小商店购物车下单
POST {{api}}/api/smallShop/trade/confirm
Content-Type: application/json
x-token: {{wxmi-token}}
x-user-id: 1

{
  "buy_id": 0
}

###小商店收银台信息
POST {{api}}/api/smallShop/trade/cashier
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

{
  "order_ids": [3]
}

###小商店会员订单列表
GET {{api}}/api/smallShop/order/list
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

###小商店会员订单快递信息
GET {{api}}/api/smallShop/order/expressInfo?id=3
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

###小商店会员确认收货
POST {{api}}/api/smallShop/order/receive
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

{
  "order_id": 3
}

###小商店会员关闭订单
POST {{api}}/api/smallShop/order/close
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

{
  "small_shop_order_id": 3
}

### 获取小商店会员列表
GET {{api}}/smallShop/getUserList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 加入黑名单
PUT {{api}}/smallShop/changeBlack
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

{
  "id": 1
}

### 加入白名单
PUT {{api}}/smallShop/changeWhite
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

{
  "id": 1
}

### 获取用户信息
GET {{api}}/smallShop/getUserInfo?id=1
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

### 获取订单列表
GET {{api}}/smallShop/getOrderList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZh5YSIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY2OTcwNjA4NCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjY5MTAwMjg0fQ.vvzupCeVS1KV0R557zDd157CU52QM0PZSaQMMbtS5qU
x-User-Id: 1

### 获取订单详情
GET {{api}}/smallShop/getOrderDetail?id=2
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZh5YSIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY2OTcwNjA4NCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjY5MTAwMjg0fQ.vvzupCeVS1KV0R557zDd157CU52QM0PZSaQMMbtS5qU
x-User-Id: 1

### 小商店订单店主确认支付
POST {{api}}/api/smallShop/smallShopOrder/pay
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "small_shop_order_id": 3
}

### 小商店订单店主确认收货
POST {{api}}/api/smallShop/smallShopOrder/receive
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

{
  "small_shop_order_id": 6
}

### 开启小商店
PUT {{api}}/smallShop/shopOpen
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

{
  "id": 1
}

### 关闭小商店
PUT {{api}}/smallShop/shopClose
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjM3MjkxMDAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzEyMzMwMH0.uKU1lpTK0zhIhxBTjC6Lf0EW5fy_hQz23qk6UXFOkmQ
x-User-Id: 1

{
  "id": 1
}

### 修改订单收货信息
POST {{api}}/smallShop/updateShippingAddress
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZh5YSIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY2OTcwNjA4NCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjY5MTAwMjg0fQ.vvzupCeVS1KV0R557zDd157CU52QM0PZSaQMMbtS5qU

{
  "order_id": 2,
  "id": 1,
  "realname": "测试1234",
  "mobile": "15945596368",
  "country_id": 86,
  "province_id": 21,
  "city_id": 2101,
  "county_id": 210102,
  "town_id": 210102001,
  "province": "辽宁省",
  "city": "沈阳市",
  "county": "和平区",
  "town": "浑河湾街道",
  "detail": "详细地址"
}

### 获取订单收货地址修改记录
GET {{api}}/smallShop/getShippingAddressLog?order_id=2
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjQ0NDEwOTUsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzgzNTI5NX0.j9kmev6fXHBLQ7BvKb7MaAEYH8ERv3StF0S16uz8cdo
x-User-Id: 1

### 支付记录
POST {{api}}/smallShop/finance/getPaymentRecord
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJBdXRob3JpdHlJZCI6Ijg4OCIsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjQ0NDEwOTUsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2MzgzNTI5NX0.j9kmev6fXHBLQ7BvKb7MaAEYH8ERv3StF0S16uz8cdo

{
  "order_id": 2
}

### 在线选品的商品列表使用原接口 api/product/list

### 前端:提交小商店申请
POST {{api}}/api/smallShop/apply/subApply
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjU5MTE5NzksImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTMwNjE3OX0.QtFAqXHDa-S3sZdcENBz-ZM6brdF_peMthTgr8qSnJY
x-User-Id: 1

{
  "title": "申请小商店测试"
}

### 前端:获取可提交申请状态
GET {{api}}/api/smallShop/apply/getApplyStatus
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

### 前端:小商店中心
GET {{api}}/api/smallShop/center/getCenterInfo
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNDgwODIsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ0MjI4Mn0.sRR3CjrI0uOVFD4UFHRB1S4UuT5AjHzOquclzk57JYI

### 前端:小商店按钮
GET {{api}}/api/smallShop/center/showCenter
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjU5MTE5NzksImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTMwNjE3OX0.QtFAqXHDa-S3sZdcENBz-ZM6brdF_peMthTgr8qSnJY

### 前端:查询小商店设置
GET {{api}}/api/smallShop/setting/getShopSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNDgwODIsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ0MjI4Mn0.sRR3CjrI0uOVFD4UFHRB1S4UuT5AjHzOquclzk57JYI

### 前端:更新小商店设置
PUT {{api}}/api/smallShop/setting/updateShopSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2Njc4OTMxNDIsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NzI4NzM0Mn0.XrHF7rF0V2K2F5jpWYS0P9qt25VzaYxOHROPUJj7_sU

{
  "id": 2,
  "value": {
    "shop_name": "商店设置测试",
    "qr_code": "https://ysm-1251768088.cos.ap-guangzhou.myqcloud.com/images/1/2022/06/3899b0babe1fd6109fc41ce2edba26e9.png",
    "special_album": 1,
    "all_product": 1,
    "hot_product": 1,
    "new_product": 1,
    "first_category": 1
  }
}

### 前端:查询定价策略
GET {{api}}/api/smallShop/setting/getShopProductSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2Nzc2NTc0MDMsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NzA1MTYwM30.9sn88lt7FRjdVpGHFqZFAMDRgvR8eJA2BIKBMtw6AXQ

### 前端:更新定价策略
PUT {{api}}/api/smallShop/setting/updateShopProductSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2Nzc2NTc0MDMsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NzA1MTYwM30.9sn88lt7FRjdVpGHFqZFAMDRgvR8eJA2BIKBMtw6AXQ

{
  "id": 1,
  "value": {
    "is_agreement_price": 1,
    "is_origin_price": 1,
    "is_guide_price": 1,
    "is_activity_price": 1,
    "agreement_price_ratio": 111,
    "origin_price_ratio": 111,
    "guide_price_ratio": 111,
    "activity_price_ratio": 111,
    "tip": 1
  }
}

### 前端:专辑库
GET {{api}}/api/smallShop/album/getAlbumList?page=1&pageSize=10
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MDExNTM2MTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcwMDU0NzgxN30.y40O-lw5x2QI7uk9Yne8klKZEwxD42ks0GCOXzOert8

### 前端:获取全部专辑标签, 无分页
GET {{api}}/api/smallShop/album/getAllTag
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjY2MDAwNTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTk5NDI1N30.ZDdIJC7s7GFu30LiWtBjxEjHJKrLcZ3KSgBwPW6uCjg

### 前端:获取已导入的专辑
GET {{api}}/api/smallShop/album/getSmallShopAlbumListByUid?page=1&pageSize=10
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MDExNTM2MTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcwMDU0NzgxN30.y40O-lw5x2QI7uk9Yne8klKZEwxD42ks0GCOXzOert8

### 前端:获取已导入专辑的标签
GET {{api}}/api/smallShop/album/getAllTagByImported
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjY2MDAwNTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTk5NDI1N30.ZDdIJC7s7GFu30LiWtBjxEjHJKrLcZ3KSgBwPW6uCjg

### 前端:获取已导入专辑的标签
GET {{api}}/api/smallShop/album/getAllTagByImported
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjY2MDAwNTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTk5NDI1N30.ZDdIJC7s7GFu30LiWtBjxEjHJKrLcZ3KSgBwPW6uCjg

### 前端:导入专辑
POST {{api}}/api/smallShop/album/importAlbum
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNDgwODIsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ0MjI4Mn0.sRR3CjrI0uOVFD4UFHRB1S4UuT5AjHzOquclzk57JYI

{
  "album_ids": [3]
}

### 前端:导入全部专辑
POST {{api}}/api/smallShop/album/importAllAlbum
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjY2MDAwNTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTk5NDI1N30.ZDdIJC7s7GFu30LiWtBjxEjHJKrLcZ3KSgBwPW6uCjg

### 前端:移除专辑
DELETE {{api}}/api/smallShop/album/removeAlbum
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzcwNDgwODIsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NjQ0MjI4Mn0.sRR3CjrI0uOVFD4UFHRB1S4UuT5AjHzOquclzk57JYI

{
  "album_ids": [2]
}

### 前端:通过分类等级和上级id 获取分类
GET {{api}}/api/smallShop/category/getCategoryByParentID?level=1&parent_id=0
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjY2MDAwNTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTk5NDI1N30.ZDdIJC7s7GFu30LiWtBjxEjHJKrLcZ3KSgBwPW6uCjg

### 前端:获取商品列表-在线选品 [利润率:origin_rate, 赚:profit]
GET {{api}}/api/smallShop/product/getProductList?pageSize=15&page=1
Content-Type: application/json
x-token:{{f-token}}

### 前端:获取商店商品列表-商品管理
GET {{api}}/api/smallShop/product/getMyProductList?pageSize=15&page=1
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzU1ODI0MDksImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NDk3NjYwOX0.EYRnDJt7Gtw_GIROY3uuBdrxRQ8ggEMTKMVs3wdkECI

### 前端:店主订单列表
GET {{api}}/api/smallShop/smallShopOrder/list
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2ODkxNDU1ODQsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY4ODUzOTc4NH0.2i5UXnYv7A7bLNw0KPlFQMV1rwwHW7MMS5dwfxmuv04

### 前端:批量or单个 删除商品
DELETE {{api}}/api/smallShop/product/deleteProduct
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMTBkYWNlYTMtMTY2YS00YWU4LWExOWUtNWE5YWFjZWU4YjgzIiwiVXNlcm5hbWUiOiIiLCJJRCI6MTkwLCJBcHBJRCI6MCwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTcwMDc5NDg3NCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzAwMTg5MDc0fQ.bC7QdIClwMAs2XSBZiY1974jRIJ7W9vSe-jMd4SC-Sk

{
  "product_ids": [44074]
}

### 前端:商店商品详情
GET {{api}}/api/smallShop/product/findProduct?id=324
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjY2MDAwNTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTk5NDI1N30.ZDdIJC7s7GFu30LiWtBjxEjHJKrLcZ3KSgBwPW6uCjg

### 前端:改价
POST {{api}}/api/smallShop/product/changePrice
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NjY2MDAwNTcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2NTk5NDI1N30.ZDdIJC7s7GFu30LiWtBjxEjHJKrLcZ3KSgBwPW6uCjg

{
  "product_id": 324,
  "change_price": 44444
}

### 前端:批量改价
POST {{api}}/api/smallShop/product/batchChangePrice
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2Njg1ODEwMzcsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY2Nzk3NTIzN30.191LXjcSO4qSI0EtutDoaGt9snrVRWeal5o7tZEFmxE

{
  "product_ids": [324],
  "ratio": 11000
}

### 小商店会员获取店主信息
GET {{api}}/api/smallShop/shopkeeper/info?sid=5
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

### 小商店会员获取店主设置
GET {{api}}/api/smallShop/shopkeeper/setting?sid=5
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

### 获取专辑商品
GET {{api}}/api/smallShop/album/getProductListByAlbumID?album_id=8
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

###小商店申请售后
POST {{api}}/api/smallShop/alterSales/create
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

{
  "amount": 5,
  "description": "shuoming",
  "detail_images": [],
  "is_received": 0,
  "order_item_id": 4,
  "reason": "",
  "reason_type": 0,
  "refund_type": 1,
  "sid": 5
}

### 小商店获取售后列表
GET {{api}}/api/smallShop/alterSales/list
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

### 获取正在进行的售后数量
GET {{api}}/api/smallShop/getAfterSalesCount
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2NzYzNTYxNTMsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY3NTc1MDM1M30.7uz9ZfMnFWLjt0cd64lHBaaIcPngkfv-gQIwuNaLJmY
x-user-id: 1

###小商店会员地址列表
POST {{api}}/api/smallShop/user/address/list
Content-Type: application/json
x-token: {{su-token}}
x-user-id: 1

### 获取基础设置
GET {{api}}/smallShop/afterSales/findByOrderItemId?id=4
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZh5YSIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY3ODg0ODU4OCwiaXNzIjoicW1QbHVzIiwibmJmIjoxNjc4MjQyNzg4fQ.ROFpucfN1rQk9otKNR3Pe0UhhnTJCL-o8lVSfoxo8pc
x-User-Id: 1

### 获取基础设置
POST {{api}}/api/smallShop/afterSales/video/create
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2OTI2ODU3NTksImlzcyI6InFtUGx1cyIsIm5iZiI6MTY5MjA3OTk1OX0.IJYSPmssps4w08s6i_rI48n1tzjuuGlYevry2w0v08I
x-User-Id: 1

{

}

### 获取协议设置
GET {{api}}/smallShop/findProtocolSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZh5YSIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY5OTkyNzcyNywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjk5MzIxOTI3fQ.y0X1HbGofapBQGici7EjsAf-JJI1VSZmG8kFLTxhNsY
x-User-Id: 1

### 更新基协议设置
PUT {{api}}/smallShop/updateProtocolSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiYTcxZTc4MWUtZmYwZi00YTU2LThlOTQtMGQ5OTYxYjMyYzRlIiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZh5YSIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTY5OTkyNzcyNywiaXNzIjoicW1QbHVzIiwibmJmIjoxNjk5MzIxOTI3fQ.y0X1HbGofapBQGici7EjsAf-JJI1VSZmG8kFLTxhNsY
x-User-Id: 1

{
  "id": 39,
  "value": {
    "service_title": "服务协议标题",
    "service_content": "服务协议内容",
    "privacy_title": "隐私协议标题",
    "privacy_content": "隐私协议内容"
  }
}

### 获取基础设置
GET {{api}}/api/smallShop/setting/getMiniAppID
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2OTcyNDY0MzAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY5NjY0MDYzMH0.W8W_viXUs_yRcH9G8yDTpLFbBm-wTvwptWszFfDjpj4
x-User-Id: 1


### 获取分类
GET {{api}}/api/smallShopWechatMini/category/categoryList?sid=1&parent_id=69485&level=2
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMzUwNGJlMWMtYjU3MS00ZTE1LTg2YzAtYjY5NDc1YzQ4NWI1IiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE2OTcyNDY0MzAsImlzcyI6InFtUGx1cyIsIm5iZiI6MTY5NjY0MDYzMH0.W8W_viXUs_yRcH9G8yDTpLFbBm-wTvwptWszFfDjpj4
x-User-Id: 1

###
GET {{api}}/api/smallShop/setting/getSetting
Content-Type: application/json

###
POST {{api}}/api/smallShop/finance/lakalaPay
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiOWYyNTAzZTMtZmQxYS00ZGNjLTg2ODItODVhMmYyMzkwMTY2IiwiVXNlcm5hbWUiOiIiLCJJRCI6NCwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MDY0OTE0NTEsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcwNTg4NTY1MX0.HmvwCPEN_nFTOc8SmoHVl_DKONYW8udcuJ5A2q-bo8I
x-User-Id: 1

{
  "amount": 100,
  "pay_sn": "123456"
}

###
POST {{api}}/api/smallShop/finance/lakalaPayNotify
Content-Type: application/json


### 在线选品的商品列表使用原接口 api/product/list

### 前端:提交小商店申请
POST {{api}}/api/smallShopWechatMini/product/list
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMTBkYWNlYTMtMTY2YS00YWU4LWExOWUtNWE5YWFjZWU4YjgzIiwiVXNlcm5hbWUiOiIiLCJJRCI6MTkwLCJBcHBJRCI6MCwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTcxMDE0MTMyMSwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzA5NTM1NTIxfQ.FodFVLTbBbUJjCZZHXSASZI9WBGuL1mw6vQIOE80wi0
x-User-Id: 1

{
  "sid": 1,
  "title": "",
  "page": 1,
  "pageSize": 10
}


###
GET {{api}}/api/smallShop/album/getAlbumList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMDBjZDZjNzktZWNiZS00YmQyLWI1M2YtNjhhNzc0YWZiOTZkIiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MTk5OTE4ODUsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcxOTM4NjA4NX0.C-Eue36RcCkq7TrXHbvBKD4BdcAZcFqmBhqnC7XlFtM
x-User-Id: 1


### product/getProductList
GET {{api}}/api/smallShop/product/getProductList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMDBjZDZjNzktZWNiZS00YmQyLWI1M2YtNjhhNzc0YWZiOTZkIiwiVXNlcm5hbWUiOiIiLCJJRCI6MSwiQXBwSUQiOjAsIkJ1ZmZlclRpbWUiOjg2NDAwLCJleHAiOjE3MTk5OTE4ODUsImlzcyI6InFtUGx1cyIsIm5iZiI6MTcxOTM4NjA4NX0.C-Eue36RcCkq7TrXHbvBKD4BdcAZcFqmBhqnC7XlFtM
x-User-Id: 1


### 添 加选品
POST {{api}}/api/smallShop/product/addByProductID
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMTBkYWNlYTMtMTY2YS00YWU4LWExOWUtNWE5YWFjZWU4YjgzIiwiVXNlcm5hbWUiOiIiLCJJRCI6MTkwLCJBcHBJRCI6MCwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTcxMDQwMDIyMywiaXNzIjoicW1QbHVzIiwibmJmIjoxNzA5Nzk0NDIzfQ.8IVUFQ9AA91p7ROO80L8-Sd5m6SKbPNw-ZQ9CV7O19Q
x-User-Id: 1

{
  "product_ids": [46513],
  "price_type": 0,
  "price_proportion": 13000

}

### 添 加选品
POST {{api}}/api/smallShop/curriculum/getCourseOrderList
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiMTBkYWNlYTMtMTY2YS00YWU4LWExOWUtNWE5YWFjZWU4YjgzIiwiVXNlcm5hbWUiOiIiLCJJRCI6MTkwLCJBcHBJRCI6MCwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTcxMDQwMDIyMywiaXNzIjoicW1QbHVzIiwibmJmIjoxNzA5Nzk0NDIzfQ.8IVUFQ9AA91p7ROO80L8-Sd5m6SKbPNw-ZQ9CV7O19Q
x-User-Id: 1

{
  "page": 1,
  "pageSize": 10,
}



### 添 加选品
POST http://127.0.0.1:8888/smallShop/setConvergenceSetting
Content-Type: application/json
x-token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNzE3MDMzNjM0LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE3MTY0Mjc4MzR9.THe7fdyjp6VN9faImie_wtlTHX4-fn106vp-8UwjvpQ
x-User-Id: 1

{

}



### 保存汇聚设置
POST http://127.0.0.1:8888/smallShop/setConvergenceSetting
Content-Type: application/json
x-User-Id: 1
X-Token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNzE3MDMzNjM0LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE3MTY0Mjc4MzR9.THe7fdyjp6VN9faImie_wtlTHX4-fn106vp-8UwjvpQ

{
"merchantno":"333",
"trademerchantno":"444",
"hmacval":"555"

}


### 获取汇聚设置
GET http://127.0.0.1:8888/smallShop/getConvergenceSetting
Content-Type: application/json
x-User-Id: 1
X-Token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZmM1MGJjMjEtNzFjZC00Mzc1LThjMzQtYWNhZjNkZDU2ODA1IiwiSUQiOjEsIkFwcElEIjowLCJVc2VybmFtZSI6ImFkbWluIiwiTmlja05hbWUiOiLotoXnuqfnrqHnkIblkZgzIiwiQXV0aG9yaXR5SWQiOiI4ODgiLCJCdWZmZXJUaW1lIjo4NjQwMCwiZXhwIjoxNzE3MDMzNjM0LCJpc3MiOiJxbVBsdXMiLCJuYmYiOjE3MTY0Mjc4MzR9.THe7fdyjp6VN9faImie_wtlTHX4-fn106vp-8UwjvpQ

### 获取基础设置
POST {{api}}/api/finance/getWithdrawSetting
Content-Type: application/json
x-token: {{f-token}}
x-User-Id: 1

{}

### 抖音团购-分类列表
GET {{api}}/api/smallShop/douyinGroup/category/list
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

### 抖音团购-商品列表
POST {{api}}/api/smallShop/douyinGroup/product/list
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "sort_by": 3,
  "order_by": 1
}

### 抖音团购-商品详情
POST {{api}}/api/smallShop/douyinGroup/product/detail
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "product_id_list": [1808063700870185],
  "shop_id": 1
}

### 抖音团购-商品转链
POST {{api}}/api/smallShop/douyinGroup/product/link
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "command": "1808063700870185",
  "shop_id": 1,
  "need_share_command": false,
  "need_qr_code": true
}

### 抖音团购-文案-详情地址
POST {{api}}/api/smallShop/douyinGroup/product/shareLink
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "product_id": 1808063700870185,
  "shop_id": 1
}

### 抖音团购-获取分享海报
POST {{api}}/api/smallShop/douyinGroup/product/poster
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "product_id_list": [1808063700870185],
  "shop_id": 1
}

### 聚合-转链
POST {{api}}/api/smallShop/cps/generateLink
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "activity_id": "404",
  "shop_id": 1,
  "link_type": "meituan",
  "jump_type": 1
}

### 聚合-美团城市列表
GET {{api}}/api/smallShop/douyinGroup/city/list?city_name=
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

### 获取美团商品列表
POST {{api}}/api/smallShop/cps/meituan/getCouponList
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "sid": 1,
  "platform": 2,
  "bizLine": 1,
  "listTopiId": 3
}


### 获取美团商品详情
POST {{api}}/api/smallShop/cps/meituan/getCouponList
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

{
  "sid": 1,
  "platform": 2,
  "bizLine": 1,
  "listTopiId": 3,
  "vpSkuViewIds": ["OSPK63PQIHS55EPZ732JMGAFF4"]
}

### 聚合-获取美团分类
GET {{api}}/api/smallShop/cps/meituan/getCategoryList
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

### 聚合-获取美团城市
GET {{api}}/api/smallShop/cps/meituan/getCityList?city_name=上海
Content-Type: application/json
x-token: {{su-token}}
x-User-Id: 1

### 获取基础设置
GET {{api}}/smallShop/getMenuButton
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1


### 指定选品-列表
GET {{api}}/smallShop/product/getSelectedList
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

### 指定选品-增加选品的商品列表
GET {{api}}/smallShop/product/getProductListBySelectedAdd?page=1&pageSize=10
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

### 指定选品-增加选品
POST {{api}}/smallShop/product/addSelected
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

{
  "product_ids": [5250, 5249]
}

### 指定选品-移除选品
POST {{api}}/smallShop/product/removeSelected
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

{
  "product_ids": [5250]
}

### 指定选品-单个同步
POST {{api}}/smallShop/product/syncSelected
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

{
  "product_id": 5249
}

### 指定选品-批量同步
POST {{api}}/smallShop/product/syncSelectedBatch
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

{
  "product_id": 5249
}

### 指定选品-批量同步
GET {{api}}/api/wechat/login?url=www.baidu.com
Content-Type: application/json
x-User-Id: 1


### 指定选品-批量同步
POST {{api}}/api/smallShop/user/wechatH5Register
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1

{
  "wx_openid": "asdasd"
}


### 获取基础设置
POST {{api}}/smallShop/ceshi
Content-Type: application/json
x-token: {{b-token}}
x-User-Id: 1
