package listener

import (
	aggregatedPaymentSplitSettlementService "aggregated-payment-split-settlement/service"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	shopOrder "order/model"
	"order/mq"
	"small-shop/model"
	"small-shop/service"
	"yz-go/component/log"
	"yz-go/source"
)

func PushOrderCreatedHandles() {
	//log.Log().Error("小商店-订单创建监听!")
	mq.PushHandles("createSmallShopAward", func(orderMsg mq.OrderMessage) (err error) {
		//log.Log().Error("小商店-订单创建监听order_id:" + strconv.Itoa(int(orderMsg.OrderID)))
		if orderMsg.MessageType != mq.Paid {
			//log.Log().Error("不是订单支付事件,返回")
			return nil
		}
		// 订单
		var order shopOrder.Order
		err = source.DB().Model(&shopOrder.Order{}).Where("id = ?", orderMsg.OrderID).First(&order).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Error("查询订单报错,返回")
			log.Log().Error("查询订单报错", zap.Any("err", err))
			return nil
		}
		_, _ = Award(order, 0, 0)
		//} else {
		//	//log.Log().Error("中台订单没有第三方订单号,返回")
		//}
		return nil
	})
}

// types 0 正常  1 聚合分账
func Award(order shopOrder.Order, splitAmount uint, types int) (err error, award model.Award) {
	if order.ThirdOrderSN != "" {
		// 验证是否已经奖励过
		var exist model.Award
		err = source.DB().Where("shop_order_id = ?", order.ID).First(&exist).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("查询小商店奖励sql报错", zap.Any("err", err.Error()))
			err = errors.New("查询小商店奖励sql报错")

			return
		}
		if exist.ID != 0 {
			//log.Log().Error("已产生过奖励记录, 返回")
			err = errors.New("已产生过奖励记录")
			return
		}
		// 通过第三方订单号查询小商店订单
		var smallShopOrder model.SmallShopOrder
		err, smallShopOrder = service.GetSmallShopOrderByOrderSN(order.ThirdOrderSN)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Error("查询小商店订单报错,返回")
			log.Log().Error("查询小商店订单报错", zap.Any("err", err))
			err = errors.New("查询小商店订单报错")

			return
		}
		if smallShopOrder.ID == 0 || smallShopOrder.Shopkeeper == 1 {
			//log.Log().Error("未找到小商店订单,返回")
			err = errors.New("未找到小商店订单")
			return
		}
		//检验是否是聚合分账，因为获取分账金额有延迟 聚合分账定时任务来执行 这里仅进行创建分账记录
		if types == 0 {
			var smallShopPayInfo model.SmallShopPayInfo
			err = source.DB().Model(&model.SmallShopPayInfo{}).Where("id", smallShopOrder.SmallShopPayInfoID).First(&smallShopPayInfo).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				//log.Log().Error("查询小商店订单报错,返回")
				log.Log().Error("聚合分账：查询小商店订单报错，查询标记为聚合分账的记录失败", zap.Any("err", err))
				err = errors.New("聚合分账：查询小商店订单报错，查询标记为聚合分账的记录失败")
				return
			}
			if smallShopPayInfo.ID > 0 && smallShopPayInfo.IsSplitStatus == 1 {
				//如果是1需要创建分账记录 0待支付1已支付待创建记录，2已创建分账记录
				if smallShopPayInfo.SplitCreateStatus == 1 {
					err = aggregatedPaymentSplitSettlementService.CreateSplitShareRecord(smallShopPayInfo.ID, smallShopPayInfo.PaymentStoreCode, smallShopPayInfo.PaySN)
					if err != nil {
						log.Log().Error("聚合分账：保存分账记录失败", zap.Any("err", err))
						source.DB().Model(&model.SmallShopPayInfo{}).Where("id =?", smallShopPayInfo.ID).Updates(map[string]interface{}{"error_msg": err.Error()})
						return
					} else {
						source.DB().Model(&model.SmallShopPayInfo{}).Where("id =?", smallShopPayInfo.ID).Updates(map[string]interface{}{"split_create_status": 2})
					}
					return
				}
				log.Log().Error("聚合分账：存在聚合分账记录，不是聚合分账执行此方法直接返回", zap.Any("err", err))
				err = errors.New("聚合分账：存在聚合分账记录，不是聚合分账执行此方法直接返回")
				return
			}
		}
		var smallShop model.SmallShop
		err, smallShop = service.GetSmallShopByID(smallShopOrder.SmallShopID)
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Error("查询小商店报错,返回")
			log.Log().Error("查询小商店报错", zap.Any("err", err))
			err = errors.New("查询小商店报错")

			return
		}
		if smallShop.ID == 0 {
			err = errors.New("未找到小商店")
			//log.Log().Error("未找到小商店,返回")
			return
		}
		err, award = service.AwardCreate(order, smallShopOrder, smallShop, splitAmount, types)
	}
	return
}
