package service

import "yz-go/source"

type ProductElasticSearch struct {
	ProductElasticSearchCut
	Level1Price          int     `json:"level_1_price"`
	Level1MinPrice       int     `json:"level_1_min_price"`
	Level1MaxPrice       int     `json:"level_1_max_price"`
	Level2Price          int     `json:"level_2_price"`
	Level2MinPrice       int     `json:"level_2_min_price"`
	Level2MaxPrice       int     `json:"level_2_max_price"`
	Level3Price          int     `json:"level_3_price"`
	Level3MinPrice       int     `json:"level_3_min_price"`
	Level3MaxPrice       int     `json:"level_3_max_price"`
	Level4Price          int     `json:"level_4_price"`
	Level4MinPrice       int     `json:"level_4_min_price"`
	Level4MaxPrice       int     `json:"level_4_max_price"`
	Level5Price          int     `json:"level_5_price"`
	Level5MinPrice       int     `json:"level_5_min_price"`
	Level5MaxPrice       int     `json:"level_5_max_price"`
	Level6Price          int     `json:"level_6_price"`
	Level6MinPrice       int     `json:"level_6_min_price"`
	Level6MaxPrice       int     `json:"level_6_max_price"`
	Level7Price          int     `json:"level_7_price"`
	Level7MinPrice       int     `json:"level_7_min_price"`
	Level7MaxPrice       int     `json:"level_7_max_price"`
	Level8Price          int     `json:"level_8_price"`
	Level8MinPrice       int     `json:"level_8_min_price"`
	Level8MaxPrice       int     `json:"level_8_max_price"`
	Level9Price          int     `json:"level_9_price"`
	Level9MinPrice       int     `json:"level_9_min_price"`
	Level9MaxPrice       int     `json:"level_9_max_price"`
	Level10Price         int     `json:"level_10_price"`
	Level10MinPrice      int     `json:"level_10_min_price"`
	Level10MaxPrice      int     `json:"level_10_max_price"`
	Level1Profit         int     `json:"level_1_profit"`
	Level1MinProfit      int     `json:"level_1_min_profit"`
	Level1MaxProfit      int     `json:"level_1_max_profit"`
	Level2Profit         int     `json:"level_2_profit"`
	Level2MinProfit      int     `json:"level_2_min_profit"`
	Level2MaxProfit      int     `json:"level_2_max_profit"`
	Level3Profit         int     `json:"level_3_profit"`
	Level3MinProfit      int     `json:"level_3_min_profit"`
	Level3MaxProfit      int     `json:"level_3_max_profit"`
	Level4Profit         int     `json:"level_4_profit"`
	Level4MinProfit      int     `json:"level_4_min_profit"`
	Level4MaxProfit      int     `json:"level_4_max_profit"`
	Level5Profit         int     `json:"level_5_profit"`
	Level5MinProfit      int     `json:"level_5_min_profit"`
	Level5MaxProfit      int     `json:"level_5_max_profit"`
	Level6Profit         int     `json:"level_6_profit"`
	Level6MinProfit      int     `json:"level_6_min_profit"`
	Level6MaxProfit      int     `json:"level_6_max_profit"`
	Level7Profit         int     `json:"level_7_profit"`
	Level7MinProfit      int     `json:"level_7_min_profit"`
	Level7MaxProfit      int     `json:"level_7_max_profit"`
	Level8Profit         int     `json:"level_8_profit"`
	Level8MinProfit      int     `json:"level_8_min_profit"`
	Level8MaxProfit      int     `json:"level_8_max_profit"`
	Level9Profit         int     `json:"level_9_profit"`
	Level9MinProfit      int     `json:"level_9_min_profit"`
	Level9MaxProfit      int     `json:"level_9_max_profit"`
	Level10Profit        int     `json:"level_10_profit"`
	Level10MinProfit     int     `json:"level_10_min_profit"`
	Level10MaxProfit     int     `json:"level_10_max_profit"`
	Level1ProfitRate     float64 `json:"level_1_profit_rate"` //利润率
	Level1MinProfitRate  float64 `json:"level_1_min_profit_rate"`
	Level1MaxProfitRate  float64 `json:"level_1_max_profit_rate"`
	Level2ProfitRate     float64 `json:"level_2_profit_rate"`
	Level2MinProfitRate  float64 `json:"level_2_min_profit_rate"`
	Level2MaxProfitRate  float64 `json:"level_2_max_profit_rate"`
	Level3ProfitRate     float64 `json:"level_3_profit_rate"`
	Level3MinProfitRate  float64 `json:"level_3_min_profit_rate"`
	Level3MaxProfitRate  float64 `json:"level_3_max_profit_rate"`
	Level4ProfitRate     float64 `json:"level_4_profit_rate"`
	Level4MinProfitRate  float64 `json:"level_4_min_profit_rate"`
	Level4MaxProfitRate  float64 `json:"level_4_max_profit_rate"`
	Level5ProfitRate     float64 `json:"level_5_profit_rate"`
	Level5MinProfitRate  float64 `json:"level_5_min_profit_rate"`
	Level5MaxProfitRate  float64 `json:"level_5_max_profit_rate"`
	Level6ProfitRate     float64 `json:"level_6_profit_rate"`
	Level6MinProfitRate  float64 `json:"level_6_min_profit_rate"`
	Level6MaxProfitRate  float64 `json:"level_6_max_profit_rate"`
	Level7ProfitRate     float64 `json:"level_7_profit_rate"`
	Level7MinProfitRate  float64 `json:"level_7_min_profit_rate"`
	Level7MaxProfitRate  float64 `json:"level_7_max_profit_rate"`
	Level8ProfitRate     float64 `json:"level_8_profit_rate"`
	Level8MinProfitRate  float64 `json:"level_8_min_profit_rate"`
	Level8MaxProfitRate  float64 `json:"level_8_max_profit_rate"`
	Level9ProfitRate     float64 `json:"level_9_profit_rate"`
	Level9MinProfitRate  float64 `json:"level_9_min_profit_rate"`
	Level9MaxProfitRate  float64 `json:"level_9_max_profit_rate"`
	Level10ProfitRate    float64 `json:"level_10_profit_rate"`
	Level10MinProfitRate float64 `json:"level_10_min_profit_rate"`
	Level10MaxProfitRate float64 `json:"level_10_max_profit_rate"`
	Level1GrossRate      float64 `json:"level_1_gross_rate"` //毛利率
	Level1MinGrossRate   float64 `json:"level_1_min_gross_rate"`
	Level1MaxGrossRate   float64 `json:"level_1_max_gross_rate"`
	Level2GrossRate      float64 `json:"level_2_gross_rate"`
	Level2MinGrossRate   float64 `json:"level_2_min_gross_rate"`
	Level2MaxGrossRate   float64 `json:"level_2_max_gross_rate"`
	Level3GrossRate      float64 `json:"level_3_gross_rate"`
	Level3MinGrossRate   float64 `json:"level_3_min_gross_rate"`
	Level3MaxGrossRate   float64 `json:"level_3_max_gross_rate"`
	Level4GrossRate      float64 `json:"level_4_gross_rate"`
	Level4MinGrossRate   float64 `json:"level_4_min_gross_rate"`
	Level4MaxGrossRate   float64 `json:"level_4_max_gross_rate"`
	Level5GrossRate      float64 `json:"level_5_gross_rate"`
	Level5MinGrossRate   float64 `json:"level_5_min_gross_rate"`
	Level5MaxGrossRate   float64 `json:"level_5_max_gross_rate"`
	Level6GrossRate      float64 `json:"level_6_gross_rate"`
	Level6MinGrossRate   float64 `json:"level_6_min_gross_rate"`
	Level6MaxGrossRate   float64 `json:"level_6_max_gross_rate"`
	Level7GrossRate      float64 `json:"level_7_gross_rate"`
	Level7MinGrossRate   float64 `json:"level_7_min_gross_rate"`
	Level7MaxGrossRate   float64 `json:"level_7_max_gross_rate"`
	Level8GrossRate      float64 `json:"level_8_gross_rate"`
	Level8MinGrossRate   float64 `json:"level_8_min_gross_rate"`
	Level8MaxGrossRate   float64 `json:"level_8_max_gross_rate"`
	Level9GrossRate      float64 `json:"level_9_gross_rate"`
	Level9MinGrossRate   float64 `json:"level_9_min_gross_rate"`
	Level9MaxGrossRate   float64 `json:"level_9_max_gross_rate"`
	Level10GrossRate     float64 `json:"level_10_gross_rate"`
	Level10MinGrossRate  float64 `json:"level_10_min_gross_rate"`
	Level10MaxGrossRate  float64 `json:"level_10_max_gross_rate"`
	ProductLevelDiscount
}

// ProductLevelDiscount 商品等级折扣
type ProductLevelDiscount struct {
	Level1MinDiscount  uint `json:"level_1_min_discount"`
	Level1MaxDiscount  uint `json:"level_1_max_discount"`
	Level2MinDiscount  uint `json:"level_2_min_discount"`
	Level2MaxDiscount  uint `json:"level_2_max_discount"`
	Level3MinDiscount  uint `json:"level_3_min_discount"`
	Level3MaxDiscount  uint `json:"level_3_max_discount"`
	Level4MinDiscount  uint `json:"level_4_min_discount"`
	Level4MaxDiscount  uint `json:"level_4_max_discount"`
	Level5MinDiscount  uint `json:"level_5_min_discount"`
	Level5MaxDiscount  uint `json:"level_5_max_discount"`
	Level6MinDiscount  uint `json:"level_6_min_discount"`
	Level6MaxDiscount  uint `json:"level_6_max_discount"`
	Level7MinDiscount  uint `json:"level_7_min_discount"`
	Level7MaxDiscount  uint `json:"level_7_max_discount"`
	Level8MinDiscount  uint `json:"level_8_min_discount"`
	Level8MaxDiscount  uint `json:"level_8_max_discount"`
	Level9MinDiscount  uint `json:"level_9_min_discount"`
	Level9MaxDiscount  uint `json:"level_9_max_discount"`
	Level10MinDiscount uint `json:"level_10_min_discount"`
	Level10MaxDiscount uint `json:"level_10_max_discount"`
}

type ProductElasticSearchCut struct {
	ID          uint   `json:"id"`
	Title       string `json:"title"`
	SearchTitle string `json:"search_title"`
	ImageUrl    string `json:"image_url"`
	Source      int    `json:"source"`
	BrandID     uint   `json:"brand_id"`
	Category1ID uint   `json:"category_1_id"`
	Category2ID uint   `json:"category_2_id"`
	Category3ID uint   `json:"category_3_id"`
	//协议价
	AgreementPrice uint `json:"agreement_price"`
	//协议价
	NormalAgreementPrice uint `json:"normal_agreement_price"`
	//指导价
	GuidePrice uint `json:"guide_price"`
	//营销价
	ActivityPrice uint `json:"activity_price"`
	//市场价 对应product.OriginPrice
	MarketPrice uint `json:"market_price"`
	//销售价
	SalePrice uint `json:"sale_price"`
	//成本价
	CostPrice uint `json:"cost_price"`
	//常规利润率(guide_price - price)/price*100%
	PromotionRate float64 `json:"origin_rate"`
	//常规利润率(guide_price - price)/price*100%
	MinPromotionRate float64 `json:"min_origin_rate"`
	//常规利润率(guide_price - price)/price*100%
	MaxPromotionRate float64 `json:"max_origin_rate"`
	//常规利润率(用原始协议价计算,下同)
	NormalPromotionRate float64 `json:"normal_origin_rate"`
	//营销利润率
	ActivityRate float64 `json:"activity_rate"`
	//营销利润率
	NormalActivityRate float64 `json:"normal_activity_rate"`
	//市场利润率
	MarketRate float64 `json:"market_rate"`
	//市场利润率
	NormalMarketRate float64 `json:"normal_market_rate"`
	//利润
	Profit int `json:"profit"`
	//利润率(price - cost_price) / cost_price * 100%
	PriceRate float64 `json:"price_rate"`
	//利润率
	ProfitRate   float64 `json:"profit_rate"`
	DiscountRate float64 `json:"discount_rate"`
	//利润率
	NormalProfitRate float64 `json:"normal_profit_rate"`
	//毛利润率
	GrossProfitRate float64 `json:"gross_profit_rate"`
	//毛利润率
	NormalGrossProfitRate float64           `json:"normal_gross_profit_rate"`
	IsDisplay             int               `json:"is_display"`
	IsRecommend           int               `json:"is_recommend"`
	IsNew                 int               `json:"is_new"`
	IsHot                 int               `json:"is_hot"`
	Stock                 int               `json:"stock"`
	Sales                 int               `json:"sales"`
	Unit                  string            `json:"unit"`
	IsPromotion           int               `json:"is_promotion"`
	MinPrice              int               `json:"min_price"`
	MaxPrice              int               `json:"max_price"`
	CreatedAt             *source.LocalTime `json:"created_at"`
	SupplierID            uint              `json:"supplier_id"`
	GatherSupplyID        uint              `json:"gather_supplier_id"`
	//供应链名称 对外
	GatherSupplyName string `json:"gather_supplier_name"`

	FreightType          int    `json:"freight_type"`
	RecommendBrandStr    string `json:"recommend_brand_str"`
	RecommendCategoryStr string `json:"recommend_category_str"`
	SupplierName         string `json:"supplier_name"`
	SupplierShopName     string `json:"supplier_shop_name"`

	UpdatedAt  *source.LocalTime `json:"updated_at"`
	Sort       int               `json:"sort"`
	IsPlugin   int               `json:"is_plugin"`
	Barcode    string            `json:"barcode"`
	ImportApps string            `json:"import_apps"`
	IsBill     int               `json:"is_bill"`
	// 1 含税 2 不含税
	IsTaxLogo int `json:"is_tax_logo"`
	// 商品税率
	TaxRate int `json:"tax_rate"`
	// 规格税率
	SkuTaxRates []int `json:"sku_tax_rates"`
	// 共享专辑ids
	AlbumIds []uint `json:"album_ids"`
	// 商品专辑ids
	CollectIds        []uint `json:"collect_ids"`
	SingleOption      int    `json:"single_option"`
	SmallShopIdString string `json:"small_shop_id_string"`
	SupplyLine        string `json:"supply_line"`
	IsVideoShop       int    `json:"is_video_shop"`
	IsImport          int    `json:"is_import"`
	MinProfit         int    `json:"min_profit"`
	SourceName        string `json:"source_name"`
	LocationId        uint   `json:"location_id"`
	// 额外添加
	// 店铺名称
	ShopName string `json:"shop_name"`
	// 是否有详情图 0没有 1有
	DetailImages int `json:"detail_images"`
	// 是否有画廊图
	GalleryImages int `json:"gallery_images"`
	// 聚水潭绑定 0未绑定 1已绑定
	JushuitanBind int `json:"jushuitan_bind"`
	// 商品状态锁定 0未锁定 1已锁定
	StatusLock int    `json:"status_lock"`
	Price      uint   `json:"price"`
	Sn         string `json:"sn"`
	// 是否开启用户等级价格 0不开启 1开启
	UserPriceSwitch int `json:"user_price_switch"`
}
