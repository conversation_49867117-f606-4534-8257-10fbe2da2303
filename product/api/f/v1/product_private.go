package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"product/request"
	"product/service"
	ufv1 "user/api/f/v1"
	"user/level"
	"user/model"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// GetProductCardList
// @Tags 产品
// @Summary 分页获取Product card列表 登录使用
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表 登录使用"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/getProductCardListLogin [post]
func GetProductCardListLogin(c *gin.Context) {
	var err error
	var search request.ProductCardListSearch
	err = c.ShouldBindQuery(&search)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	var user service.User
	if userID > 0 {
		err = source.DB().Preload("UserLevel").First(&user, userID).Error
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if user.UserLevel.Discount > 0 {
			if search.MinPrice > 0 {
				search.MinPrice = search.MinPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
			if search.MaxPrice > 0 {
				search.MaxPrice = search.MaxPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
		}
		search.UserLevelID = user.LevelID
	}

	err, list, total := service.GetProductCardList(search, level.GetLevelSort(user.UserLevel.Level))
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
	return
}

// GetProductCardList
// @Tags 产品
// @Summary 分页获取Product card列表 登录使用
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表 登录使用"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/getProductCardListLogin [post]
func GetProductCardListLoginPost(c *gin.Context) {
	var err error
	var search request.ProductCardListSearch
	err = c.ShouldBindJSON(&search)

	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	if userID > 0 {
		var user service.User
		err = source.DB().Preload("UserLevel").First(&user, userID).Error
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if user.UserLevel.Discount > 0 {
			if search.MinPrice > 0 {
				search.MinPrice = search.MinPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
			if search.MaxPrice > 0 {
				search.MaxPrice = search.MaxPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
		}
		search.UserLevelID = user.LevelID
	}

	err, list, total := service.GetProductCardList(search, service.GetLevel(userID))
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
	return
}

// GetSupplierProductCardList
// @Tags 产品
// @Summary 获取店铺产品列表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/supplier/list [post]
func GetSupplierProductCardListLogin(c *gin.Context) {
	var err error
	var search request.ProductCardListSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	if userID > 0 {
		var user service.User
		err = source.DB().Preload("UserLevel").First(&user, userID).Error
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if user.UserLevel.Discount > 0 {
			if search.MinPrice > 0 {
				search.MinPrice = search.MinPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
			if search.MaxPrice > 0 {
				search.MaxPrice = search.MaxPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
		}
		search.UserLevelID = user.LevelID
	}
	err, list, total := service.GetProductCardList(search, service.GetLevel(userID))
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
	return
}

// GetRelationProductCardList
// @Tags 产品
// @Summary 获取相关产品列表
// @accept application/json
// @Produce application/json
// @Param data body RelationProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/relation/list [post]
func GetRelationProductCardListLogin(c *gin.Context) {
	var err error
	var search RelationProductCardListSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, product := service.GetProduct(search.ProductID)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	userID := ufv1.GetUserID(c)
	if userID > 0 {
		var user service.User
		err = source.DB().Preload("UserLevel").First(&user, userID).Error
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		if user.UserLevel.Discount > 0 {
			if search.MinPrice > 0 {
				search.MinPrice = search.MinPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
			if search.MaxPrice > 0 {
				search.MaxPrice = search.MaxPrice / (user.UserLevel.Discount / 10000)
				search.SearchByLevelPrice = 1
			}
		}
		search.UserLevelID = user.LevelID
	}
	search.Category3ID = product.Category3ID
	err, list, total := service.GetProductCardList(search.ProductCardListSearch, service.GetLevel(userID))
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
	return
}

func GetResource(c *gin.Context) {
	var reqId yzRequest.GetById
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	if err, link := service.Down(reqId.Id); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(gin.H{"link": link}, c)
	}
}

// GetProductCardListByCollection
// @Tags 产品
// @Summary 分页获取Product card列表 登录使用
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/list [post]
func GetProductCardListByCollectionLogin(c *gin.Context) {
	var err error
	var search request.CollectionProductCardList
	err = c.ShouldBindJSON(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	var user model.User
	err = source.DB().Where("id = ?", userID).First(&user).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	search.UserLevelID = user.LevelID
	err, list, total := service.GetProductCardListByCollection(search, userID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
		//NextUrl:  nextUrl,
	}, "获取成功", c)
	return
}

func GetProductCardListByCollectionLoginNew(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	var user service.User
	err = source.DB().Preload("UserLevel").Where("id = ?", userID).First(&user).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return

	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").Where("member_id = ?", userID).First(&app).Error
	if err == nil {
		pageInfo.AppID = app.ID
	}

	if err, list, total := service.GetProductStorageCenterList(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level, 0); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio:    app.ApplicationLevel.ServerRadio,
			MyCenterTotal: service.GetMyCenterTotal(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level),
		}, "获取成功", c)
	}
}
