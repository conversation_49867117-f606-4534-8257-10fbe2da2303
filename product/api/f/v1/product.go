package v1

import (
	ufv1 "gin-vue-admin/admin/api/v1"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	productModel "product/model"
	"product/request"
	"product/response"
	"product/service"
	"product/stock"
	v1 "user/api/f/v1"
	"user/model"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
	"yz-go/source"
)

// FindProduct
// @Tags 产品
// @Summary 用id查询Product
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询Product"
// @Success 200 {object} response.FindProduct
// @Router /api/product/get [post]
func FindProduct(c *gin.Context) {
	var reqId yzRequest.GetByIdAndUid
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	err, reProduct := service.GetProductSalesInfo(reqId)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		if err.Error() == "商品不存在" {
			yzResponse.FailWithMessage("商品不存在", c)
		} else {
			yzResponse.FailWithMessage("查询失败", c)
		}
		return
	}
	// 扣除预占库存
	for i, sku := range reProduct.Skus {
		err, sku.LockStock = stock.GetSkuLockStock(sku.ID)
		if err != nil {
			return
		}
		sku.Stock -= sku.LockStock
		reProduct.Skus[i] = sku
	}
	//err, level, desLevel, shopLevel, expressLevel := service.GetProductCommentLevel(reqId.Id)
	yzResponse.OkWithData(response.FindProduct{Product: reProduct, DesLevel: reProduct.DesLevel, ShopLevel: reProduct.ShopLevel, ExpressLevel: reProduct.ExpressLevel, Level: reProduct.Level}, c)
}

// FindProduct
// @Tags 产品
// @Summary 用id查询Product
// @accept application/json
// @Produce application/json
// @Param data body yzRequest.GetById true "用id查询Product"
// @Success 200 {object} response.FindProduct
// @Router /api/product/get [post]
func FindProductByLogin(c *gin.Context) {
	var reqId yzRequest.GetByIdAndUid
	err := c.ShouldBindQuery(&reqId)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	userID := v1.GetUserID(c)
	reqId.UserID = userID
	err, reProduct := service.GetProductSalesInfo(reqId)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		if err.Error() == "商品不存在" {
			yzResponse.FailWithMessage("商品不存在", c)
		} else {
			yzResponse.FailWithMessage("查询失败", c)
		}
		return
	}
	// 规格数不大于1 使用表中数据
	if len(reProduct.Skus) == 1 {
		reProduct.Price = reProduct.Skus[0].Price
		reProduct.OriginPrice = reProduct.Skus[0].OriginPrice

	}
	// 扣除预占库存
	for i, sku := range reProduct.Skus {
		err, sku.LockStock = stock.GetSkuLockStock(sku.ID)
		if err != nil {
			return
		}
		sku.Stock -= sku.LockStock
		reProduct.Skus[i] = sku
	}
	var app service.Application
	var isStorage = 0 //1已加入选品库 0未加入
	appErr := source.DB().Where("member_id = ?", userID).First(&app).Error
	if appErr == nil {
		var goodsStorage productModel.Storage
		source.DB().Model(&productModel.Storage{}).Where("product_id = ?", reProduct.ID).Where("app_id = ?", app.ID).First(&goodsStorage)
		if goodsStorage.ID > 0 {
			isStorage = 1
		}
	}
	//err, level, desLevel, shopLevel, expressLevel := service.GetProductCommentLevel(reqId.Id)
	yzResponse.OkWithData(response.FindProduct{Product: reProduct, DesLevel: reProduct.DesLevel, ShopLevel: reProduct.ShopLevel, ExpressLevel: reProduct.ExpressLevel, Level: reProduct.Level, IsStorage: isStorage}, c)
}

// GetProductCardList
// @Tags 产品
// @Summary 分页获取Product card列表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/list [post]
func GetProductCardList(c *gin.Context) {
	var err error
	var search request.ProductCardListSearch
	err = c.ShouldBindJSON(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, list, total := service.GetProductCardList(search, 0)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
		//NextUrl:  nextUrl,
	}, "获取成功", c)
	return
}

// GetProductCardList
// @Tags 产品
// @Summary 分页获取Product card列表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/list [post]
func GetProductCardListGet(c *gin.Context) {
	var err error
	var search request.ProductCardListSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, list, total := service.GetProductCardList(search, 0)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
		//NextUrl:  nextUrl,
	}, "获取成功", c)
	return
}

type RelationProductCardListSearch struct {
	request.ProductCardListSearch
	ProductID uint `json:"product_id" form:"product_id"` // 产品id
}

// GetRelationProductCardList
// @Tags 产品
// @Summary 获取相关产品列表
// @accept application/json
// @Produce application/json
// @Param data body RelationProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/relation/list [post]
func GetRelationProductCardList(c *gin.Context) {
	var err error
	var search RelationProductCardListSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, product := service.GetProduct(search.ProductID)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("查询失败", c)
		return
	}
	search.Category3ID = product.Category3ID
	err, list, total := service.GetProductCardList(search.ProductCardListSearch, 0)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
		//NextUrl:  nextUrl,
	}, "获取成功", c)
	return
}

type PersonalRelationProductCardListSearch struct {
	request.ProductCardListSearch
}

// GetPersonalRelationProductCardList
// @Tags 产品
// @Summary 获取个人推荐产品列表
// @accept application/json
// @Produce application/json
// @Param data body RelationProductCardListSearch true "个人推荐"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/relation/list [post]
func GetPersonalRelationProductCardList(c *gin.Context) {
	var err error
	var search PersonalRelationProductCardListSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("查询失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	err, list, total := service.GetProductCardList(search.ProductCardListSearch, 1)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
	}, "获取成功", c)
	return
}

// GetSupplierProductCardList
// @Tags 产品
// @Summary 获取店铺产品列表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/supplier/list [post]
func GetSupplierProductCardList(c *gin.Context) {
	var err error
	var search request.SupplierProductCardListSearch
	err = c.ShouldBindQuery(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	err, list, total := service.GetSupplierProductCardList(search, 0)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
		//NextUrl:  nextUrl,
	}, "获取成功", c)
	return
}

// GetProductCardListByCollection
// @Tags 产品
// @Summary 分页获取Product card列表
// @accept application/json
// @Produce application/json
// @Param data body request.ProductCardListSearch true "分页获取Product cart列表"
// @Success 200 {object} []yzResponse.Product
// @Router /api/product/list [post]
func GetProductCardListByCollection(c *gin.Context) {
	var err error
	var search request.CollectionProductCardList
	err = c.ShouldBindJSON(&search)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := ufv1.GetUserID(c)
	if userID > 0 {
		var user model.User
		err = source.DB().Where("id = ?", userID).First(&user).Error
		if err != nil {
			log.Log().Error("获取失败", zap.Any("err", err))
			yzResponse.FailWithMessage(err.Error(), c)
			return
		}
		search.UserLevelID = user.LevelID
	}
	err, list, total := service.GetProductCardListByCollection(search, 0)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	//search.Page += 1
	//err, nextUrl := utils.Url(c.FullPath(), search)
	//if err != nil {
	//	log.Log().Error("获取失败", zap.Any("err", err))
	//	yzResponse.FailWithMessage(err.Error(), c)
	//	return
	//}

	yzResponse.OkWithDetailed(yzResponse.PageResult{
		List:     list,
		Total:    total,
		Page:     search.Page,
		PageSize: search.PageSize,
		//NextUrl:  nextUrl,
	}, "获取成功", c)
	return
}

func GetProductCardListByCollectionNew(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetProductStorageCenterList(pageInfo, 0, 0, 0, 0, 0); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio:    0,
			MyCenterTotal: service.GetMyCenterTotal(pageInfo, 0, 0, 0, 0),
		}, "获取成功", c)
	}
}

func GetProductCardListNew(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}

	if err, list, total := service.GetProductStorageCenterList(pageInfo, 0, 0, 0, 0, 0); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio:    0,
			MyCenterTotal: service.GetMyCenterTotal(pageInfo, 0, 0, 0, 0),
		}, "获取成功", c)
	}
}

type AppProductPageResult struct {
	yzResponse.PageResult
	SeverRatio    int   `json:"sever_ratio"`
	MyCenterTotal int64 `json:"my_center_total"`
}

type Application struct {
	source.Model
	SupplierID       uint             `json:"supplier_id"`
	AppLevelID       uint             `json:"app_level_id"`
	PetSupplierID    uint             `json:"pet_supplier_id"`
	ApplicationLevel ApplicationLevel `json:"applicationLevel" gorm:"foreignKey:AppLevelID"`
}
type ApplicationLevel struct {
	source.Model
	ServerRadio int `json:"serverRadio" form:"serverRadio" gorm:"column:server_radio;comment:手续费比例(万分之一);type:int;size:10;"` // 手续费比例(万分之一)
}

func (ApplicationLevel) TableName() string {
	return "application_level"
}
func (Application) TableName() string {
	return "application"
}
