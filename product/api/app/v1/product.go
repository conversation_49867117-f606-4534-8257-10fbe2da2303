package v1

import (
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"product/request"
	"product/service"
	v1 "user/api/f/v1"
	"yz-go/cache"
	"yz-go/component/log"
	"yz-go/model"
	yzResponse "yz-go/response"
	"yz-go/source"
	"yz-go/utils"
)

type AppProductPageResult struct {
	yzResponse.PageResult
	SeverRatio    int   `json:"sever_ratio"`
	MyCenterTotal int64 `json:"my_center_total"`
}
type AppProductCursorResult struct {
	List     interface{} `json:"list"`
	Total    int64       `json:"total"`
	Cursor   int         `json:"cursor"`
	PageSize int         `json:"pageSize"`
}

func GetProductList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	userID := utils.GetAppUserID(c)
	var user service.User
	err = source.DB().Where("id = ?", userID).First(&user).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.AppID = appID
	if err, list, total := service.GetProductStorageInfoList(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.Level); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio: app.ApplicationLevel.ServerRadio,
		}, "获取成功", c)
	}
}

func GetGuangdianProductList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	userID := utils.GetAppUserID(c)
	var user service.User
	err = source.DB().Where("id = ?", userID).First(&user).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.AppID = appID
	if err, list, total := service.GetGuangdianProductStorageInfoList(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.Level); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio: app.ApplicationLevel.ServerRadio,
		}, "获取成功", c)
	}
}

func GetCakeProductList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	userID := utils.GetAppUserID(c)
	var user service.User
	err = source.DB().Where("id = ?", userID).First(&user).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.AppID = appID
	if err, list, total := service.GetCakeProductStorageInfoList(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.Level); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio: app.ApplicationLevel.ServerRadio,
		}, "获取成功", c)
	}
}

func ListLoginNew(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	var user *model.User
	user, err = cache.GetUserFromCache(userID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").Where("member_id = ?", userID).First(&app).Error
	if err == nil {
		pageInfo.AppID = app.ID
	}

	if err, list, total := service.GetProductStorageCenterList(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level, 0); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio:    app.ApplicationLevel.ServerRadio,
			MyCenterTotal: service.GetMyCenterTotal(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level),
		}, "获取成功", c)
	}
}

func GetStorageCenterList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	var user *model.User
	user, err = cache.GetUserFromCache(userID)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").Where("member_id = ?", userID).First(&app).Error
	if err == nil {
		pageInfo.AppID = app.ID
	}

	if err, list, total := service.GetProductStorageCenterList(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level, 1); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio:    app.ApplicationLevel.ServerRadio,
			MyCenterTotal: service.GetMyCenterTotal(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.UserLevel.Level),
		}, "获取成功", c)
	}
}

func GetMyStorageCenterList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	userID := v1.GetUserID(c)
	var user service.User
	err = source.DB().Where("id = ?", userID).First(&user).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	var app Application
	err = source.DB().Preload("ApplicationLevel").Where("member_id = ?", userID).First(&app).Error
	if err == nil {
		pageInfo.AppID = app.ID
	} else {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage("如需使用选品库，请先申请API采购权限", c)
		return
	}
	//if pageInfo.IsMyStorageList == 1 {
	//	var productIds []uint
	//	err = source.DB().Model(&model.StorageCenter{}).Where("user_id = ?", userID).Pluck("product_id", &productIds).Error
	//	if err != nil {
	//		log.Log().Error("获取失败", zap.Any("err", err))
	//		yzResponse.FailWithMessage(err.Error(), c)
	//	}
	//	pageInfo.GoodsIds = productIds
	//}

	if err, list, total := service.GetMyProductStorageCenterList(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.Level); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductPageResult{
			PageResult: yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			},
			SeverRatio:    app.ApplicationLevel.ServerRadio,
			MyCenterTotal: service.GetMyCenterTotal(pageInfo, app.AppLevelID, app.PetSupplierID, userID, user.Level),
		}, "获取成功", c)
	}
}

func GetMyStorageIdsList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	if err, list, total := service.GetMyProductStorageIdsList(pageInfo, appID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(
			yzResponse.PageResult{
				List:     list,
				Total:    total,
				Page:     pageInfo.Page,
				PageSize: pageInfo.PageSize,
			}, "获取成功", c)
	}
}

func GetProductCursorList(c *gin.Context) {
	var pageInfo request.ProductStorageSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	userID := utils.GetAppUserID(c)
	var app Application
	err = source.DB().Preload("ApplicationLevel").First(&app, appID).Error
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	pageInfo.AppID = appID
	if err, list, total, cursor := service.GetProductByCursor(pageInfo.Cursor, pageInfo.PageSize, userID, app.AppLevelID, appID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(AppProductCursorResult{
			List:     list,
			Total:    total,
			PageSize: pageInfo.PageSize,
			Cursor:   cursor,
		}, "获取成功", c)
	}
}

func GetProductDetailList(c *gin.Context) {
	var pageInfo request.ProductDetailSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)
	userID := utils.GetAppUserID(c)
	if err, list := service.GetProductDetailList(pageInfo.Ids, appID, pageInfo.SupplyLineId, userID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}

// @Tags Product
// @Summary 选品库增加商品
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ProductDetailSearch true "批量获取产品详情"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /product/getProductList [get]
func AddProductStorage(c *gin.Context) {
	var pageInfo request.ProductDetailSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)

	if err = service.PutProductStorage(pageInfo.Ids, appID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("添加成功", c)
	}
}

func DeleteProductStorage(c *gin.Context) {
	var pageInfo request.ProductDetailSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	appID := utils.GetAppID(c)

	if err = service.DeleteProductStorage(pageInfo.Ids, appID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("删除成功", c)
	}
}
func GetProductStock(c *gin.Context) {
	var pageInfo request.ProductDetailSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetProductStock(pageInfo.Ids); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List: list,
		}, "获取成功", c)
	}
}
