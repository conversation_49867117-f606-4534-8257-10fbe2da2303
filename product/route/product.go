package route

import (
	"github.com/gin-gonic/gin"
	av1 "product/api/app/v1"
	fv1 "product/api/f/v1"
	"product/api/v1"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("product")
	{
		ProductRouter.POST("createProduct", v1.CreateProduct)                           // 新建Product
		ProductRouter.DELETE("deleteProduct", v1.DeleteProduct)                         // 删除Product
		ProductRouter.DELETE("deleteProductByIds", v1.DeleteProductByIds)               // 批量删除Product
		ProductRouter.DELETE("deleteProductInfoList", v1.DeleteProductList)             // 全部
		ProductRouter.DELETE("getDeleteProductCount", v1.GetDeleteProductCount)         // 获取删除数量
		ProductRouter.PUT("updateProduct", v1.UpdateProduct)                            // 更新Product
		ProductRouter.GET("findProduct", v1.FindProduct)                                // 根据ID获取Product
		ProductRouter.GET("getProductList", v1.GetProductList)                          // 获取Product列表
		ProductRouter.GET("getGuangdianProductList", v1.GetGuangdianProductList)        // 获取Product列表
		ProductRouter.GET("getAliProductList", v1.GetAliProductList)                    // 获取阿里Product列表
		ProductRouter.GET("exportProductList", v1.ExportProductList)                    // 导出商品列表
		ProductRouter.GET("exportProductRecordList", v1.ExportProductRecordList)        // 商品导出记录列表
		ProductRouter.DELETE("deleteProductExportRecord", v1.DeleteProductExportRecord) // 伤处商品导出记录
		ProductRouter.POST("setAttributeStatus", v1.SetAttributeStatus)                 // 设置各种状态
		ProductRouter.GET("getSupplierOptionList", v1.GetSupplierOptionList)            // 获取供应商选项列表
		ProductRouter.POST("displayProductByIds", v1.DisplayProductByIds)               // 批量上下架
		ProductRouter.POST("changeCategoryByIds", v1.ChangeCategoryByIds)               // 批量修改分类等
		ProductRouter.POST("sync", v1.Sync)                                             // 获取供应商选项列表
		ProductRouter.POST("importProduct", v1.ImportProduct)                           // 外部商品导入
		ProductRouter.POST("saveSetting", v1.SaveSetting)                               // 外部商品导入
		ProductRouter.POST("getSetting", v1.GetSetting)                                 // 外部商品导入
		ProductRouter.GET("productVerifyList", v1.GetProductVerifyList)
		ProductRouter.POST("verify", v1.Verify)
		ProductRouter.POST("VerifyByIds", v1.VerifyByIds)
		ProductRouter.GET("getProductVerifyCount", v1.GetProductVerifyCount)
		ProductRouter.POST("productTransfer", v1.ProductTransfer)
		ProductRouter.GET("productTransferRecord", v1.ProductTransferRecord)
		ProductRouter.POST("productMerge", v1.ProductMerge)
		ProductRouter.GET("productMergeRecord", v1.ProductMergeRecord)
		ProductRouter.POST("copyProduct", v1.CopyProduct)
		ProductRouter.POST("exportEditSkuExcel", v1.ExportEditSkuExcel)
		ProductRouter.POST("uploadEditSkuExcel", v1.UploadEditSkuExcel)
		ProductRouter.POST("importEditSkuExcel", v1.ImportEditSkuExcel)
		ProductRouter.POST("updateProductByExcel", v1.UpdateProductByExcel)
		ProductRouter.POST("publishDeleteProductMqMessagesByDeletedAt", v1.PublishDeleteProductMqMessagesByDeletedAt)
		ProductRouter.POST("updateProductSetting", v1.UpdateProductSetting) // 修改商城设置
		ProductRouter.POST("batchSettingProduct", v1.BatchSettingProduct)   // 批量设置
		ProductRouter.POST("unit/list", v1.UnitList)                        // 商品单位分页列表
		ProductRouter.POST("unit/option", v1.UnitOption)                    // 商品单位选择列表
		ProductRouter.POST("unit/create", v1.UnitCreate)                    // 商品单位创建接口
		ProductRouter.POST("unit/delete", v1.UnitDelete)                    // 商品单位删除接口
	}
	TopicRouter := Router.Group("topic")
	{
		TopicRouter.GET("findApplicationSetting", v1.FindTopicSetting)      // 获取专题设置
		TopicRouter.POST("updateApplicationSetting", v1.UpdateTopicSetting) // 修改专题设置
		TopicRouter.POST("createTopic", v1.CreateTopic)                     // 新建Topic
		TopicRouter.DELETE("deleteTopic", v1.DeleteTopic)                   // 删除Topic
		TopicRouter.DELETE("deleteTopicByIds", v1.DeleteTopicByIds)         // 批量删除Topic
		TopicRouter.PUT("updateTopic", v1.UpdateTopic)                      // 更新Topic
		TopicRouter.GET("findTopic", v1.FindTopicGet)                       // 根据ID获取Topic
		TopicRouter.GET("getTopicList", v1.GetTopicList)                    // 获取Topic列表
		TopicRouter.GET("getTopicCategory", v1.GetTopicCategory)            // 获取Topic列表
	}
}
func InitAdminPublicRouter(Router *gin.RouterGroup) {
	OrderRouter := Router.Group("product")
	{
		OrderRouter.GET("findProductSetting", v1.FindProductSetting) // 获取商城设置
		OrderRouter.POST("autoSync", v1.Sync)                        // 获取供应商选项列表
	}
}
func InitUserPublicRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("product")
	{
		ProductRouter.POST("list", fv1.GetProductCardList)                                 // 获取Product列表
		ProductRouter.POST("listNew", fv1.GetProductCardListNew)                           // 获取Product列表
		ProductRouter.GET("list", fv1.GetProductCardListGet)                               // 获取Product列表
		ProductRouter.POST("listByCollectionId", fv1.GetProductCardListByCollection)       // 获取Product列表
		ProductRouter.POST("listByCollectionIdNew", fv1.GetProductCardListByCollectionNew) // 获取Product列表
		ProductRouter.GET("get", fv1.FindProduct)                                          // 根据ID获取Product
		ProductRouter.GET("relation/list", fv1.GetRelationProductCardList)                 // 根据ID获取Product关联商品
		ProductRouter.GET("personal/list", fv1.GetPersonalRelationProductCardList)         // 根据ID获取Product关联商品
		ProductRouter.GET("supplier/list", fv1.GetSupplierProductCardList)                 // 根据店铺ID获取推荐商品
		ProductRouter.POST("importshopfwproduct", v1.ImportShopfwProduct)                  // 导入shopfw 工具商品
		ProductRouter.POST("uploadImage", v1.UploadImage)                                  // 导入shopfw 商品图
	}
}
func InitUserPrivateRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("product")
	{
		ProductRouter.GET("listLogin", fv1.GetProductCardListLogin)      // 获取Product列表登录使用
		ProductRouter.POST("listLogin", fv1.GetProductCardListLoginPost) // 获取Product列表登录使用
		ProductRouter.POST("listLoginNew", av1.ListLoginNew)
		ProductRouter.GET("getByLogin", fv1.FindProductByLogin)                                      // 根据ID获取Product
		ProductRouter.GET("supplier/listLogin", fv1.GetSupplierProductCardListLogin)                 // 获取店铺商品列表登录使用
		ProductRouter.GET("relation/listLogin", fv1.GetRelationProductCardListLogin)                 // 获取推荐商品列表登录使用
		ProductRouter.POST("listByCollectionIdLoginNew", fv1.GetProductCardListByCollectionLoginNew) // 获取专辑商品列表  登录使用
		ProductRouter.GET("resource", fv1.GetResource)                                               // 下载资源
		ProductRouter.POST("getStorageCenterList", av1.GetStorageCenterList)                         // 选品中心/我的选品库(isMyStorageList 1我的选品库 0选品中心)
		ProductRouter.POST("getMyStorageCenterList", av1.GetMyStorageCenterList)                     // 选品中心/我的选品库(isMyStorageList 1我的选品库 0选品中心)
		ProductRouter.POST("addStorageCenter", fv1.AddStorageCenter)                                 // 添加选品中心
		ProductRouter.POST("deleteStorageCenter", fv1.DeleteStorageCenter)                           // 添加选品中心
		ProductRouter.POST("importGoods", fv1.ImportGoods)                                           // 添加选品中心
		ProductRouter.POST("deleteGoods", fv1.DeleteGoods)                                           // 添加选品中心
	}
}

func InitAppPrivateRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("product")
	{
		ProductRouter.POST("storage/cursorList", av1.GetProductCursorList)
		ProductRouter.POST("storage/deleteStorage", av1.DeleteProductStorage)
		ProductRouter.POST("cloud/pushProduct", av1.PushProduct)
		ProductRouter.POST("cloud/getCloudProductList", av1.GetCloudProductList)
		ProductRouter.POST("cloud/updateCloudProduct", av1.UpdateCloudProduct)
		ProductRouter.POST("cloud/updateCloudSku", av1.UpdateCloudSku)
		ProductRouter.DELETE("cloud/deleteProduct", av1.DeleteProduct)            // 删除Product
		ProductRouter.POST("cloud/findProduct", av1.FindProduct)                  // 根据ID获取Product
		ProductRouter.POST("cloud/displayProductByIds", av1.DisplayProductByIds)  // 获取供应商选项列表
		ProductRouter.POST("topicList", v1.GetApplicationTopicList)               // 获取供应商选项列表
		ProductRouter.POST("topicProductList", v1.GetApplicationTopicProductList) // 获取供应商选项列表
		ProductRouter.POST("topicDetail", v1.FindTopic)                           // 获取供应商选项列表
		ProductRouter.POST("topicCategory", v1.GetTopicCategory)                  // 获取供应商选项列表
		ProductRouter.POST("storage/getMyStorageIdsList", av1.GetMyStorageIdsList)
	}
}

func PermInitAppPrivateRouter(Router *gin.RouterGroup) {
	ProductRouter := Router.Group("product")
	{
		ProductRouter.POST("storage/list", av1.GetProductList)
		ProductRouter.POST("guangdian/storage/list", av1.GetGuangdianProductList)
		ProductRouter.POST("cake/storage/list", av1.GetCakeProductList)
		ProductRouter.POST("storage/detailList", av1.GetProductDetailList)
		ProductRouter.POST("storage/stockList", av1.GetProductStock)
		ProductRouter.POST("storage/addStorage", av1.AddProductStorage)
	}
}
