package route

import (
	"aggregated-payment-split-settlement/api/admin"
	"github.com/gin-gonic/gin"
)

func InitAdminPrivateRouter(Router *gin.RouterGroup) {
	serviceProviderSystemRouter := Router.Group("aggregatedPaymentSplitSettlement")
	{
		serviceProviderSystemRouter.GET("getSysAggregatedPaymentSplitSettlementSetting", admin.GetSysAggregatedPaymentSplitSettlementSetting)    //获取基础设置
		serviceProviderSystemRouter.POST("saveSysAggregatedPaymentSplitSettlementSetting", admin.SaveSysAggregatedPaymentSplitSettlementSetting) //保存基础设置

		//主商户同步 同系统-支付设置-聚合支付-同步主商户

		serviceProviderSystemRouter.GET("getCommunicationPaymentInfoList", admin.GetCommunicationPaymentInfoList)                              //主商户列表
		serviceProviderSystemRouter.GET("getCommunicationPaymentInfoById", admin.GetCommunicationPaymentInfoById)                              //通过id获取主商户详情
		serviceProviderSystemRouter.POST("getCommunicationPaymentInfoByPaymentStoreCode", admin.GetCommunicationPaymentInfoByPaymentStoreCode) //通过id获取主商户详情

		serviceProviderSystemRouter.GET("getCommunicationPaymentInfoAll", admin.GetCommunicationPaymentInfoAll) //获取所有主商户 支持分账的

		serviceProviderSystemRouter.POST("getCommunicationSubMerList", admin.GetCommunicationSubMerList) //子商户列表
		serviceProviderSystemRouter.GET("getCommunicationSubMerById", admin.GetCommunicationSubMerById)  //通过id获取子商户详情		serviceProviderSystemRouter.GET("getCommunicationPaymentInfoAll", admin.GetCommunicationPaymentInfoAll)   //获取所有主商户

		serviceProviderSystemRouter.POST("synSubMer", admin.SynSubMer)         //同步子商户
		serviceProviderSystemRouter.POST("addSubMer", admin.AddSubMer)         //新增子商户
		serviceProviderSystemRouter.POST("bindSmallShop", admin.BindSmallShop) //绑定小商店 传子商户id与小商店id

		serviceProviderSystemRouter.POST("applySubMerSplitSettlement", admin.ApplySubMerSplitSettlement)                                       //申请开通分账 (只有拉卡拉才有)
		serviceProviderSystemRouter.POST("getSubMerSplitSettlementLogByPaymentStoreCode", admin.GetSubMerSplitSettlementLogByPaymentStoreCode) //获取审核记录 -- 只传子商户的payment_store_code
		serviceProviderSystemRouter.POST("saveSubMerSplitSettlement", admin.SaveSubMerSplitSettlement)                                         //修改分账 (只有拉卡拉才有)
		serviceProviderSystemRouter.POST("revenueSharing", admin.RevenueSharing)                                                               //手动分账

		serviceProviderSystemRouter.POST("getSubMerSplitSettlementLogList", admin.GetSubMerSplitSettlementLogList) //分账开户审核列表，详情直接使用列表信息就可以一样的

		serviceProviderSystemRouter.POST("bindSubMerSplitSettlement", admin.BindSubMerSplitSettlement) //绑定解绑分账方（只有拉卡拉才有）

		serviceProviderSystemRouter.POST("getSplitShareRecordList", admin.GetSplitShareRecordList) //分账记录
		serviceProviderSystemRouter.POST("getSplitShareRecordByID", admin.GetSplitShareRecordByID) //通过id查询分账记录详情

	}
}

// 后端公共
func InitAdminPublicRouter(Router *gin.RouterGroup) {
	//serviceProviderSystem := Router.Group("serviceProviderSystem")
	//{
	//}

}

// 前端私有
func InitUserPrivateRouter(Router *gin.RouterGroup) {

}

// 前端公共
func InitUserPublicRouter(Router *gin.RouterGroup) {

}

// 采购端API
func InitAppPrivateRouter(Router *gin.RouterGroup) {
	//serviceProviderSystem := Router.Group("ServiceProviderSystem")
	//{
	//
	//}

}
