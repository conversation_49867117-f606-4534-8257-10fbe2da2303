package request

import (
	serviceProviderSystemModel "service-provider-system/model"
	yzRequest "yz-go/request"
)

type CommunicationMerchantSyncDataSearch struct {
	yzRequest.PageInfo
	serviceProviderSystemModel.CommunicationMerchantSyncData
	StartAT string `json:"start_at"  form:"start_at"`
	EndAT   string `json:"end_at" form:"end_at"`
}

type CommunicationSubMerSearch struct {
	yzRequest.PageInfo
	serviceProviderSystemModel.CommunicationSubMer
	StartAT  string `json:"start_at"  form:"start_at"`
	EndAT    string `json:"end_at" form:"end_at"`
	UserId   uint   `json:"user_id" form:"user_id"`
	Tel      string `json:"tel" form:"tel"`
	NickName string `json:"nick_name" form:"nick_name"`
}

type SubMerSplitSettlementLogSearch struct {
	yzRequest.PageInfo
	serviceProviderSystemModel.SubMerSplitSettlementLog
	StartAT string `json:"start_at"  form:"start_at"`
	EndAT   string `json:"end_at" form:"end_at"`
	// AuditStatus 审核状态：0-待审核, 1-审核通过, 2-审核驳回  全部不传这个字段
	AuditStatus *int `json:"audit_status" gorm:"column:audit_status;default:0"` // 审核状态：0-待审核, 1-审核通过, 2-审核驳回
	// MerRegName 商户注册名称
	MerRegName string `gorm:"column:mer_reg_name;comment:商户注册名称" json:"mer_reg_name"`

	// MerOptName 商户经营名称
	MerOptName string `gorm:"column:mer_opt_name;comment:商户经营名称" json:"mer_opt_name"`
}

type SplitShareRecordSearch struct {
	yzRequest.PageInfo
	serviceProviderSystemModel.SplitShareRecord
	StartAT            string `json:"start_at"  form:"start_at"`
	EndAT              string `json:"end_at" form:"end_at"`
	OrderStatus        int    `json:"order_status"`
	SplitStatus        *int   `json:"split_status" gorm:"column:split_status;comment:分账状态0待处理1处理中2部分处理3处理完成-1处理失败-2请求异常;default:0;type:int(11);"`
	RequestPayTypeName string `json:"request_pay_type_name" gorm:"column:request_pay_type_name;comment:支付类型名称" json:"request_pay_type_name"` //支付编号

}
