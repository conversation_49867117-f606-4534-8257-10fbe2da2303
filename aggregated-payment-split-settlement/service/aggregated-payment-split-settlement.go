package service

import (
	"aggregated-payment-split-settlement/request"
	"errors"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"math"
	shopOrder "order/model"
	paymentModel "payment/model"
	"regexp"
	"service-provider-system/common"
	"service-provider-system/model"
	serviceProviderSystemRequest "service-provider-system/request"
	smallShopModel "small-shop/model"
	smallShopService "small-shop/service"
	"strconv"
	"yz-go/component/log"

	"yz-go/source"
)

// 支持分账的通道

type CommunicationMerchantSyncDataResponse struct {
	model.CommunicationMerchantSyncData
	CommunicationMerchantDetail model.CommunicationMerchantDetail `json:"communication_merchant_detail" gorm:"foreignKey:PaymentStoreCode;references:PaymentStoreCode"`
}

func (CommunicationMerchantSyncDataResponse) TableName() string {
	return "communication_merchant_sync_data"
}
func GetCommunicationPaymentInfoList(info request.CommunicationMerchantSyncDataSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&CommunicationMerchantSyncDataResponse{}).Joins("left join communication_merchant_details on communication_merchant_details.payment_store_code = communication_merchant_sync_data.payment_store_code").Preload("CommunicationMerchantDetail")
	var data []CommunicationMerchantSyncDataResponse
	// 应用条件
	if info.StoreCode != "" {
		db = db.Where("communication_merchant_sync_data.store_code = ?", info.StoreCode)
	}
	if info.PaymentStoreCode != "" {
		db = db.Where("communication_merchant_sync_data.payment_store_code = ?", info.PaymentStoreCode)
	}

	if info.ChannelType != 0 {
		db = db.Where("communication_merchant_sync_data.channel_type = ?", info.ChannelType)
	} else {
		db = db.Where("communication_merchant_sync_data.channel_type in ?", paymentModel.SplitChannelType)

	}

	if info.StartAT != "" {
		db = db.Where("communication_merchant_details.create_time >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db = db.Where("communication_merchant_details.create_time <= ?", info.EndAT)
	}
	// 统计总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 查询结果
	err = db.Limit(limit).Select("communication_merchant_sync_data.*").Order("created_at desc").Offset(offset).Find(&data).Error
	if err != nil {
		return
	}

	return err, data, total
}

func GetCommunicationPaymentInfoAll() (err error, list interface{}) {

	db := source.DB().Model(&CommunicationMerchantSyncDataResponse{}).Joins("left join communication_merchant_details on communication_merchant_details.payment_store_code = communication_merchant_sync_data.payment_store_code").Preload("CommunicationMerchantDetail")
	var data []CommunicationMerchantSyncDataResponse

	db = db.Where("communication_merchant_sync_data.channel_type in ?", paymentModel.SplitChannelTypeCreateSurMer)

	// 查询结果
	err = db.Select("communication_merchant_sync_data.*").Order("created_at desc").Find(&data).Error
	if err != nil {
		return
	}

	return err, data
}

func GetCommunicationPaymentInfoById(info request.CommunicationMerchantSyncDataSearch) (err error, list interface{}) {
	if info.ID == 0 {
		err = errors.New("请提交id")
		return
	}
	db := source.DB().Model(&CommunicationMerchantSyncDataResponse{}).Preload("CommunicationMerchantDetail")
	var data CommunicationMerchantSyncDataResponse
	// 应用条件

	db = db.Where("id = ?", info.ID)

	// 查询结果
	err = db.First(&data).Error
	if err != nil {
		return
	}

	return err, data
}

func GetCommunicationSubMerResponseByPaymentStoreCode(PaymentStoreCode string) (err error, data CommunicationSubMerResponse) {
	if PaymentStoreCode == "" {
		err = errors.New("请提交PaymentStoreCode")
		return
	}
	db := source.DB().Model(&CommunicationSubMerResponse{}).Preload("CommunicationSubMerDetail").Preload("BindOrUnbindSubMerSplitSettlementLog")

	db = db.Where("payment_store_code = ?", PaymentStoreCode)

	// 查询结果
	err = db.First(&data).Error
	if err != nil {
		return
	}

	return err, data
}

func GetCommunicationPaymentInfoByPaymentStoreCode(PaymentStoreCode string) (err error, data CommunicationMerchantSyncDataResponse) {
	if PaymentStoreCode == "" {
		err = errors.New("请提交PaymentStoreCode")
		return
	}
	db := source.DB().Model(&CommunicationMerchantSyncDataResponse{}).Preload("CommunicationMerchantDetail")

	// 应用条件

	db = db.Where("payment_store_code = ?", PaymentStoreCode)

	// 查询结果
	err = db.First(&data).Error
	if err != nil {
		return
	}

	return err, data
}

type CommunicationSubMerResponse struct {
	model.CommunicationSubMer
	CommunicationSubMerDetail            model.CommunicationSubMerDetail            `json:"communication_sub_mer_detail" gorm:"foreignKey:PaymentStoreCode;references:PaymentStoreCode"`
	SmallShop                            smallShopModel.SmallShop                   `json:"small_shop" gorm:"foreignKey:SmallShopId;references:id"`
	BindOrUnbindSubMerSplitSettlementLog model.BindOrUnbindSubMerSplitSettlementLog `json:"bind_or_unbind_sub_mer_split_settlement_log" gorm:"foreignKey:BindOrUnbindSubMerSplitSettlementLogID;references:ID"` //关联绑定/解绑审核记录
	SubMerSplitSettlementLog             model.SubMerSplitSettlementLog             `json:"sub_mer_split_settlement_logs" gorm:"foreignKey:SubMerSplitSettlementLogID;references:ID"`                           //

}

func (CommunicationSubMerResponse) TableName() string {
	return "communication_sub_mers"
}
func GetCommunicationSubMerList(info request.CommunicationSubMerSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&CommunicationSubMerResponse{}).Joins("left join communication_sub_mer_details on communication_sub_mer_details.payment_store_code = communication_sub_mers.payment_store_code").Preload("CommunicationSubMerDetail").Preload("BindOrUnbindSubMerSplitSettlementLog").Preload("SubMerSplitSettlementLog")
	var data []CommunicationSubMerResponse
	// 应用条件
	if info.ParentStoreCode != "" {
		db = db.Where("communication_sub_mers.parent_store_code = ?", info.ParentStoreCode)
	}

	if info.PaymentStoreCode != "" {
		db = db.Where("communication_sub_mers.payment_store_code = ?", info.PaymentStoreCode)
	}
	if info.SeparateBindStatus != nil {
		db = db.Where("communication_sub_mers.separate_bind_status = ?", info.SeparateBindStatus)
	}

	if info.MerchantNo != "" {
		db = db.Where("communication_sub_mers.merchant_no LIKE ?", "%"+info.MerchantNo+"%")

	}
	if info.MerRegName != "" {
		db = db.Where("communication_sub_mers.mer_reg_name LIKE ?", "%"+info.MerRegName+"%")

	}
	if info.MerOptName != "" {
		db = db.Where("communication_sub_mers.mer_opt_name LIKE ?", "%"+info.MerOptName+"%")

	}
	if info.ChannelType != 0 {
		db = db.Where("communication_sub_mers.channel_type = ?", info.ChannelType)
	} else {
		db = db.Where("communication_sub_mers.channel_type in ?", paymentModel.SplitChannelType)

	}

	if info.StartAT != "" {
		db = db.Where("communication_sub_mer_details.create_time >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db = db.Where("communication_sub_mer_details.create_time <= ?", info.EndAT)
	}
	if info.UserId != 0 {
		db = db.Joins("left join small_shops on small_shops.id = communication_sub_mers.small_shop_id").Where("uid = ?", info.UserId)
	}
	if info.Tel != "" {
		db = db.Joins("left join small_shops on small_shops.id = communication_sub_mers.small_shop_id")
		db = db.Joins("left join small_shop_users on small_shops.uid = small_shop_users.id")
		db = db.Where("small_shop_users.mobile LIKE ?", "%"+info.Tel+"%")

	}
	if info.NickName != "" {
		db = db.Joins("left join small_shops on small_shops.id = communication_sub_mers.small_shop_id")
		db = db.Joins("left join small_shop_users on small_shops.uid = small_shop_users.id")
		db = db.Where("small_shop_users.nick_name LIKE ?", "%"+info.NickName+"%")
	}

	//Tel      string `json:"tel" form:"tel"`
	//NickName string `json:"nick_name" form:"nick_name"`
	// 统计总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 查询结果
	err = db.Limit(limit).Select("communication_sub_mers.*").Order("created_at desc").Offset(offset).Find(&data).Error
	if err != nil {
		return
	}

	return err, data, total
}

func GetCommunicationSubMerById(info request.CommunicationSubMerSearch) (err error, list interface{}) {
	if info.ID == 0 {
		err = errors.New("请提交id")
		return
	}
	db := source.DB().Model(&CommunicationSubMerResponse{}).Preload("CommunicationSubMerDetail").Preload("BindOrUnbindSubMerSplitSettlementLog")
	var data CommunicationSubMerResponse
	// 应用条件

	db = db.Where("id = ?", info.ID)

	// 查询结果
	err = db.First(&data).Error
	if err != nil {
		return
	}

	return err, data
}

// 同步子商户
func SynSubMer() (err error) {
	err, rd := common.Initial()
	if err != nil {
		return
	}
	var requestSearch serviceProviderSystemRequest.GetPaymentMerchantSync
	requestSearch.Page = 1
	requestSearch.PageSize = 50
	data, nextPage, err := rd.GetSubMerListSync(requestSearch)
	if err != nil {
		log.Log().Error("获取主商户信息失败", zap.Any("err", err))
		return
	}
	if len(data) > 0 {
		//获取之前保存的
		var olds []model.CommunicationSubMer
		source.DB().Model(&model.CommunicationSubMer{}).Find(&olds)
		var oldsMake = make(map[string]uint)
		var oldsSubMerSplitSettlementLogIDMake = make(map[string]uint)
		if olds != nil {
			for _, item := range olds {
				oldsMake[item.PaymentStoreCode] = item.ID
				oldsSubMerSplitSettlementLogIDMake[item.PaymentStoreCode] = item.SubMerSplitSettlementLogID
			}
		}
		//获取之前保存的
		var oldCommunicationSubMerDetail []model.CommunicationSubMerDetail
		source.DB().Model(&model.CommunicationSubMerDetail{}).Find(&oldCommunicationSubMerDetail)
		var oldCommunicationSubMerDetailMake = make(map[string]uint)
		if oldCommunicationSubMerDetail != nil {
			for _, item := range oldCommunicationSubMerDetail {
				oldCommunicationSubMerDetailMake[item.PaymentStoreCode] = item.ID
			}
		}
		go GetSynSubMerStart(rd, requestSearch, data, nextPage, oldsMake, oldCommunicationSubMerDetailMake, oldsSubMerSplitSettlementLogIDMake)

	} else {
		err = errors.New("没有任何主商户信息")
		return
	}
	return
}
func GetSynSubMerStart(rd common.RequestData, requestSearch serviceProviderSystemRequest.GetPaymentMerchantSync, data []common.SubMerListResponse, nextPage bool, oldsMake map[string]uint, oldDetail map[string]uint, oldsSubMerSplitSettlementLogIDMake map[string]uint) {
	var err error
	for _, item := range data {
		if oldsMake != nil && len(oldsMake) > 0 {
			value, exists := oldsMake[item.PaymentStoreCode]
			if exists {
				err = source.DB().Model(&model.CommunicationSubMer{}).Where("id = ?", value).Updates(&model.CommunicationSubMer{
					SubMerListResponse: item,
				}).Error
				if err != nil {
					log.Log().Error("修改子商户失败", zap.Any("err", err), zap.Any("item", item))
				}
			} else {
				err = source.DB().Model(&model.CommunicationSubMer{}).Create(&model.CommunicationSubMer{
					SubMerListResponse: item,
				}).Error
				if err != nil {
					log.Log().Error("创建子商户失败", zap.Any("err", err), zap.Any("item", item))
				}
			}
		} else {
			err = source.DB().Model(&model.CommunicationSubMer{}).Create(&model.CommunicationSubMer{
				SubMerListResponse: item,
			}).Error
			if err != nil {
				log.Log().Error("创建子商户失败", zap.Any("err", err), zap.Any("item", item))
			}
		}
		//创建子商户
		var merchantDetail common.SubMerDetailResponse
		err, merchantDetail = CreateTableSubMer(rd, item.PaymentStoreCode, oldDetail, item)
		if err != nil {
			log.Log().Error("创建子商户详情失败", zap.Any("err", err), zap.Any("item", item))
			continue
		}

		if oldsSubMerSplitSettlementLogIDMake != nil && len(oldsSubMerSplitSettlementLogIDMake) > 0 {
			//如果不存在或者记录id为0
			value, exists := oldsSubMerSplitSettlementLogIDMake[item.PaymentStoreCode]
			if !exists || value == 0 {
				var resData common.MerchantAudit
				resData, err = rd.GetSubMerSplitSettlement(item.PaymentStoreCode)
				if err != nil {
					log.Log().Error("聚合支付分账查询审核修改状态失败", zap.Any("err", err), zap.Any("item", item))
					continue
				}
				var typeStatus = 0
				switch resData.SplitType {
				case 1:
					typeStatus = 0
					break
				case 2:
					typeStatus = 1
					break
				}
				var subMerSplitSettlementLog = model.SubMerSplitSettlementLog{
					SubMerSplitSettlement: common.SubMerSplitSettlement{
						PaymentStoreCode: resData.PaymentStoreCode,
						ContactMobile:    merchantDetail.MerContactMobile,
						SplitLowestRatio: merchantDetail.SplitLowestRatio,
					},
					Type:            typeStatus,
					AuditStatus:     resData.SplitStatus,
					AuditStatusDesc: resData.SplitStatusDesc,
				}
				source.DB().Model(&model.SubMerSplitSettlementLog{}).Create(&subMerSplitSettlementLog)
				source.DB().Model(&model.CommunicationSubMer{}).Where("payment_store_code = ?", item.PaymentStoreCode).Updates(map[string]interface{}{"sub_mer_split_settlement_log_id": subMerSplitSettlementLog.ID})
			}
		}

		//var merchantDetail common.SubMerDetailResponse
		//merchantDetail, err = rd.GetSubMerDetail(item.PaymentStoreCode)
		//if err != nil {
		//	log.Log().Error("获取子商户详情失败", zap.Any("err", err), zap.Any("item", item))
		//	continue
		//}
		//value, exists := oldDetail[item.PaymentStoreCode]
		//if exists {
		//	err = source.DB().Model(&model.CommunicationSubMerDetail{}).Where("id = ?", value).Updates(&model.CommunicationSubMerDetail{
		//		SubMerDetailResponse: merchantDetail,
		//		PaymentStoreCode:     item.PaymentStoreCode,
		//	}).Error
		//	if err != nil {
		//		log.Log().Error("修改子商户详情失败", zap.Any("err", err), zap.Any("item", item))
		//	}
		//} else {
		//	err = source.DB().Model(&model.CommunicationSubMerDetail{}).Create(&model.CommunicationSubMerDetail{
		//		SubMerDetailResponse: merchantDetail,
		//		PaymentStoreCode:     item.PaymentStoreCode,
		//	}).Error
		//	if err != nil {
		//		log.Log().Error("创建子商户详情失败", zap.Any("err", err), zap.Any("item", item))
		//	}
		//}
	}
	//如果存在下一页
	if nextPage == true {
		requestSearch.Page++
		data, nextPage, err = rd.GetSubMerListSync(requestSearch)
		GetSynSubMerStart(rd, requestSearch, data, nextPage, oldsMake, oldDetail, oldsSubMerSplitSettlementLogIDMake)
	} else {

	}

}

// 创建子账户
func CreateTableSubMer(rd common.RequestData, paymentStoreCode string, oldDetail map[string]uint, mer common.SubMerListResponse) (err error, merchantDetail common.SubMerDetailResponse) {
	merchantDetail, err = rd.GetSubMerDetail(paymentStoreCode)
	if err != nil {
		log.Log().Error("获取子商户详情失败", zap.Any("err", err), zap.Any("paymentStoreCode", paymentStoreCode))
		return
	}
	if merchantDetail.CreateTime != "" && mer.CreatedAt != "" {
		merchantDetail.CreateTime = mer.CreatedAt
	}
	value, exists := oldDetail[paymentStoreCode]
	if exists {
		err = source.DB().Model(&model.CommunicationSubMerDetail{}).Where("id = ?", value).Updates(&model.CommunicationSubMerDetail{
			SubMerDetailResponse: merchantDetail,
			PaymentStoreCode:     paymentStoreCode,
		}).Error
		if err != nil {
			log.Log().Error("修改子商户详情失败", zap.Any("err", err), zap.Any("paymentStoreCode", paymentStoreCode))
			return
		}
	} else {
		err = source.DB().Model(&model.CommunicationSubMerDetail{}).Create(&model.CommunicationSubMerDetail{
			SubMerDetailResponse: merchantDetail,
			PaymentStoreCode:     paymentStoreCode,
		}).Error
		if err != nil {
			log.Log().Error("创建子商户详情失败", zap.Any("err", err), zap.Any("paymentStoreCode", paymentStoreCode))
			return
		}
	}
	return
}

// 新增子商户
func AddSubMer(data serviceProviderSystemRequest.AddSubMer) (err error) {
	if data.ParentStoreCode == "" {
		err = errors.New("请提交父商户号")
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}
	var communicationMerchantSyncData model.CommunicationMerchantSyncData
	err = source.DB().Model(&model.CommunicationMerchantSyncData{}).Where("payment_store_code = ?", data.ParentStoreCode).First(&communicationMerchantSyncData).Error
	if err != nil {
		err = errors.New("父商户号不存在" + err.Error())
		return
	}
	var addSubMerResponse common.SubMerListResponse
	switch communicationMerchantSyncData.ChannelType {
	case 1: //拉卡拉
		err, addSubMerResponse = AddSubMerLaKala(data, rd)
		break
	case 2: //汇付
		err, addSubMerResponse = AddSubMerHuiFu(data, rd)
		break
	default:
		err = errors.New("渠道类型暂不支持添加子商户")
		break
	}
	if err != nil {
		return
	}
	//API创建成功新增子商户并且同步子商户详情
	err = source.DB().Create(&model.CommunicationSubMer{
		SubMerListResponse: addSubMerResponse,
	}).Error
	if err != nil {
		err = errors.New("API创建成功,本地生成记录失败请点击同步子商户按钮进行同步本次新增" + err.Error())
		return
	}
	var oldDetail map[string]uint
	err, _ = CreateTableSubMer(rd, addSubMerResponse.PaymentStoreCode, oldDetail, addSubMerResponse)
	if err != nil {
		err = errors.New("API创建成功,本地生成记录失败请点击同步子商户按钮进行同步本次新增" + err.Error())
		return
	}
	return
}

// 新增拉卡拉
func AddSubMerLaKala(data serviceProviderSystemRequest.AddSubMer, rd common.RequestData) (err error, addSubMerResponse common.SubMerListResponse) {
	if data.ParentStoreCode == "" {
		err = errors.New("父级商户数据通商户号不能为空")
		return
	}
	if data.RegisterName == "" {
		err = errors.New("商户注册名称不能为空")
		return
	}
	//if data.OperateName == "" {
	//	err = errors.New("商户经营名称不能为空")
	//	return
	//}
	if data.MerCupNo == "" {
		err = errors.New("822银联商户号不能为空")
		return
	}
	if data.MerchantNo == "" {
		err = errors.New("商户号不能为空")
		return
	}
	if data.TermNo == "" {
		err = errors.New("终端号不能为空")
		return
	}
	addSubMerResponse, err = rd.AddLaKaLaSubMer(data)
	return
}

// 新增汇付
func AddSubMerHuiFu(data serviceProviderSystemRequest.AddSubMer, rd common.RequestData) (err error, addSubMerResponse common.SubMerListResponse) {
	if data.MerchantNo == "" {
		err = errors.New("商户号不可为空")
		return
	}
	addSubMerResponse, err = rd.AddHuiFuSubMer(data)
	return
}

// 开户
func ApplySubMerSplitSettlement(data common.SubMerSplitSettlement) (err error) {
	err = validateSubMerSplitSettlement(data)
	if err != nil {
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}
	_, err = rd.ApplySubMerSplitSettlement(data)
	if err != nil {
		return
	}
	var logEntry = &model.SubMerSplitSettlementLog{
		Type:                  0,
		SubMerSplitSettlement: data,
	}
	err = source.DB().Create(&logEntry).Error
	if err != nil {
		err = errors.New("申请成功,记录创建失败,请联系客服" + err.Error())
		return
	}
	err = source.DB().Model(&model.CommunicationSubMer{}).Where("payment_store_code = ?", data.PaymentStoreCode).Updates(map[string]interface{}{"sub_mer_split_settlement_log_id": logEntry.ID}).Error
	if err != nil {
		err = errors.New("申请成功,记录关联失败,请联系客服" + err.Error())
		return
	}
	return
}

func GetSubMerSplitSettlementLogByPaymentStoreCode(paymentStoreCode string) (err error, data model.SubMerSplitSettlementLog) {
	err = source.DB().Model(&model.SubMerSplitSettlementLog{}).Where("payment_store_code = ?", paymentStoreCode).Order("id desc").First(&data).Error
	return
}

// 修改
func SaveSubMerSplitSettlement(data common.SubMerSplitSettlement) (err error) {
	err = validateSubMerSplitSettlement(data)
	if err != nil {
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}
	_, err = rd.SaveSubMerSplitSettlement(data)
	if err != nil {
		return
	}
	err = source.DB().Create(&model.SubMerSplitSettlementLog{
		Type:                  1,
		SubMerSplitSettlement: data,
	}).Error
	if err != nil {
		err = errors.New("申请成功,记录创建失败,请联系客服" + err.Error())
		return
	}
	return
}

func validateSubMerSplitSettlement(data common.SubMerSplitSettlement) error {

	if data.PaymentStoreCode == "" {
		return errors.New("聚合支付数据通商户号不能为空")
	}
	var subMerSplitSettlementLog model.SubMerSplitSettlementLog
	source.DB().Model(&model.SubMerSplitSettlementLog{}).Where("payment_store_code = ?", data.PaymentStoreCode).First(&subMerSplitSettlementLog)
	if subMerSplitSettlementLog.ID > 0 && subMerSplitSettlementLog.AuditStatus == 0 {
		return errors.New("存在待审核记录")
	}

	if data.ContactMobile == "" {
		return errors.New("联系手机号不能为空")
	}

	if data.SplitLowestRatio == "" {
		return errors.New("最低分账比例不能为空")
	} else {
		// 可选：验证是否符合百分比格式（如 "70" 或 "70.50"）
		match, _ := regexp.MatchString(`^\d+(\.\d{1,2})?$`, data.SplitLowestRatio)
		if !match {
			return errors.New("最低分账比例格式不正确，应为数字或带两位小数的数字")
		}
	}

	if data.EntrustFileName == "" {
		return errors.New("分账结算委托书文件名称不能为空")
	}

	if data.EntrustFile == "" {
		return errors.New("分账结算委托书文件不能为空")
	}

	//if data.EntrustFilePath == "" {
	//	return errors.New("分账结算委托书文件路径不能为空")
	//}

	return nil
}
func SynSubMerSplitSettlementStatusCron() (err error) {
	var subMerSplitSettlementLog []model.SubMerSplitSettlementLog
	source.DB().Model(&model.SubMerSplitSettlementLog{}).Where("status = ?", 0).Find(&subMerSplitSettlementLog)
	//不存在待审核记录直接跳过
	if len(subMerSplitSettlementLog) == 0 {
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}
	var oldCommunicationSubMerDetail []model.CommunicationSubMerDetail
	source.DB().Model(&model.CommunicationSubMerDetail{}).Find(&oldCommunicationSubMerDetail)
	var oldCommunicationSubMerDetailMake = make(map[string]uint)
	if oldCommunicationSubMerDetail != nil {
		for _, item := range oldCommunicationSubMerDetail {
			oldCommunicationSubMerDetailMake[item.PaymentStoreCode] = item.ID
		}
	}
	var resData common.MerchantAudit
	for _, item := range subMerSplitSettlementLog {
		resData, err = rd.GetSubMerSplitSettlement(item.PaymentStoreCode)
		if err != nil {
			log.Log().Error("聚合支付分账查询审核修改状态失败", zap.Any("err", err), zap.Any("item", item))
			continue
		}
		item.AuditStatus = resData.SplitStatus
		item.AuditStatusDesc = resData.SplitStatusDesc
		err = source.DB().Save(&item).Error
		if err != nil {
			err = errors.New("保存审核记录失败" + err.Error())
			return
		}
		//如果审核通过更新这个子账号详情
		if resData.SplitStatus == 1 {
			var mer common.SubMerListResponse
			err, _ = CreateTableSubMer(rd, item.PaymentStoreCode, oldCommunicationSubMerDetailMake, mer)
			if err != nil {
				err = errors.New("审核通过子账户更新失败" + err.Error())
				return
			}
		}
	}

	return
}

type SplitShareRecordAmountStatusDatas []SplitShareRecordAmountStatusData
type SplitShareRecordAmountStatusData struct {
	Count  uint `json:"count" form:"count" gorm:"column:count;comment:数量;type:uint;size:11;"`       // 订单数量
	Status int  `json:"status" form:"status" gorm:"column:status;comment:状态;type:smallint;size:3;"` // 订单状态

}

func SynSubMerSplitPaymentStatusCron() (err error) {
	var splitShareRecordAmount []model.SplitShareRecordAmount
	var status = []int{1, 2}
	source.DB().Model(&model.SplitShareRecordAmount{}).Where("split_status in ?", status).Order("split_share_record_id asc").Find(&splitShareRecordAmount)
	//不存在待审核记录直接跳过
	if len(splitShareRecordAmount) == 0 {
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}

	var resData common.SplitStatusResponse
	var oldSplitShareRecordId uint = 0
	var splitStatusDesc string
	for _, item := range splitShareRecordAmount {
		//如果主记录表id不是0 并且与本次的不一致证明分账状态已经同步完成这里判断一下是否全部完成
		if oldSplitShareRecordId != item.SplitShareRecordId {
			_ = TagMainLedgerStatus(oldSplitShareRecordId, splitStatusDesc)
			oldSplitShareRecordId = item.SplitShareRecordId
			splitStatusDesc = ""
		}
		resData, err = rd.GetSplitAmtStatusQuery(item.OutSeparateNo)
		if err != nil {
			log.Log().Error("聚合支付分账查询分账状态失败", zap.Any("err", err), zap.Any("item", item))
			continue
		}
		// 分账状态: PROCESSING(处理中), ACCEPTED(已受理), SUCCESS(成功), FAIL(失败)
		var splitStatus = 1 //默认直接标记处理中 避免请求之后返回其他状态
		switch resData.Status {
		case "PROCESSING":
			splitStatus = 1
			break
		case "ACCEPTED":
			splitStatus = 2
			break
		case "SUCCESS":
			splitStatus = 3
			break
		case "FAIL":
			splitStatus = -1
			splitStatusDesc += resData.StatusDesc + ";"
			break
		}
		err = source.DB().Model(&model.SplitShareRecordAmount{}).Where("id = ?", item.ID).Updates(map[string]interface{}{"split_status": splitStatus, "split_status_desc": resData.StatusDesc}).Error
		if err != nil {
			err = errors.New("保存分账记录失败" + err.Error())
			return
		}
	}
	//循环结束执行一下最后一个
	_ = TagMainLedgerStatus(oldSplitShareRecordId, splitStatusDesc)

	return
}

// 主分账记录标记
func TagMainLedgerStatus(oldSplitShareRecordId uint, splitStatusDesc string) (err error) {
	if oldSplitShareRecordId != 0 {
		var splitShareRecordAmountStatusDatas SplitShareRecordAmountStatusDatas
		err = source.DB().Model(&model.SplitShareRecordAmount{}).Select("split_status as status,COALESCE(count(id), 0) as count").Where("split_share_record_id = ?", oldSplitShareRecordId).Group("split_status").Find(&splitShareRecordAmountStatusDatas).Error
		if err != nil {
			log.Log().Error("聚合支付分账查询分账状态失败", zap.Any("err", err))
			return
		}
		var successCount, failCount, waitCount uint
		for _, splitShareRecordAmountStatusData := range splitShareRecordAmountStatusDatas {
			switch splitShareRecordAmountStatusData.Status {
			case 0:
				waitCount += splitShareRecordAmountStatusData.Count
				break
			case 1:
				waitCount += splitShareRecordAmountStatusData.Count
				break
			case 2:
				waitCount += splitShareRecordAmountStatusData.Count
				break
			case 3:
				successCount += splitShareRecordAmountStatusData.Count
				break
			case -1:
				failCount += splitShareRecordAmountStatusData.Count
				break
			}
		}
		//代表失败了 标记主表为失败
		if failCount > 0 {
			err = source.DB().Model(&model.SplitShareRecord{}).Where("id = ?", oldSplitShareRecordId).Updates(map[string]interface{}{"split_status": -1, "error_msg": splitStatusDesc}).Error
		} else if waitCount == 0 && successCount > 0 {
			err = source.DB().Model(&model.SplitShareRecord{}).Where("id = ?", oldSplitShareRecordId).Updates(map[string]interface{}{"split_status": 3}).Error

		} else if waitCount > 0 {
			err = source.DB().Model(&model.SplitShareRecord{}).Where("id = ?", oldSplitShareRecordId).Updates(map[string]interface{}{"split_status": 2}).Error

		}
		if err != nil {
			log.Log().Error("聚合支付分账保存主分账记录失败", zap.Any("err", err))
		}
	}
	return
}

type SubMerSplitSettlementLogResponse struct {
	model.SubMerSplitSettlementLog
	CommunicationSubMerDetail model.CommunicationSubMerDetail `json:"communication_sub_mer_detail" gorm:"foreignKey:PaymentStoreCode;references:PaymentStoreCode"`
}

func (SubMerSplitSettlementLogResponse) TableName() string {
	return "sub_mer_split_settlement_logs"
}
func GetSubMerSplitSettlementLogList(info request.SubMerSplitSettlementLogSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&SubMerSplitSettlementLogResponse{}).Joins("left join communication_sub_mer_details on communication_sub_mer_details.payment_store_code = sub_mer_split_settlement_logs.payment_store_code").Preload("CommunicationSubMerDetail")
	var data []SubMerSplitSettlementLogResponse
	// 应用条件

	if info.PaymentStoreCode != "" {
		db = db.Where("communication_sub_mer_details.payment_store_code = ?", info.PaymentStoreCode)
	}
	if info.MerRegName != "" {
		db = db.Where("communication_sub_mer_details.mer_reg_name LIKE ?", "%"+info.MerRegName+"%")
	}
	if info.MerOptName != "" {
		db = db.Where("communication_sub_mer_details.mer_opt_name LIKE ?", "%"+info.MerOptName+"%")
	}

	if info.AuditStatus != nil {
		db = db.Where("sub_mer_split_settlement_logs.audit_status = ?", info.AuditStatus)
	}

	if info.ContactMobile != "" {
		db = db.Where("sub_mer_split_settlement_logs.contact_mobile LIKE ?", "%"+info.ContactMobile+"%")
	}

	if info.StartAT != "" {
		db = db.Where("sub_mer_split_settlement_logs.created_at >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db = db.Where("sub_mer_split_settlement_logs.created_at <= ?", info.EndAT)
	}

	// 统计总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 查询结果
	err = db.Limit(limit).Select("sub_mer_split_settlement_logs.*").Order("sub_mer_split_settlement_logs.created_at desc").Offset(offset).Find(&data).Error
	if err != nil {
		return
	}

	return err, data, total
}

type SplitShareRecordResponse struct {
	model.SplitShareRecord
	SmallShopOrder smallShopModel.SmallShopOrder `json:"small_shop_order" gorm:"foreignKey:order_sn;references:order_sn"`
}

func (SplitShareRecordResponse) TableName() string {
	return "split_share_records"
}
func GetSplitShareRecordList(info request.SplitShareRecordSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB().Model(&SplitShareRecordResponse{}).Preload("SplitShareRecordAmount").Preload("SmallShopOrder")
	var data []SplitShareRecordResponse
	// 应用条件

	if info.PaymentStoreCode != "" {
		db = db.Where("split_share_records.payment_store_code = ?", info.PaymentStoreCode)
	}
	if info.RequestPayTypeName != "" {
		db = db.Where("split_share_records.pay_type_name LIKE ?", "%"+info.RequestPayTypeName+"%")

	}
	if info.MerRegName != "" {
		db = db.Where("split_share_records.mer_reg_name LIKE ?", "%"+info.MerRegName+"%")
	}
	if info.OrderSN != 0 {
		db = db.Where("split_share_records.order_sn = ?", info.OrderSN)
	}
	if info.OrderStatus != 0 {
		db = db.Joins("INNER join small_shop_orders on small_shop_orders.order_sn = split_share_records.order_sn").Where("small_shop_orders.status = ?", info.OrderStatus)
	}

	if info.PaySN != 0 {
		db = db.Where("split_share_records.pay_sn = ?", info.PaySN)
	}
	if info.PayTypeID != 0 {
		db = db.Where("split_share_records.pay_type_id = ?", info.PayTypeID)

	}
	if info.ChannelType != 0 {
		db = db.Where("split_share_records.channel_type = ?", info.ChannelType)
	}
	if info.SplitStatus != nil {
		db = db.Where("split_share_records.split_status = ?", info.SplitStatus)
	}
	if info.StartAT != "" {
		db = db.Where("split_share_records.created_at >= ?", info.StartAT)
	}
	if info.EndAT != "" {
		db = db.Where("split_share_records.created_at <= ?", info.EndAT)
	}

	// 统计总数
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	// 查询结果
	err = db.Limit(limit).Select("split_share_records.*").Order("split_share_records.created_at desc").Offset(offset).Find(&data).Error
	if err != nil {
		return
	}

	return err, data, total
}

func GetSplitShareRecordByID(id uint) (err error, list interface{}) {

	db := source.DB().Model(&SplitShareRecordResponse{}).Preload("SplitShareRecordAmount").Preload("SmallShopOrder")
	var data SplitShareRecordResponse

	// 查询结果
	err = db.Where("id = ?", id).First(&data).Error
	if err != nil {
		return
	}

	return err, data
}

// 绑定或者解绑分账方
func BindSubMerSplitSettlement(data common.BindOrUnbindSubMerSplitSettlement) (err error) {
	err = validateBindSubMerSplitSettlement(data)
	if err != nil {
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}
	_, err = rd.BindOrUnbindSubMerSplitSettlement(data)
	if err != nil {
		return
	}
	var logEntry = &model.BindOrUnbindSubMerSplitSettlementLog{
		BindOrUnbindSubMerSplitSettlement: data,
	}
	err = source.DB().Create(logEntry).Error
	if err != nil {
		err = errors.New("申请成功,记录创建失败,请联系客服" + err.Error())
		return
	}
	err = source.DB().Model(&model.CommunicationSubMer{}).Where("payment_store_code = ?", data.PaymentStoreCode).Updates(map[string]interface{}{"bind_or_unbind_sub_mer_split_settlement_log_id": logEntry.ID}).Error
	if err != nil {
		err = errors.New("申请成功,记录关联失败,请联系客服" + err.Error())
		return
	}
	return
}

func validateBindSubMerSplitSettlement(data common.BindOrUnbindSubMerSplitSettlement) error {

	if data.PaymentStoreCode == "" {
		return errors.New("聚合支付数据通商户号不能为空")
	}
	var subMerSplitSettlementLog model.BindOrUnbindSubMerSplitSettlementLog
	source.DB().Model(&model.BindOrUnbindSubMerSplitSettlementLog{}).Where("payment_store_code = ?", data.PaymentStoreCode).First(&subMerSplitSettlementLog)
	if subMerSplitSettlementLog.ID > 0 && subMerSplitSettlementLog.AuditStatus == 0 {
		return errors.New("存在待审核记录")
	}

	if data.Type == 1 && data.Remark == "" {
		return errors.New("解绑必传备注")
	}
	if data.EntrustFileName == "" {
		return errors.New("分账结算委托书文件名称不能为空")
	}

	if data.EntrustFile == "" {
		return errors.New("分账结算委托书文件不能为空")
	}

	//if data.EntrustFilePath == "" {
	//	return errors.New("分账结算委托书文件路径不能为空")
	//}

	return nil
}

func SynSubMerBindSplitSettlementStatusCron() (err error) {
	var bindOrUnbindSubMerSplitSettlementLog []model.BindOrUnbindSubMerSplitSettlementLog
	source.DB().Model(&model.BindOrUnbindSubMerSplitSettlementLog{}).Where("status = ?", 0).Find(&bindOrUnbindSubMerSplitSettlementLog)
	//不存在待审核记录直接跳过
	if len(bindOrUnbindSubMerSplitSettlementLog) == 0 {
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}
	var resData common.SubMerSplitBindStatusInfo
	for _, item := range bindOrUnbindSubMerSplitSettlementLog {
		resData, err = rd.GetSubMerSplitBind(item.PaymentStoreCode)
		if err != nil {
			log.Log().Error("聚合支付分账查询审核修改状态失败", zap.Any("err", err), zap.Any("item", item))
			continue
		}
		item.AuditStatus = resData.Status
		item.AuditStatusDesc = resData.StatusDesc
		item.AuditRemark = resData.Remark
		err = source.DB().Save(&item).Error
		if err != nil {
			err = errors.New("保存审核记录失败" + err.Error())
			return
		}
	}

	return
}

// 绑定或者解绑分账方
func BindSmallShop(data model.CommunicationSubMer) (err error) {
	if data.ID == 0 {
		err = errors.New("ID不能为空")
		return
	}
	if data.SmallShopId == 0 {
		err = errors.New("小店ID不能为空")
		return
	}
	err = source.DB().Model(&model.CommunicationSubMer{}).Where("small_shop_id = ?", data.SmallShopId).Updates(map[string]interface{}{"small_shop_id": 0}).Error
	if err != nil {
		err = errors.New("关联失败" + err.Error())
		return
	}
	err = source.DB().Model(&model.CommunicationSubMer{}).Where("id = ?", data.ID).Updates(map[string]interface{}{"small_shop_id": data.SmallShopId}).Error
	if err != nil {
		err = errors.New("关联失败" + err.Error())
		return
	}

	return
}

// 分账
func SplitAmt(rd common.RequestData, splitRequest common.SplitRequest, paymentStoreCode string) (err error) {

	var splitResponse common.SplitResponse
	splitResponse, err = rd.SplitAmt(splitRequest)
	if err != nil {
		source.DB().Model(&model.SplitShareRecordAmount{}).Where("payment_store_code = ?", paymentStoreCode).Updates(map[string]interface{}{"split_status": -1, "error_msg": err.Error()})
		return
	}
	// 分账状态: PROCESSING(处理中), ACCEPTED(已受理), SUCCESS(成功), FAIL(失败)
	var splitStatus = 1 //默认直接标记处理中 避免请求之后返回其他状态
	switch splitResponse.Status {
	//case "PROCESSING":
	//	splitStatus = 1
	//	break
	//case "ACCEPTED":
	//	splitStatus = 2
	//	break
	//case "SUCCESS":
	//	splitStatus = 3
	//	break
	case "FAIL":
		splitStatus = -1
		break
	}
	err = source.DB().Model(&model.SplitShareRecordAmount{}).Where("payment_store_code = ?", paymentStoreCode).Updates(map[string]interface{}{"split_status": splitStatus, "error_msg": splitResponse.StatusDesc}).Error
	if err != nil {
		log.Log().Error("聚合分账:请求分账保存返回信息失败", zap.Any("err", err), zap.Any("splitResponse", splitResponse), zap.Any("splitRequest", splitRequest))
		return
	}
	return
}

// 生成分账记录
func CreateSplitShareRecord(payInfoId uint, paymentStoreCode string, paySN uint) (err error) {

	var smallShopOrders []smallShopModel.SmallShopOrder
	err = source.DB().Model(&smallShopModel.SmallShopOrder{}).Where("small_shop_pay_info_id = ?", payInfoId).Find(&smallShopOrders).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		//log.Log().Error("查询订单报错,返回")
		log.Log().Error("查询订单报错", zap.Any("err", err))
		err = errors.New("查询订单报错" + err.Error())
		return
	}
	var communicationSubMerResponse CommunicationSubMerResponse
	err, communicationSubMerResponse = GetCommunicationSubMerResponseByPaymentStoreCode(paymentStoreCode)
	if err != nil {
		err = errors.New("聚合支付分账:获取子商户信息失败" + err.Error())
		return
	}
	var communicationMerchantSyncDataResponse CommunicationMerchantSyncDataResponse
	err, communicationMerchantSyncDataResponse = GetCommunicationPaymentInfoByPaymentStoreCode(communicationSubMerResponse.ParentStoreCode)
	if err != nil {
		err = errors.New("聚合支付分账:获取主商户信息失败" + err.Error())
		return
	}
	var total uint
	for _, order := range smallShopOrders {
		total += order.Amount
	}
	for _, smallShopOrder := range smallShopOrders {

		tx := source.DB().Begin()
		defer func() {
			if r := recover(); r != nil {
				tx.Rollback()
			}
		}()

		var splitShareRecordData model.SplitShareRecord
		err = source.DB().Model(&model.SplitShareRecord{}).Where("order_sn = ?", smallShopOrder.OrderSN).First(&splitShareRecordData).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			log.Log().Error("聚合分账查询主记录失败", zap.Any("err", err))
			err = errors.New("聚合分账查询主记录失败" + err.Error())
			continue
		}
		//已创建直接跳过
		if splitShareRecordData.ID > 0 {
			continue
		}
		//这里记录 之后分钱给平台
		var splitShareRecord model.SplitShareRecord
		splitShareRecord.Amount = smallShopOrder.Amount
		splitShareRecord.MerRegName = communicationSubMerResponse.CommunicationSubMerDetail.MerRegName
		splitShareRecord.MerchantNo = communicationSubMerResponse.MerchantNo
		splitShareRecord.OrderSN = smallShopOrder.OrderSN
		splitShareRecord.OrderID = smallShopOrder.ID
		splitShareRecord.PaySN = paySN
		splitShareRecord.PayInfoId = payInfoId
		splitShareRecord.PayTypeID = smallShopOrder.PayTypeID
		splitShareRecord.PayTypeName = paymentModel.GetPayTypeName(smallShopOrder.PayTypeID)
		splitShareRecord.Channel = communicationSubMerResponse.Channel
		splitShareRecord.ChannelType = communicationSubMerResponse.ChannelType
		splitShareRecord.TermNo = communicationSubMerResponse.TermNo
		splitShareRecord.SplitAmount = 0
		splitShareRecord.SplitStatus = -2
		splitShareRecord.PaymentStoreCode = paymentStoreCode

		err = source.DB().Model(&model.SplitShareRecord{}).Create(&splitShareRecord).Error
		if err != nil {
			tx.Rollback()
			log.Log().Error("保存分账记录报错", zap.Any("err", err))
			err = errors.New("保存分账记录报错" + err.Error())
			return
		}

		var splitShareRecordAmount model.SplitShareRecordAmount
		splitShareRecordAmount.MerchantNo = communicationMerchantSyncDataResponse.CommunicationMerchantDetail.MerInnerNo
		splitShareRecordAmount.PaymentStoreCode = communicationMerchantSyncDataResponse.PaymentStoreCode
		splitShareRecordAmount.OutSeparateNo = strconv.Itoa(int(splitShareRecord.OutSeparateNo)) + "-0"
		splitShareRecordAmount.Type = 0
		splitShareRecordAmount.SplitAmount = 0
		splitShareRecordAmount.OrderID = smallShopOrder.ID
		splitShareRecordAmount.SplitShareRecordId = splitShareRecord.ID
		splitShareRecordAmount.SplitStatus = 0
		splitShareRecord.SplitShareRecordAmount = append(splitShareRecord.SplitShareRecordAmount, splitShareRecordAmount)
		var splitShareRecordAmountSubMer model.SplitShareRecordAmount
		splitShareRecordAmountSubMer.MerchantNo = communicationSubMerResponse.CommunicationSubMerDetail.MerInnerNo
		splitShareRecordAmountSubMer.PaymentStoreCode = communicationSubMerResponse.PaymentStoreCode
		splitShareRecordAmountSubMer.Type = 1
		splitShareRecordAmountSubMer.OrderID = smallShopOrder.ID
		splitShareRecordAmountSubMer.OutSeparateNo = strconv.Itoa(int(splitShareRecord.OutSeparateNo)) + "-1"
		splitShareRecordAmountSubMer.SplitAmount = 0
		splitShareRecordAmountSubMer.SplitShareRecordId = splitShareRecord.ID
		splitShareRecordAmountSubMer.SplitStatus = 0
		splitShareRecord.SplitShareRecordAmount = append(splitShareRecord.SplitShareRecordAmount, splitShareRecordAmountSubMer)

		if err = tx.CreateInBatches(splitShareRecord.SplitShareRecordAmount, 2).Error; err != nil {
			tx.Rollback()
			log.Log().Error("保存子分账记录报错", zap.Any("err", err))
			err = errors.New("保存子分账记录报错" + err.Error())
			return err
		}
		tx.Commit()
	}
	return

}

// 获取分账金额
func GetSplitAmtQuery(paySN uint, rd common.RequestData) (err error, resTotalSeparateAmtInt uint) {
	var resData common.SplitAmountQuery

	//获取分账金额
	resData, err = rd.GetSplitAmtQuery(paySN)
	if err != nil {
		err = errors.New("聚合支付分账:获取分账金额失败" + err.Error())
		return
	}
	totalSeparateAmtFloat, err := strconv.ParseFloat(resData.TotalSeparateAmt, 64)
	if err != nil {
		err = errors.New("聚合支付分账:金额转换失败" + err.Error())
		return
	}
	canSeparateAmtAmtFloat, err := strconv.ParseFloat(resData.CanSeparateAmt, 64)
	if err != nil {
		err = errors.New("聚合支付分账:可分账金额转换失败" + err.Error())
		return
	}
	// 转换为整数（根据业务需求可选择四舍五入或直接截断）
	totalSeparateAmtInt := int(totalSeparateAmtFloat)
	//如果分账总额和可分账金额都是0则直接返回错误，这里分账使用总分账金额 通过中台同支付单号的订单计算怎么分账
	if totalSeparateAmtInt == 0 && canSeparateAmtAmtFloat == 0 {
		err = errors.New("聚合支付分账:获取金额失败,金额为0")
		return
	}

	resTotalSeparateAmtInt = uint(canSeparateAmtAmtFloat)
	return
}

// 分账
func RevenueSharing(id uint) (err error) {
	if id == 0 {
		err = errors.New("id必传")
		return
	}
	var splitShareRecord model.SplitShareRecord
	err = source.DB().Model(&model.SplitShareRecord{}).Preload("SplitShareRecordAmount").Where("id = ?", id).First(&splitShareRecord).Error
	if err != nil {
		err = errors.New("分账记录不存在" + err.Error())
		return
	}
	if splitShareRecord.SplitStatus == 3 {
		err = errors.New("分账已完成")
		return
	}
	err, rd := common.Initial()
	if err != nil {
		return
	}
	//如果没有获取金额进行分账这里先获取金额
	if splitShareRecord.IsSplit == 0 {
		err = AggregatedPaymentSplitSettlementStart(rd, splitShareRecord, 1)
		if err != nil {
			return
		}
		//分账金额计算完成之后重新获取一次
		err = source.DB().Model(&model.SplitShareRecord{}).Preload("SplitShareRecordAmount").Where("id = ?", id).First(&splitShareRecord).Error
		if err != nil {
			err = errors.New("分账记录不存在" + err.Error())
			return
		}
		if splitShareRecord.SplitStatus == 3 {
			return
		}
		if splitShareRecord.SplitAmount == 0 {
			err = errors.New("金额异常无法进行分账,或者分账金额为0")
			return
		}
	}
	err = RevenueSharingSplit(rd, splitShareRecord)
	if err != nil {
		return
	}
	splitShareRecordErr := source.DB().Model(&model.SplitShareRecord{}).Where("id = ?", splitShareRecord.ID).Updates(map[string]interface{}{"split_status": 1}).Error
	if splitShareRecordErr != nil {
		log.Log().Error("聚合分账保存平台分账完成记录失败", zap.Any("err", err))
	}
	return
}

// 手动分账开始进行分账
func RevenueSharingSplit(rd common.RequestData, splitShareRecord model.SplitShareRecord) (err error) {
	for _, item := range splitShareRecord.SplitShareRecordAmount {
		if (item.SplitStatus == 0 || item.SplitStatus == -1) && item.SettlementStatus == 1 {
			var splitRequest common.SplitRequest
			splitRequest.OrderCode = strconv.Itoa(int(splitShareRecord.PaySN))
			//加上-0代表是主商户的分账  -1是子商户的分账
			splitRequest.OutSeparateNo = item.OutSeparateNo
			splitRequest.TotalAmt = strconv.Itoa(int(item.SplitAmount))
			var recvData common.RecvData
			if item.Type == 1 {
				recvData.RecvStoreNo = item.PaymentStoreCode
			} else {
				recvData.RecvNo = item.PaymentStoreCode
			}
			recvData.SeparateValue = strconv.Itoa(int(item.SplitAmount))
			splitRequest.RecvDatas = append(splitRequest.RecvDatas, recvData)
			err = SplitAmt(rd, splitRequest, item.PaymentStoreCode)
			if err != nil {
				err = errors.New("聚合支付分账:分账失败" + err.Error())
				return
			}
		}
	}
	return
}

// isSplitApi 是否直接请求分账API 0是 1否
func AggregatedPaymentSplitSettlementStart(rd common.RequestData, splitShareRecord model.SplitShareRecord, isSplitApi int) (err error) {
	err, totalSeparateAmtIUint := GetSplitAmtQuery(splitShareRecord.PaySN, rd)
	//这里获取不到分账金额直接跳出
	if err != nil {
		return
	}
	var communicationSubMerResponse CommunicationSubMerResponse
	err, communicationSubMerResponse = GetCommunicationSubMerResponseByPaymentStoreCode(splitShareRecord.PaymentStoreCode)
	if err != nil {
		err = errors.New("聚合支付分账:获取子商户信息失败" + err.Error())
		return
	}
	var communicationMerchantSyncDataResponse CommunicationMerchantSyncDataResponse
	err, communicationMerchantSyncDataResponse = GetCommunicationPaymentInfoByPaymentStoreCode(communicationSubMerResponse.ParentStoreCode)
	if err != nil {
		err = errors.New("聚合支付分账:获取主商户信息失败" + err.Error())
		return
	}

	//总金额大于0 进行分账
	if totalSeparateAmtIUint > 0 {
		var smallShopOrders []smallShopModel.SmallShopOrder
		err = source.DB().Model(&smallShopModel.SmallShopOrder{}).Where("small_shop_pay_info_id = ?", splitShareRecord.PayInfoId).Find(&smallShopOrders).Error
		if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
			//log.Log().Error("查询订单报错,返回")
			err = errors.New("查询订单报错" + err.Error())
			return
		}
		var total uint
		for _, order := range smallShopOrders {
			total += order.Amount
		}
		for k, smallShopOrder := range smallShopOrders {
			var splitAmount uint

			//如果存在多个订单则进行按照百分比进行分账每单按照支付金额进行占比 如果是最后一个剩下的都是最后一单的
			if (k + 1) == len(smallShopOrders) {
				splitAmount = totalSeparateAmtIUint
			} else {
				result := float64(smallShopOrder.Amount) / float64(total)

				// 保留两位小数，乘以 100 然后四舍五入
				roundedResult := math.Round(result * 100)

				// 转换为 uint
				finalResult := uint(roundedResult)
				splitAmount = finalResult * totalSeparateAmtIUint / 100
			}
			//可能同一个支付记录多个订单 这里重新查询当前订单的分账记录
			var splitShareRecordData model.SplitShareRecord
			err = source.DB().Model(&model.SplitShareRecord{}).Where("order_sn = ?", smallShopOrder.OrderSN).First(&splitShareRecordData).Error
			if err != nil {
				log.Log().Error("聚合分账查询主记录失败", zap.Any("err", err))
				err = errors.New("聚合分账查询主记录失败" + err.Error())
				continue
			}
			totalSeparateAmtIUint = totalSeparateAmtIUint - splitAmount
			//原逻辑中与中台的订单就是一对一的
			var order shopOrder.Order
			err = source.DB().Model(&shopOrder.Order{}).Where("third_order_sn = ?", smallShopOrder.OrderSN).First(&order).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				//log.Log().Error("查询订单报错,返回")
				log.Log().Error("查询订单报错", zap.Any("err", err))
				err = errors.New("查询订单中台订单失败" + err.Error())
				continue
			}
			var smallShop smallShopModel.SmallShop
			err, smallShop = smallShopService.GetSmallShopByID(smallShopOrder.SmallShopID)
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				//log.Log().Error("查询小商店报错,返回")
				log.Log().Error("查询小商店报错", zap.Any("err", err))
				err = errors.New("查询小商店报错")

				return
			}
			var award smallShopModel.Award

			err = source.DB().Where("shop_order_id = ?", order.ID).First(&award).Error
			if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
				log.Log().Error("查询小商店奖励sql报错", zap.Any("err", err.Error()))
				err = errors.New("查询小商店奖励sql报错")
				return
			}
			//已产生奖励不再产生
			if award.ID != 0 {
			} else {
				err, award = smallShopService.AwardCreate(order, smallShopOrder, smallShop, splitAmount, 1)
			}
			if err != nil {
				continue
			}
			var supplyAmount uint //平台分到的金额
			supplyAmount = splitAmount - award.Amount

			tx := source.DB().Begin()
			defer func() {
				if r := recover(); r != nil {
					tx.Rollback()
				}
			}()

			var isCommon = 0 //如果主分账记录与子分账记录都是0则分账直接完成
			//平台分账信息保存
			var splitShareRecordAmountMap = map[string]interface{}{"split_amount": supplyAmount, "split_status": 1, "settlement_status": 1}
			if supplyAmount == 0 {
				splitShareRecordAmountMap["split_status"] = 3
				isCommon++
			}

			err = source.DB().Model(&model.SplitShareRecordAmount{}).Where("split_share_record_id = ? and type = 0", splitShareRecordData.ID).Updates(splitShareRecordAmountMap).Error
			if err != nil {
				tx.Rollback()
				log.Log().Error("保存主分账记录报错", zap.Any("err", err))
				err = errors.New("保存主分账记录报错" + err.Error())
				continue
			}
			var splitShareRecordAmountMapSub = map[string]interface{}{"split_amount": award.Amount, "split_status": 0}
			if award.Amount == 0 {
				splitShareRecordAmountMapSub["split_status"] = 3
				isCommon++
			}
			err = source.DB().Model(&model.SplitShareRecordAmount{}).Where("split_share_record_id = ? and type = 1", splitShareRecordData.ID).Updates(splitShareRecordAmountMapSub).Error
			if err != nil {
				tx.Rollback()
				log.Log().Error("保存子分账记录报错", zap.Any("err", err))
				err = errors.New("保存子分账记录报错" + err.Error())
				continue
			}
			var splitShareRecordMap = map[string]interface{}{"split_amount": splitAmount, "is_split": 1}
			switch isCommon {
			case 1:
				splitShareRecordMap["split_status"] = 2
				break
			case 2:
				splitShareRecordMap["split_status"] = 3
				splitShareRecordMap["error_msg"] = "分账金额为0"
				break
			default:
				splitShareRecordMap["split_status"] = 1
				break
			}
			//记录分账金额
			err = source.DB().Model(&model.SplitShareRecord{}).Where("id = ?", splitShareRecordData.ID).Updates(splitShareRecordMap).Error
			if err != nil {
				tx.Rollback()
				log.Log().Error("保存分账记录报错", zap.Any("err", err))
				err = errors.New("保存分账记录报错" + err.Error())
				continue
			}
			tx.Commit()
			//如果是自动执行 平台金额分得的不是0则直接分
			if isSplitApi == 0 && supplyAmount > 0 {
				var splitRequest common.SplitRequest
				splitRequest.OrderCode = strconv.Itoa(int(splitShareRecordData.PaySN))
				//加上-0代表是主商户的分账  -1是子商户的分账
				splitRequest.OutSeparateNo = strconv.Itoa(int(splitShareRecord.OutSeparateNo)) + "-0"
				splitRequest.TotalAmt = strconv.Itoa(int(supplyAmount))

				var recvData common.RecvData
				recvData.RecvNo = communicationMerchantSyncDataResponse.PaymentStoreCode
				recvData.SeparateValue = strconv.Itoa(int(supplyAmount))
				splitRequest.RecvDatas = append(splitRequest.RecvDatas, recvData)
				err = SplitAmt(rd, splitRequest, communicationMerchantSyncDataResponse.PaymentStoreCode)
				if err != nil {
					splitShareRecordErr := source.DB().Model(&model.SplitShareRecord{}).Where("id = ?", splitShareRecordData.ID).Updates(map[string]interface{}{"split_status": -1, "error_msg": err.Error()}).Error
					if splitShareRecordErr != nil {
						log.Log().Error("聚合分账保存分账错误记录失败", zap.Any("err", err))
					}
				} else {
					splitShareRecordErr := source.DB().Model(&model.SplitShareRecord{}).Where("id = ?", splitShareRecordData.ID).Updates(map[string]interface{}{"split_status": 2}).Error
					if splitShareRecordErr != nil {
						log.Log().Error("聚合分账保存平台分账完成记录失败", zap.Any("err", err))
					}
				}
			}

		}

	} else {
		err = source.DB().Model(&model.SplitShareRecord{}).Where("pay_sn = ?", splitShareRecord.PaySN).Updates(map[string]interface{}{"split_amount": 0, "split_status": 3, "error_msg": "分账金额为0"}).Error
		if err != nil {
			log.Log().Error("保存分账记录报错", zap.Any("err", err))
			err = errors.New("保存分账记录报错" + err.Error())
			return
		}
	}
	return
}
