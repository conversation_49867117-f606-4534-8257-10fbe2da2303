package setting

import (
	"database/sql/driver"
	"encoding/json"
	"yz-go/model"
	"yz-go/source"
)

type SysSetting struct {
	model.SysSetting
	Value Value `json:"value"`
}
type Value struct {
	IsOpen int `mapstructure:"isopen" json:"isopen" yaml:"isopen"` //状态1开始2关闭
}

var AggregatedPaymentSplitSettlementKey = "aggregated_payment_split_settlement"
var AggregatedPaymentSplitSettlementSetting *SysSetting

func (value Value) Value() (driver.Value, error) {
	return json.Marshal(value)
}
func (value *Value) Scan(data interface{}) error {
	return json.Unmarshal(data.([]byte), &value)
}

func GetAggregatedPaymentSplitSettlementSetting() (err error, sysSetting SysSetting) {
	if AggregatedPaymentSplitSettlementSetting == nil {
		var key = AggregatedPaymentSplitSettlementKey
		err = source.DB().Where("`key` = ?", key).First(&sysSetting).Error
		if err != nil {
			AggregatedPaymentSplitSettlementSetting = &sysSetting
		}
		return
	} else {
		sysSetting = *AggregatedPaymentSplitSettlementSetting
		return
	}

}

func SaveSysServiceProviderSystemSetting(data SysSetting) (err error) {
	data.Key = AggregatedPaymentSplitSettlementKey
	_, sysSetting := GetAggregatedPaymentSplitSettlementSetting()
	if sysSetting.ID != 0 {
		data.ID = sysSetting.ID
	}

	if data.ID != 0 {
		err = source.DB().Where("id = ?", data.ID).Updates(&data).Error
	} else {
		err = source.DB().Create(&data).Error
	}
	AggregatedPaymentSplitSettlementSetting = nil
	return err
}
