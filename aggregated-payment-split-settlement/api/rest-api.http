
#### 获取基础设置
GET {{api}}/aggregatedPaymentSplitSettlement/getSysAggregatedPaymentSplitSettlementSetting
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MDM5MTA1MiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzQ5Nzg1MjUyfQ.fvmXE8951dagWNQlFqRg8KIu1skbEL7S35pxEDaLkME
x-user-id: 1



#### 设置基础设置
POST {{api}}/aggregatedPaymentSplitSettlement/saveSysAggregatedPaymentSplitSettlementSetting
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MDM5MTA1MiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzQ5Nzg1MjUyfQ.fvmXE8951dagWNQlFqRg8KIu1skbEL7S35pxEDaLkME
x-user-id: 1

{
  "id": 120,
  "key": "aggregated_payment_split_settlement",
  "value": {
    "is_open": 2
  }
}


#### 获取基础设置
GET {{api}}/aggregatedPaymentSplitSettlement/getCommunicationPaymentInfoList?page=1&page_size=10
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MDM5MTA1MiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzQ5Nzg1MjUyfQ.fvmXE8951dagWNQlFqRg8KIu1skbEL7S35pxEDaLkME
x-user-id: 1


#### 获取基础设置
GET {{api}}/aggregatedPaymentSplitSettlement/getCommunicationPaymentInfoById?id=1
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MDM5MTA1MiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzQ5Nzg1MjUyfQ.fvmXE8951dagWNQlFqRg8KIu1skbEL7S35pxEDaLkME
x-user-id: 1


#### 设置基础设置
POST {{api}}/aggregatedPaymentSplitSettlement/synSubMer
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MTAxMzYxMiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzUwNDA3ODEyfQ.apc0gftDeTrN0eRNY-5lQ3wmHH5gJG5HHNdw_hfbw1A
x-user-id: 1

{

}


#### 获取基础设置
GET {{api}}/aggregatedPaymentSplitSettlement/getCommunicationSubMerList?page=1&page_size=10
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MDM5MTA1MiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzQ5Nzg1MjUyfQ.fvmXE8951dagWNQlFqRg8KIu1skbEL7S35pxEDaLkME
x-user-id: 1


#### 获取基础设置
GET {{api}}/aggregatedPaymentSplitSettlement/getCommunicationSubMerById?id=1
Content-Type: application/json
x-token:eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJVVUlEIjoiZDAyYWFmZTEtNDBhMS00MDFhLTg3ZjItMmFmMThlMTQ1NmVjIiwiSUQiOjEsIkFwcElEIjowLCJTaG9wSUQiOjAsIlVzZXJuYW1lIjoiYWRtaW4iLCJOaWNrTmFtZSI6Iui2hee6p-euoeeQhuWRmDY2NiIsIkF1dGhvcml0eUlkIjoiODg4IiwiQnVmZmVyVGltZSI6ODY0MDAsImV4cCI6MTc1MDM5MTA1MiwiaXNzIjoicW1QbHVzIiwibmJmIjoxNzQ5Nzg1MjUyfQ.fvmXE8951dagWNQlFqRg8KIu1skbEL7S35pxEDaLkME
x-user-id: 1



