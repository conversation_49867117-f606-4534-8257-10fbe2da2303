package admin

import (
	"aggregated-payment-split-settlement/request"
	"aggregated-payment-split-settlement/service"
	"aggregated-payment-split-settlement/setting"
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"service-provider-system/common"
	"service-provider-system/model"
	serviceProviderSystemRequest "service-provider-system/request"
	"yz-go/component/log"
	yzRequest "yz-go/request"
	yzResponse "yz-go/response"
)

// @Tags 服务商系统API
// @Summary 获取服务商系统配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "获取获取服务商系统配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/getSysServiceProviderSystemSetting [get]
func GetSysAggregatedPaymentSplitSettlementSetting(c *gin.Context) {
	var sys setting.SysSetting
	_ = c.ShouldBindJSON(&sys)

	err, sysServiceProviderSystemSetting := setting.GetAggregatedPaymentSplitSettlementSetting()

	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithData(sysServiceProviderSystemSetting, c)

}

// @Tags 服务商系统API
// @Summary 保存 服务商系统配置
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "保存获取服务商系统配置"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"成功"}"
// @Router /serviceProviderSystem/saveSysServiceProviderSystemSetting [get]
func SaveSysAggregatedPaymentSplitSettlementSetting(c *gin.Context) {
	var sys setting.SysSetting
	err := c.ShouldBindJSON(&sys)

	if err != nil {
		yzResponse.FailWithMessage("参数获取失败"+err.Error(), c)

		return
	}

	err = setting.SaveSysServiceProviderSystemSetting(sys)
	if err != nil {
		yzResponse.FailWithMessage("保存失败"+err.Error(), c)
		return
	}
	yzResponse.Ok(c)

}

func GetCommunicationPaymentInfoList(c *gin.Context) {
	var pageInfo request.CommunicationMerchantSyncDataSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCommunicationPaymentInfoList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
func GetCommunicationPaymentInfoAll(c *gin.Context) {

	if err, list := service.GetCommunicationPaymentInfoAll(); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(list, "获取成功", c)
	}
}

func GetCommunicationPaymentInfoById(c *gin.Context) {
	var pageInfo request.CommunicationMerchantSyncDataSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetCommunicationPaymentInfoById(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(list, "获取成功", c)
	}
}

func GetCommunicationPaymentInfoByPaymentStoreCode(c *gin.Context) {
	var pageInfo request.CommunicationMerchantSyncDataSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetCommunicationPaymentInfoByPaymentStoreCode(pageInfo.PaymentStoreCode); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(list, "获取成功", c)
	}
}

func GetCommunicationSubMerList(c *gin.Context) {
	var pageInfo request.CommunicationSubMerSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetCommunicationSubMerList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetCommunicationSubMerById(c *gin.Context) {
	var pageInfo request.CommunicationSubMerSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetCommunicationSubMerById(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(list, "获取成功", c)
	}
}

func SynSubMer(c *gin.Context) {
	err := service.SynSubMer()
	if err != nil {
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithMessage("同步中需要1-2分钟请勿重复点击", c)
}

func AddSubMer(c *gin.Context) {
	var pageInfo serviceProviderSystemRequest.AddSubMer
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("新增失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.AddSubMer(pageInfo); err != nil {
		log.Log().Error("新增失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("新增成功", c)
	}
}

func ApplySubMerSplitSettlement(c *gin.Context) {
	var pageInfo common.SubMerSplitSettlement
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.ApplySubMerSplitSettlement(pageInfo); err != nil {
		log.Log().Error("申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("申请成功，请等待审核", c)
	}
}

func GetSubMerSplitSettlementLogByPaymentStoreCode(c *gin.Context) {
	var pageInfo common.SubMerSplitSettlement
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, data := service.GetSubMerSplitSettlementLogByPaymentStoreCode(pageInfo.PaymentStoreCode); err != nil {
		log.Log().Error("申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithData(data, c)
	}
}

func SaveSubMerSplitSettlement(c *gin.Context) {
	var pageInfo common.SubMerSplitSettlement
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.SaveSubMerSplitSettlement(pageInfo); err != nil {
		log.Log().Error("申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("修改申请成功，请等待审核", c)
	}
}

func RevenueSharing(c *gin.Context) {
	var pageInfo yzRequest.GetById
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("手动分账失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.RevenueSharing(pageInfo.Id); err != nil {
		log.Log().Error("手动分账失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("手动分账成功，请等待进度", c)
	}
}
func GetSubMerSplitSettlementLogList(c *gin.Context) {
	var pageInfo request.SubMerSplitSettlementLogSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetSubMerSplitSettlementLogList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func BindSubMerSplitSettlement(c *gin.Context) {
	var pageInfo common.BindOrUnbindSubMerSplitSettlement
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("绑定或解绑申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.BindSubMerSplitSettlement(pageInfo); err != nil {
		log.Log().Error("绑定或解绑申请失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("绑定或解绑申请成功，请等待审核", c)
	}
}

func BindSmallShop(c *gin.Context) {
	var pageInfo model.CommunicationSubMer
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("绑定小商店失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err = service.BindSmallShop(pageInfo); err != nil {
		log.Log().Error("绑定小商店失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithMessage("绑定成功", c)
	}
}

func GetSplitShareRecordList(c *gin.Context) {
	var pageInfo request.SplitShareRecordSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list, total := service.GetSplitShareRecordList(pageInfo); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}

func GetSplitShareRecordByID(c *gin.Context) {
	var pageInfo request.SplitShareRecordSearch
	err := c.ShouldBindJSON(&pageInfo)
	if err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if err, list := service.GetSplitShareRecordByID(pageInfo.ID); err != nil {
		log.Log().Error("获取失败", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(list, "获取成功", c)
	}
}
