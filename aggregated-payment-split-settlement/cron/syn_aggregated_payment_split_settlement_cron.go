package cron

import (
	"aggregated-payment-split-settlement/service"
	"fmt"
	"go.uber.org/zap"
	"service-provider-system/common"
	serviceProviderSystemModel "service-provider-system/model"
	"yz-go/component/log"
	"yz-go/cron"
	"yz-go/source"
)

// 定时获取已支付待处理的进行获取分账金额进行分账 -- 聚合支付这里分账金额获取有五分钟延迟所以不能监听支付直接获取进行分账
func SynAggregatedPaymentSplitSettlementCron() {
	task := cron.Task{
		Key:  "AggregatedPaymentSplitSettlementCron",
		Name: "定时获取已支付待处理的进行获取分账金额进行分账",
		Spec: "0 0/5 * * * *",
		Handle: func(task cron.Task) {
			AggregatedPaymentSplitSettlement()
		},
		Status: cron.ENABLED,
	}
	cron.PushTask(task)
}

func AggregatedPaymentSplitSettlement() {
	fmt.Println("聚合分账定时任务开始:")
	//查询需要分账的记录 -- -2代表请求API失败, pay_info_id分组是因为可能一个支付记录支付多个订单
	var splitShareRecordData []serviceProviderSystemModel.SplitShareRecord
	source.DB().Model(&serviceProviderSystemModel.SplitShareRecord{}).Where("split_status = ?", -2).Find(&splitShareRecordData)
	if len(splitShareRecordData) > 0 {
		err, rd := common.Initial()
		if err != nil {
			return
		}
		//进行获取分账金额进行分账
		for _, v := range splitShareRecordData {
			err = service.AggregatedPaymentSplitSettlementStart(rd, v, 0)
			//标记一下，错误的不改状态下次继续请求分账
			if err != nil {
				log.Log().Error("聚合分账:分账错误", zap.Any("err", err))
			}
		}
	}
}
