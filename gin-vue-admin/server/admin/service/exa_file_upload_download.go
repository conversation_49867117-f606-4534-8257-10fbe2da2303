package service

import (
	"errors"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/admin/model/request"
	"mime/multipart"
	"os"
	"path"
	"strings"
	"yz-go/component/upload"
	"yz-go/source"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: Upload
//@description: 创建文件上传记录
//@param: file model.ExaFileUploadAndDownload
//@return: error

func Upload(file model.ExaFileUploadAndDownload) error {
	return source.DB().Create(&file).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: FindFile
//@description: 删除文件切片记录
//@param: id uint
//@return: error, model.ExaFileUploadAndDownload

func FindFile(id uint) (error, model.ExaFileUploadAndDownload) {
	var file model.ExaFileUploadAndDownload
	err := source.DB().Where("id = ?", id).First(&file).Error
	return err, file
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteFile
//@description: 删除文件记录
//@param: file model.ExaFileUploadAndDownload
//@return: err error

func DeleteFile(file model.ExaFileUploadAndDownload) (err error) {
	var fileFromDb model.ExaFileUploadAndDownload
	err, fileFromDb = FindFile(file.ID)
	oss := upload.NewOss()
	if err = oss.DeleteFile(fileFromDb.Key); err != nil {
		return errors.New("文件删除失败")
	}
	err = source.DB().Where("id = ?", file.ID).Unscoped().Delete(&file).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetFileRecordInfoList
//@description: 分页获取数据
//@param: info yzRequest.PageInfo
//@return: err error, list interface{}, total int64

func GetFileRecordInfoList(info request.ExaFileSearch) (err error, list interface{}, total int64) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := source.DB()
	var fileLists []model.ExaFileUploadAndDownload
	if info.GroupId != 0 {
		db = db.Where("`group_id` = ?", info.GroupId)
	}
	if info.Uid != 0 {
		db = db.Where("`uid` = ?", info.Uid)
	}
	err = db.Find(&fileLists).Count(&total).Error
	err = db.Limit(limit).Offset(offset).Order("updated_at desc").Find(&fileLists).Error
	return err, fileLists, total
}

func UploadLocalFile(header *multipart.FileHeader, noSave string, fileData map[string]uint, dir string) (err error, file model.ExaFileUploadAndDownload) {
	local := &upload.Local{Dir: dir}
	filePath, key, uploadErr := local.UploadFile(header)
	if uploadErr != nil {
		panic(err)
	}
	if noSave == "0" {
		s := strings.Split(header.Filename, ".")
		f := model.ExaFileUploadAndDownload{
			Url:     filePath,
			Name:    header.Filename,
			Tag:     s[len(s)-1],
			Key:     key,
			Uid:     fileData["uid"],
			GroupId: fileData["groupId"],
		}
		return Upload(f), f
	}
	return
}
func UploadLocalFileToOss(fullPath string) (err error, url string, ossFileName string) {
	fileName := path.Base(fullPath)
	file, err := os.Open(fullPath)
	if err != nil {
		file, err = os.Open("/data/goSupply" + fullPath)
		if err != nil {
			return err, "", ""
		}
	}
	oss := upload.NewOss()

	url, ossFileName, err = oss.UploadLocalFile(fileName, file)
	if err != nil {
		return err, "", ""
	}
	return nil, url, ossFileName
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: UploadFile
//@description: 根据配置文件判断是文件上传到本地或者七牛云
//@param: header *multipart.FileHeader, noSave string
//@return: err error, file model.ExaFileUploadAndDownload

func UploadFile(header *multipart.FileHeader, noSave string, fileData map[string]uint) (err error, file model.ExaFileUploadAndDownload) {
	oss := upload.NewOss()
	header.Filename = strings.ReplaceAll(header.Filename, "+", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "%", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "=", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "-", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "*", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "&", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "^", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "$", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "#", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "@", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "!", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "?", "_")
	header.Filename = strings.ReplaceAll(header.Filename, ">", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "<", "_")
	header.Filename = strings.ReplaceAll(header.Filename, ";", "_")
	header.Filename = strings.ReplaceAll(header.Filename, ":", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "'", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "\"", "_")
	ext := path.Ext(header.Filename)
	if ext == ".pdf" {
		return errors.New("不支持上传pdf格式的文件"), file

	}
	filePath, key, uploadErr := oss.UploadFile(header)
	if uploadErr != nil {
		return uploadErr, file
	}
	if noSave == "0" {
		s := strings.Split(header.Filename, ".")
		f := model.ExaFileUploadAndDownload{
			Url:     filePath,
			Name:    header.Filename,
			Tag:     s[len(s)-1],
			Key:     key,
			Uid:     fileData["uid"],
			GroupId: fileData["groupId"],
		}
		return Upload(f), f
	}
	return
}

// @author: [piexlmax](https://github.com/piexlmax)
// @function: UploadFile
// @description: 根据配置文件判断是文件上传到本地或者七牛云
// @param: header *multipart.FileHeader, noSave string
// @return: err error, file model.ExaFileUploadAndDownload
// 不限制上传格式
func UploadFileUnrestricted(header *multipart.FileHeader, noSave string, fileData map[string]uint) (err error, file model.ExaFileUploadAndDownload) {
	oss := upload.NewOss()
	header.Filename = strings.ReplaceAll(header.Filename, "+", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "%", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "=", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "-", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "*", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "&", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "^", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "$", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "#", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "@", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "!", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "?", "_")
	header.Filename = strings.ReplaceAll(header.Filename, ">", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "<", "_")
	header.Filename = strings.ReplaceAll(header.Filename, ";", "_")
	header.Filename = strings.ReplaceAll(header.Filename, ":", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "'", "_")
	header.Filename = strings.ReplaceAll(header.Filename, "\"", "_")
	//ext := path.Ext(header.Filename)
	//if ext == ".pdf" {
	//	return errors.New("不支持上传pdf格式的文件"), file
	//
	//}
	filePath, key, uploadErr := oss.UploadFile(header)
	if uploadErr != nil {
		return uploadErr, file
	}
	if noSave == "0" {
		s := strings.Split(header.Filename, ".")
		f := model.ExaFileUploadAndDownload{
			Url:     filePath,
			Name:    header.Filename,
			Tag:     s[len(s)-1],
			Key:     key,
			Uid:     fileData["uid"],
			GroupId: fileData["groupId"],
		}
		return Upload(f), f
	}
	return
}
