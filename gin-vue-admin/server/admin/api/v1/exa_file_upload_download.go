package v1

import (
	"fmt"
	"gin-vue-admin/admin/model"
	"gin-vue-admin/admin/model/request"
	"gin-vue-admin/admin/model/response"
	"gin-vue-admin/admin/service"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"strconv"
	"yz-go/component/log"
	"yz-go/config"
	yzResponse "yz-go/response"
)

// @Tags ExaFileUploadAndDownload
// @Summary 上传文件示例
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "上传文件示例"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"上传成功"}"
// @Router /fileUploadAndDownload/upload [post]
func UploadFile(c *gin.Context) {
	var file model.ExaFileUploadAndDownload
	noSave := c.DefaultQuery("noSave", "0")
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Log().Error("接收文件失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("接收文件失败", c)
		return
	}

	// 如果配置中没有设置或设置为0，使用默认值10MB
	maxSize := config.Config().AttachmentType.UploadMaxSize
	if maxSize >= 0 {
		maxSize = 20 * 1024 * 1024
	} else {
		maxSize = maxSize * 1024 * 1024
	}

	if header.Size > maxSize {
		// 文件大小超过限制
		maxSizeMB := float64(maxSize) / 1024 / 1024
		fileSizeMB := float64(header.Size) / 1024 / 1024
		errMsg := fmt.Sprintf("文件大小超过限制，最大允许%.2fMB，当前文件大小%.2fMB", maxSizeMB, fileSizeMB)
		log.Log().Error(errMsg)
		yzResponse.FailWithMessage(errMsg, c)
		return
	}

	UserID := GetUserID(c)
	GroupId := c.Request.FormValue("groupId")
	i, _ := strconv.Atoi(GroupId)
	fileData := map[string]uint{"uid": UserID, "groupId": uint(i)}
	err, file = service.UploadFile(header, noSave, fileData) // 文件上传后拿到文件路径
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if c.Request.FormValue("index") != "" {
		file.Index, err = strconv.Atoi(c.Request.FormValue("index"))
	}
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(response.ExaFileResponse{File: file}, "上传成功", c)
}

// @Tags ExaFileUploadAndDownload
// @Summary 上传文件示例
// @Security ApiKeyAuth
// @accept multipart/form-data
// @Produce  application/json
// @Param file formData file true "上传文件示例"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"上传成功"}"
// @Router /fileUploadAndDownload/upload [post]
func UploadFileUnrestricted(c *gin.Context) {
	var file model.ExaFileUploadAndDownload
	noSave := c.DefaultQuery("noSave", "0")
	_, header, err := c.Request.FormFile("file")
	if err != nil {
		log.Log().Error("接收文件失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("接收文件失败", c)
		return
	}

	// 如果配置中没有设置或设置为0，使用默认值10MB
	maxSize := config.Config().AttachmentType.UploadMaxSize
	if maxSize >= 0 {
		maxSize = 20 * 1024 * 1024
	} else {
		maxSize = maxSize * 1024 * 1024
	}

	if header.Size > maxSize {
		// 文件大小超过限制
		maxSizeMB := float64(maxSize) / 1024 / 1024
		fileSizeMB := float64(header.Size) / 1024 / 1024
		errMsg := fmt.Sprintf("文件大小超过限制，最大允许%.2fMB，当前文件大小%.2fMB", maxSizeMB, fileSizeMB)
		log.Log().Error(errMsg)
		yzResponse.FailWithMessage(errMsg, c)
		return
	}

	UserID := GetUserID(c)
	GroupId := c.Request.FormValue("groupId")
	i, _ := strconv.Atoi(GroupId)
	fileData := map[string]uint{"uid": UserID, "groupId": uint(i)}
	err, file = service.UploadFileUnrestricted(header, noSave, fileData) // 文件上传后拿到文件路径
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	if c.Request.FormValue("index") != "" {
		file.Index, err = strconv.Atoi(c.Request.FormValue("index"))
	}
	if err != nil {
		log.Log().Error(err.Error(), zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	}
	yzResponse.OkWithDetailed(response.ExaFileResponse{File: file}, "上传成功", c)
}

// @Tags ExaFileUploadAndDownload
// @Summary 删除文件
// @Security ApiKeyAuth
// @Produce  application/json
// @Param data body model.ExaFileUploadAndDownload true "传入文件里面id即可"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /fileUploadAndDownload/deleteFile [post]
func DeleteFile(c *gin.Context) {
	var file model.ExaFileUploadAndDownload
	_ = c.ShouldBindJSON(&file)
	if err := service.DeleteFile(file); err != nil {
		log.Log().Error("删除失败!", zap.Any("err", err))
		yzResponse.FailWithMessage("删除失败", c)
		return
	}
	yzResponse.OkWithMessage("删除成功", c)
}

// @Tags ExaFileUploadAndDownload
// @Summary 分页文件列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.ExaFileSearch true "页码, 每页大小"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /fileUploadAndDownload/getFileList [post]
func GetFileList(c *gin.Context) {

	var pageInfo request.ExaFileSearch
	_ = c.ShouldBindJSON(&pageInfo)
	UserID := GetUserID(c)
	pageInfo.Uid = UserID
	err, list, total := service.GetFileRecordInfoList(pageInfo)
	if err != nil {
		log.Log().Error("获取失败!", zap.Any("err", err))
		yzResponse.FailWithMessage(err.Error(), c)
		return
	} else {
		yzResponse.OkWithDetailed(yzResponse.PageResult{
			List:     list,
			Total:    total,
			Page:     pageInfo.Page,
			PageSize: pageInfo.PageSize,
		}, "获取成功", c)
	}
}
