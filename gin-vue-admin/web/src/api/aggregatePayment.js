import service from '@/utils/request';

// 获取基础设置
export const getAggregateSetting = () => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getSysAggregatedPaymentSplitSettlementSetting',
        method: 'get',
    });
};

// 保存基础设置
export const saveAggregateSetting = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/saveSysAggregatedPaymentSplitSettlementSetting',
        method: 'post',
        data,
    });
};

// 获取主商户列表
export const getCommunicationPaymentInfoList = (params) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getCommunicationPaymentInfoList',
        method: 'get',
        params,
    });
};

// 通过id获取主商户详情
export const getCommunicationPaymentInfoById = (params) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getCommunicationPaymentInfoById',
        method: 'get',
        params,
    });
};

// 获取子商户列表
export const getCommunicationSubMerList = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getCommunicationSubMerList',
        method: 'post',
        data,
    });
};

// 同步子商户
export const synSubMer = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/synSubMer',
        method: 'post',
        data,
    });
};

// 修增子商户
export const addSubMer = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/addSubMer',
        method: 'post',
        data,
    });
};

// 获取支持分账的所有主商户
export const getCommunicationPaymentInfoAll = (params) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getCommunicationPaymentInfoAll',
        method: 'get',
        params,
    });
};

// 获取全部小商店
export const getAllSmallShop = (params) => {
    return service({
        url: '/smallShop/getAllSmallShop',
        method: 'get',
        params,
    });
};

// 绑定小商店
export const bindSmallShop = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/bindSmallShop',
        method: 'post',
        data,
    });
};

// 通过id获取子商户详情
export const getCommunicationSubMerById = (params) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getCommunicationSubMerById',
        method: 'get',
        params,
    });
};

// 申请开通分账
export const applySubMerSplitSettlement = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/applySubMerSplitSettlement',
        method: 'post',
        data,
    });
};

// 修改开通分账
export const saveSubMerSplitSettlement = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/saveSubMerSplitSettlement',
        method: 'post',
        data,
    });
};

// 绑定解绑分账方
export const bindSubMerSplitSettlement = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/bindSubMerSplitSettlement',
        method: 'post',
        data,
    });
};

// 通过子商户获取主商户的信息
export const getCommunicationPaymentInfoByPaymentStoreCode = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getCommunicationPaymentInfoByPaymentStoreCode',
        method: 'post',
        data,
    });
};

// 分账记录列表
export const getSplitShareRecordList = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getSplitShareRecordList',
        method: 'post',
        data,
    });
};

// 手动分账
export const revenueSharing = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/revenueSharing',
        method: 'post',
        data,
    });
};

// 通过id查询分账记录详情
export const getSplitShareRecordByID = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getSplitShareRecordByID',
        method: 'post',
        data,
    });
};

// 分账开户审核列表
export const getSubMerSplitSettlementLogList = (data) => {
    return service({
        url: '/aggregatedPaymentSplitSettlement/getSubMerSplitSettlementLogList',
        method: 'post',
        data,
    });
};