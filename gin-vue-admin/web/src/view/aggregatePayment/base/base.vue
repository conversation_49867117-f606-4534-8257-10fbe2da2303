<template>
    <m-card>
        <el-form v-model="formData" label-width="130px">
            <el-form-item label="聚合支付分账:">
                <el-radio-group v-model="formData.isopen">
                    <el-radio :label="1">开启</el-radio>
                    <el-radio :label="2">关闭</el-radio>
                </el-radio-group>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="save">保存</el-button>
            </el-form-item>
        </el-form>
    </m-card>
</template>

<script>
import {
    getAggregateSetting,
    saveAggregateSetting,
} from '@/api/aggregatePayment';

export default {
    name: 'base',
    data() {
        return {
            formData: {
                isopen: 2,
            },
            id: null,
            key: '',
        };
    },
    mounted() {
        this.init();
    },
    methods: {
        async init() {
            const res = await getAggregateSetting();
            if (res.code === 0) {
                this.id = res.data.id;
                this.key = res.data.key;
                this.formData.isopen = res.data.value.isopen;
            }
        },
        async save() {
            const data = {
                id: this.id,
                key: this.key,
                value: {
                    isopen: parseInt(this.formData.isopen),
                },
            };
            const res = await saveAggregateSetting(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.init();
            }
        },
    },
};
</script>

<style lang="scss" scoped>
</style>