<template>
    <m-card>
        <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
            <el-tab-pane label="基础设置" name="1">
                <Base ref="base"></Base>
            </el-tab-pane>
        </el-tabs>
    </m-card>
</template>

<script>
import Base from './base';
export default {
    name: 'baseIndex',
    components: {
        Base,
    },
    data() {
        return {
            activeName: '1',
        };
    },
    methods: {
        handleClick(tab, event) {
            let name = '';
            switch (this.activeName) {
                case '1':
                    name = 'base';
                    break;
            }
            this.$refs[name].init();
        },
    },
};
</script>

<style>
</style>