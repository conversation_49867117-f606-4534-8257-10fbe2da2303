<template>
    <m-card>
        <el-form :model="searchInfo" label-width="90px" class="search-term mt_20" inline>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.order_sn"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">订单号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.pay_sn"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">支付单号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>订单状态</span>
                    </div>
                    <el-select v-model="searchInfo.order_status" class="w100" clearable>
                        <el-option :value="1" label="待发货"></el-option>
                        <el-option :value="2" label="待收货"></el-option>
                        <el-option :value="3" label="已完成"></el-option>
                        <el-option :value="-1" label="已关闭"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>支付类型</span>
                    </div>
                    <el-select v-model="searchInfo.request_pay_type_name" class="w100" clearable>
                        <el-option value="微信" label="微信"></el-option>
                        <el-option value="支付宝" label="支付宝"></el-option>
                        <el-option value="拉卡拉" label="拉卡拉收银台"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>所属通道</span>
                    </div>
                    <el-select v-model="searchInfo.channel_type" class="w100" clearable>
                        <el-option :value="1" label="拉卡拉"></el-option>
                        <el-option :value="2" label="汇付"></el-option>
                        <el-option :value="5" label="汇聚"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>分账状态</span>
                    </div>
                    <el-select v-model="searchInfo.split_status" class="w100" clearable>
                        <el-option :value="1" label="处理中"></el-option>
                        <el-option :value="2" label="已受理"></el-option>
                        <el-option :value="3" label="成功"></el-option>
                        <el-option :value="-1" label="失败"></el-option>
                        <el-option :value="-2" label="请求异常"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input" style="width: 500px">
                    <div class="line-box">
                        <span>创建时间</span>
                    </div>
                    <m-daterange v-model="orderDate"></m-daterange>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="dataTable">
            <el-table-column label="创建时间" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.created_at | formatDate }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="170px">
                <template slot="header">
                    <p>商户注册名称</p>
                    <p>商户号</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.mer_reg_name">{{ scope.row.mer_reg_name }}</p>
                    <p v-else>--</p>
                    <p>{{ scope.row.merchant_no }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>商户订单号</p>
                    <p>支付交易流水号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.order_sn }}</p>
                    <p>{{ scope.row.pay_sn }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>支付方式</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.pay_type_name }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>所属通道</p>
                    <p>终端号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.channel }}</p>
                    <p v-if="scope.row.term_no != ''">{{ scope.row.term_no }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>交易状态</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.small_shop_order.status === 1">待发货</p>
                    <p v-if="scope.row.small_shop_order.status === 2">待收货</p>
                    <p v-if="scope.row.small_shop_order.status === 3">已完成</p>
                    <p v-if="scope.row.small_shop_order.status === -1">已关闭</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>支付金额</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.amount |formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>平台方分账金额</p>
                    <p>商家结算金额</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.split_share_record_amount[0].split_amount | formatF2Y }}</p>
                    <p>{{ scope.row.split_share_record_amount[1].split_amount | formatF2Y }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>分账状态</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.split_status === 0">待处理</p>
                    <p v-if="scope.row.split_status === 1">处理中</p>
                    <p v-if="scope.row.split_status === 2">部分处理</p>
                    <p v-if="scope.row.split_status === 3">处理完成</p>
                    <p v-if="scope.row.split_status === -1">处理失败</p>
                    <p v-if="scope.row.split_status === -2">请求异常</p>
                    <el-popover placement="bottom" trigger="hover" :content="scope.row.error_msg">
                        <el-button v-if="scope.row.error_msg" type="text" slot="reference">信息说明</el-button>
                    </el-popover>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        v-if="scope.row.split_status === -1 || scope.row.split_status === -2"
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="handSplit(scope.row)"
                    >分账</el-button>
                    <el-button
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="jumpDetail(scope.row)"
                    >详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
          display: 'flex',
          justifyContent: 'flex-end',
          marginRight: '20px',
        }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <detailDrawer ref="detailDrawer"></detailDrawer>
    </m-card>
</template>

<script>
import mDaterange from '@/components/mDate/daterange';
import {
    getSplitShareRecordList,
    revenueSharing,
} from '@/api/aggregatePayment';
import detailDrawer from './detailDrawer.vue';
import { confirm } from '@/decorators/decorators';
export default {
    name: 'splitRecordIndex',
    components: { mDaterange, detailDrawer },
    data() {
        return {
            searchInfo: {
                order_sn: null, // 订单号
                pay_sn: null, // 支付单号
                order_status: null, // 订单状态
                request_pay_type_name: '', // 支付类型
                channel_type: null, // 所属通道
                split_status: null, // 分账状态
                start_at: '',
                end_at: '',
            },
            orderDate: [],
            dataTable: [],
            page: 1,
            pageSize: 10,
            total: null,
        };
    },
    mounted() {
        this.getSplitShareRecordList();
    },
    methods: {
        // 获取分账记录列表
        async getSplitShareRecordList() {
            const data = {
                ...this.searchInfo,
                payment_store_code: this.$route.query.payment_store_code
                    ? this.$route.query.payment_store_code
                    : '',
                order_sn: parseInt(this.searchInfo.order_sn),
                pay_sn: parseInt(this.searchInfo.pay_sn),
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await getSplitShareRecordList(data);
            if (res.code === 0) {
                this.dataTable = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        async search() {
            if (this.orderDate) {
                this.searchInfo.start_at =
                    this.orderDate.length > 0 ? this.orderDate[0] : '';
                this.searchInfo.end_at =
                    this.orderDate.length > 0 ? this.orderDate[1] : '';
            } else {
                this.searchInfo.start_at = '';
                this.searchInfo.end_at = '';
            }
            this.getSplitShareRecordList();
        },
        // 重置搜索条件
        reSearch() {
            this.searchInfo = {
                order_sn: null, // 订单号
                pay_sn: null, // 支付单号
                order_status: null, // 订单状态
                request_pay_type_name: '', // 支付类型
                channel_type: null, // 所属通道
                split_status: null, // 分账状态
                start_at: '',
                end_at: '',
            };
            this.orderDate = [];
        },
        // 手动分账
        @confirm('提示', '手动发起分账请求?')
        async handSplit(item) {
            const data = {
                id: parseInt(item.id),
            };
            const res = await revenueSharing(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getSplitShareRecordList();
            }
        },
        // 跳转详情
        jumpDetail(item) {
            this.$refs.detailDrawer.drawer = true;
            this.$refs.detailDrawer.id = item.id;
            this.$refs.detailDrawer.getSplitShareRecordByID();
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getSplitShareRecordList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getSplitShareRecordList();
        },
    },
};
</script>

<style>
</style>