<template>
    <m-card>
        <el-drawer title="详情" :visible.sync="drawer" direction="rtl" size="80%">
            <div class="detail-box">
                <div class="detail-title f fac">
                    <div class="detail-title-box"></div>
                    <div>分账信息</div>
                </div>
                <div class="detail-msg">
                    <div class="mt30">
                        分账状态：
                        <span v-if="detail.split_status === 0">处理中</span>
                        <span v-if="detail.split_status === 1">部分处理</span>
                        <span v-if="detail.split_status === 2">处理完成</span>
                        <span v-if="detail.split_status === -1">处理失败</span>
                        <span v-if="detail.split_status === -2">请求异常</span>
                    </div>
                    <div class="mt30">分账请求号：{{ detail.out_separate_no }}</div>
                    <div class="mt30">支付号：{{ detail.pay_sn }}</div>
                    <div class="mt30">订单号：{{ detail.order_sn }}</div>
                </div>
            </div>
            <div class="mt_20 detail-box">
                <div class="detail-title f fac">
                    <div class="detail-title-box"></div>
                    <div>分账方</div>
                </div>
                <el-table
                    class="ml_20"
                    style="width: 400px"
                    :data="detail.split_share_record_amount"
                >
                    <el-table-column align="center">
                        <template slot="header">
                            <p>商户号</p>
                        </template>
                        <template slot-scope="scope">
                            <p>{{ scope.row.merchant_no}}</p>
                        </template>
                    </el-table-column>
                    <el-table-column align="center">
                        <template slot="header">
                            <p>金额</p>
                        </template>
                        <template slot-scope="scope">
                            <p>{{ scope.row.split_amount |formatF2Y }}</p>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="mt_20 detail-box">
                <div class="detail-title f fac">
                    <div class="detail-title-box"></div>
                    <div>商家信息</div>
                </div>
                <div class="detail-msg">
                    <div>小商店ID：{{ detail.small_shop_order.id }}</div>
                    <div class="mt30">小商店名称：{{ detail.small_shop_order.title }}</div>
                </div>
            </div>
        </el-drawer>
    </m-card>
</template>

<script>
import { getSplitShareRecordByID } from '@/api/aggregatePayment';
export default {
    name: 'detailDrawer',
    data() {
        return {
            drawer: false,
            id: null,
            detail: {},
        };
    },
    methods: {
        async getSplitShareRecordByID() {
            const params = {
                id: parseInt(this.id),
            };
            const res = await getSplitShareRecordByID(params);
            if (res.code === 0) {
                this.detail = res.data;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer {
    background-color: rgba(240, 242, 245, 1);
}
::v-deep .el-drawer__header {
    background-color: #ffffff;
    padding-bottom: 20px;
    margin-bottom: 19px;
}
.detail-box {
    background-color: #ffffff;
    width: 1490px;
    padding-bottom: 33px;
    .detail-title {
        padding: 21px 24px;
        .detail-title-box {
            width: 4px;
            height: 16px;
            background-color: #155bd4;
            margin-right: 11px;
        }
    }
    .detail-msg {
        padding-left: 24px;
    }
}
.mt30 {
    margin-top: 30px;
}
</style>