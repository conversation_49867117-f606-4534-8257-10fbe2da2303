<template>
    <m-card>
        <el-button type="primary" @click="addSubMer">新增</el-button>
        <el-button type="primary" @click="synSubMer">同步</el-button>
        <el-form :model="searchInfo" label-width="90px" class="search-term mt_20" inline>
            <el-form-item label prop="user_id">
                <el-input
                    v-model="searchInfo[goodsCommand]"
                    placeholder="请输入"
                    clearable
                    class="line-input-width"
                >
                    <template slot="prepend">
                        <el-dropdown class="el-dropdown-row" @command="handleCommand">
                            <span class="el-dropdown-link">
                                {{returnGoodsValue(goodsCommand)}}
                                <i
                                    class="el-icon-arrow-down el-icon--right"
                                ></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="user_id">会员ID</el-dropdown-item>
                                <el-dropdown-item command="tel">手机号</el-dropdown-item>
                                <el-dropdown-item command="nick_name">会员昵称</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item label prop="merchant_no">
                <el-input
                    v-model="searchInfo[shopCommand]"
                    placeholder="请输入"
                    clearable
                    class="line-input-width"
                >
                    <template slot="prepend">
                        <el-dropdown class="el-dropdown-row" @command="handleShopCommand">
                            <span class="el-dropdown-link">
                                {{returnShopValue(shopCommand)}}
                                <i
                                    class="el-icon-arrow-down el-icon--right"
                                ></i>
                            </span>
                            <el-dropdown-menu slot="dropdown">
                                <el-dropdown-item command="merchant_no">内部商户号</el-dropdown-item>
                                <el-dropdown-item command="mer_reg_name">商户注册名称</el-dropdown-item>
                                <el-dropdown-item command="mer_opt_name">商户经营名称</el-dropdown-item>
                            </el-dropdown-menu>
                        </el-dropdown>
                    </template>
                </el-input>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>绑定状态</span>
                    </div>
                    <el-select v-model="searchInfo.separate_bind_status" class="w100" clearable>
                        <el-option :value="1" label="已绑定"></el-option>
                        <el-option :value="0" label="未绑定"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input" style="width: 500px">
                    <div class="line-box">
                        <span>开通时间</span>
                    </div>
                    <m-daterange v-model="orderDate"></m-daterange>
                </div>
            </el-form-item>
            <br />
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="dataTable">
            <el-table-column label="创健时间" align="center">
                <template slot-scope="scope">
                    <p
                        v-if="scope.row.communication_sub_mer_detail.create_time != ''"
                    >{{ scope.row.communication_sub_mer_detail.create_time }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="240px">
                <template slot="header">
                    <p>商户注册名称</p>
                    <p>商户经营名称</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.mer_reg_name">{{ scope.row.mer_reg_name }}</p>
                    <p v-else>--</p>
                    <p v-if="scope.row.mer_opt_name">{{ scope.row.mer_opt_name }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center" label="内部商户号">
                <template slot-scope="scope">
                    <p v-if="scope.row.merchant_no">{{ scope.row.merchant_no }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center" width="200px">
                <template slot="header">
                    <p>营业执照编码</p>
                    <p>行政区域</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.mer_blis">{{ scope.row.mer_blis }}</p>
                    <p v-else>--</p>
                    <p v-if="scope.row.area_id">{{ scope.row.area_id }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>商户联系人</p>
                    <p>电话</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.mer_contact_name }}</p>
                    <p>{{ scope.row.mer_contact_mobile }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>所属通道</p>
                    <p>终端号</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.channel_type == 1">拉卡拉</p>
                    <p v-if="scope.row.channel_type == 2">汇付支付</p>
                    <p v-if="scope.row.channel_type == 5">汇聚支付</p>
                    <p v-if="scope.row.term_no != ''">{{ scope.row.term_no }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>分账状态</p>
                    <p>平台分账绑定状态</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.apply_status == 0">关闭</p>
                    <p v-if="scope.row.apply_status == 1">开启</p>
                    <p v-if="scope.row.separate_bind_status == 0">未绑定</p>
                    <p v-if="scope.row.separate_bind_status == 1">绑定</p>
                </template>
            </el-table-column>
            <el-table-column label="绑定小商店" align="center">
                <template slot-scope="scope">
                    <el-select
                        v-model="scope.row.small_shop_id"
                        filterable
                        clearable
                        placeholder="请选择"
                    >
                        <el-option
                            v-for="item in shopList"
                            :key="item.id"
                            :label="item.title"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                    <el-button
                        type="text"
                        @click="chooseSmallShop(scope.row.id,scope.row.small_shop_id)"
                    >选择小商店</el-button>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="jumpDetail(scope.row)"
                    >详情</el-button>
                    <el-button
                        type="text"
                        v-if="scope.row.channel_type == 1"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="bindSplit(scope.row)"
                    >解绑分账方</el-button>
                    <el-button
                        type="text"
                        v-if="scope.row.channel_type == 1"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="splitSetting(scope.row)"
                    >分账设置</el-button>
                    <el-button
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="jumpRecord(scope.row)"
                    >分账记录</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
          display: 'flex',
          justifyContent: 'flex-end',
          marginRight: '20px',
        }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <addSubMer ref="addSubMer" @getChildList="getCommunicationSubMerList"></addSubMer>
        <detailDrawer ref="detailDrawer"></detailDrawer>
        <splitSetting ref="splitSetting"></splitSetting>
        <bindSplit ref="bindSplit"></bindSplit>
    </m-card>
</template>

<script>
import mDaterange from '@/components/mDate/daterange';
import {
    getCommunicationSubMerList,
    synSubMer,
    getAllSmallShop,
    bindSmallShop,
} from '@/api/aggregatePayment';
import addSubMer from './addSubMer.vue';
import { confirm } from '@/decorators/decorators';
import detailDrawer from './detailDrawer.vue';
import splitSetting from './splitSetting.vue';
import bindSplit from './bindSplit.vue';
export default {
    name: 'childMerchantIndex',
    components: {
        mDaterange,
        addSubMer,
        detailDrawer,
        splitSetting,
        bindSplit,
    },
    data() {
        return {
            searchInfo: {
                user_id: null,
                tel: '',
                nick_name: '',
                merchant_no: '',
                mer_reg_name: '',
                mer_opt_name: '',
                separate_bind_status: null,
                start_at: '',
                end_at: '',
            },
            orderDate: [], // 开通时间
            goodsCommand: 'user_id',
            shopCommand: 'merchant_no',
            dataTable: [],
            shopList: [], // 小商店列表
            small_shop_id: null, // 选中小商店ID
            page: 1,
            pageSize: 10,
            total: null,
        };
    },
    mounted() {
        this.getCommunicationSubMerList();
        this.getAllSmallShop();
    },
    methods: {
        // 获取全部小商店
        async getAllSmallShop() {
            const res = await getAllSmallShop();
            if (res.code === 0) {
                this.shopList = res.data.shops;
            }
        },
        // 选择小商店
        @confirm('提示', '若选中的小商店已被绑定则会切换到当前子商户?')
        async chooseSmallShop(id, small_shop_id) {
            const data = {
                id: parseInt(id),
                small_shop_id: parseInt(small_shop_id),
            };
            const res = await bindSmallShop(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.getCommunicationSubMerList();
            }
        },
        // 新增子商户
        addSubMer() {
            this.$refs.addSubMer.dialogVisible = true;
        },
        // 同步子商户
        async synSubMer() {
            const res = await synSubMer();
            if (res.code === 0) {
                this.$message.success(res.msg);
            }
        },
        returnGoodsValue(command) {
            let text = '会员ID';
            switch (command) {
                case 'user_id':
                    text = '会员ID';
                    break;
                case 'tel':
                    text = '手机号';
                    break;
                case 'nick_name':
                    text = '会员昵称';
                    break;
                default:
                    break;
            }
            return text;
        },
        handleCommand(command) {
            this.goodsCommand = command;
            // 切换选项时清空所有数值
            this.searchInfo.user_id = null;
            this.searchInfo.tel = '';
            this.searchInfo.nick_name = '';
        },
        returnShopValue(command) {
            let text = '内部商户号';
            switch (command) {
                case 'merchant_no':
                    text = '内部商户号';
                    break;
                case 'mer_reg_name':
                    text = '商户注册名称';
                    break;
                case 'mer_opt_name':
                    text = '商户经营名称';
                    break;
                default:
                    break;
            }
            return text;
        },
        handleShopCommand(command) {
            this.shopCommand = command;
            // 切换选项时清空所有数值
            this.searchInfo.merchant_no = '';
            this.searchInfo.mer_reg_name = '';
            this.searchInfo.mer_opt_name = '';
        },
        // 获取子商户列表
        async getCommunicationSubMerList() {
            const data = {
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchInfo,
                parent_store_code: this.$route.query.parent_store_code
                    ? this.$route.query.parent_store_code
                    : '',
            };
            const res = await getCommunicationSubMerList(data);
            if (res.code === 0) {
                res.data.list.forEach((element) => {
                    if (element.small_shop_id === 0) {
                        element.small_shop_id = null;
                    }
                });
                this.dataTable = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        async search() {
            if (this.orderDate) {
                this.searchInfo.start_at =
                    this.orderDate.length > 0 ? this.orderDate[0] : '';
                this.searchInfo.end_at =
                    this.orderDate.length > 0 ? this.orderDate[1] : '';
            } else {
                this.searchInfo.start_at = '';
                this.searchInfo.end_at = '';
            }
            this.getCommunicationSubMerList();
        },
        // 重置搜索条件
        reSearch() {
            this.searchInfo = {
                user_id: null,
                tel: '',
                nick_name: '',
                merchant_no: '',
                mer_reg_name: '',
                mer_opt_name: '',
                separate_bind_status: null, // 所属通道
                start_at: '',
                end_at: '',
            };
            this.orderDate = [];
        },
        // 跳转详情
        jumpDetail(item) {
            this.$refs.detailDrawer.drawer = true;
            this.$refs.detailDrawer.id = item.id;
            this.$refs.detailDrawer.getCommunicationSubMerById();
        },
        // 分账设置
        splitSetting(row) {
            this.$refs.splitSetting.dialogVisible = true;
            this.$refs.splitSetting.splitDetailList(row);
        },
        // 分账记录
        jumpRecord(row) {
            this.$router.push({
                path: '/layout/aggregatePaymentIndex/splitRecordIndex',
                query: {
                    payment_store_code: row.payment_store_code,
                },
            });
        },
        // 解绑分账方
        bindSplit(row) {
            this.$refs.bindSplit.dialogVisible = true;
            this.$refs.bindSplit.bindSplitList(row);
            this.$refs.bindSplit.getCommunicationPaymentInfoByPaymentStoreCode(
                row,
            );
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getCommunicationSubMerList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getCommunicationSubMerList();
        },
    },
};
</script>

<style>
</style>