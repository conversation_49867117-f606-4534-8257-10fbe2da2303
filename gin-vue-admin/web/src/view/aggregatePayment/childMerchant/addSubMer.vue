<template>
    <m-card>
        <el-dialog title="新增子商户" :visible.sync="dialogVisible" width="40%" @close="cancel">
            <el-form :model="searchForm" label-width="120px">
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>主商户:
                            </span>
                            <el-select
                                v-model="searchForm.parent_store_code"
                                placeholder="请选择"
                                class="w100"
                                @change="chooseChannelType"
                            >
                                <el-option
                                    v-for="item in merchantOptions"
                                    :key="item.payment_store_code"
                                    :label="item.store_name + '-' + item.channel_type_desc"
                                    :value="item.payment_store_code"
                                ></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="channel_type" :span="16">
                        <el-form-item label="支付通道:">
                            <span v-if="channel_type == 1">拉卡拉</span>
                            <span v-if="channel_type == 2">汇付</span>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="channel_type == 1" :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>商户注册名称:
                            </span>
                            <el-input clearable v-model="searchForm.register_name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="channel_type == 1" :span="21">
                        <el-form-item>
                            <span slot="label">商户经营名称:</span>
                            <el-input clearable v-model="searchForm.operate_name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="channel_type == 1" :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>银联商家号:
                            </span>
                            <el-input clearable v-model="searchForm.mer_cup_no"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="channel_type" :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>商家号:
                            </span>
                            <el-input clearable v-model="searchForm.merchant_no"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="channel_type == 1" :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>终端号:
                            </span>
                            <el-input clearable v-model="searchForm.term_no"></el-input>
                            <p class="tip">终端号，在商户详情-终端信息中，终端号填写业务信息（扫描）中的终端号</p>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="channel_type == 1" :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>vpos_id:
                            </span>
                            <el-input clearable v-model="searchForm.vpos_id"></el-input>
                            <p class="tip">实体终端号，在商户详情-终端信息中，终端号填写业务信息（扫描）中的终端号</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="5">
                        <el-form-item>
                            <el-button @click="addSubMer" type="primary">确定</el-button>
                            <el-button @click="cancel">取消</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-dialog>
    </m-card>
</template>

<script>
import {
    getCommunicationPaymentInfoAll,
    addSubMer,
} from '@/api/aggregatePayment';
export default {
    name: 'addSubMer',
    data() {
        return {
            dialogVisible: false,
            searchForm: {
                parent_store_code: '', // 主商户
                register_name: '', // 商户注册名称
                operate_name: '', // 商户经营名称
                mer_cup_no: '', // 银联商家号
                merchant_no: '', // 商家号
                term_no: '', // 终端号
                vpos_id: '', // 实体终端号
            },
            merchantOptions: [],
            channel_type: null,
        };
    },
    mounted() {
        this.getCommunicationPaymentInfoAll();
    },
    methods: {
        // 获取支持分账的所有主商户
        async getCommunicationPaymentInfoAll() {
            const res = await getCommunicationPaymentInfoAll();
            if (res.code === 0) {
                this.merchantOptions = res.data;
            }
        },
        // 选择主商户
        chooseChannelType() {
            const selectedItem = this.merchantOptions.find(
                (item) =>
                    item.payment_store_code ===
                    this.searchForm.parent_store_code,
            );
            if (selectedItem) {
                this.channel_type = selectedItem.channel_type;
            }
        },
        // 新增
        async addSubMer() {
            const data = {
                ...this.searchForm,
            };
            if (!this.searchForm.parent_store_code) {
                this.$message.error('请提交主商户号');
                return;
            }
            if (!this.searchForm.register_name && this.channel_type === 1) {
                this.$message.error('请提交商户注册名称');
                return;
            }
            if (!this.searchForm.mer_cup_no && this.channel_type === 1) {
                this.$message.error('请提交银联商家号');
                return;
            }
            if (!this.searchForm.merchant_no) {
                this.$message.error('请提交商家号');
                return;
            }
            if (!this.searchForm.term_no && this.channel_type === 1) {
                this.$message.error('请提交终端号');
                return;
            }
            if (!this.searchForm.vpos_id && this.channel_type === 1) {
                this.$message.error('请提交实体终端号');
                return;
            }
            const res = await addSubMer(data);
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.cancel();
                this.$emit('getChildList');
            }
        },
        // 取消
        cancel() {
            this.searchForm = {
                parent_store_code: '', // 主商户
                register_name: '', // 商户注册名称
                operate_name: '', // 商户经营名称
                mer_cup_no: '', // 银联商家号
                merchant_no: '', // 商家号
                term_no: '', // 终端号
                vpos_id: '', // 实体终端号
            };
            this.channel_type = null;
            this.dialogVisible = false;
        },
    },
};
</script>

<style lang="scss" scoped>
.tip {
    color: rgba(16, 16, 16, 0.5);
    font-size: 14px;
}
</style>