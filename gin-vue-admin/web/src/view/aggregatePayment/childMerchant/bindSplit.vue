<template>
    <m-card>
        <el-dialog
            :title="bindSplit.separate_bind_status == 0 ? '绑定分账方' : '解绑分账方'"
            :visible.sync="dialogVisible"
            width="40%"
            @close="cancel"
        >
            <el-steps :active="active" align-center>
                <el-step title="填写申请"></el-step>
                <el-step title="审核中"></el-step>
                <el-step v-if="active === 2" title="审核驳回"></el-step>
                <el-step v-else title="审核通过"></el-step>
            </el-steps>
            <el-form :model="searchForm" label-width="180px">
                <el-row :gutter="10">
                    <el-col :span="24">
                        <el-form-item label="当前支付商户名称:">{{ bindSplit.payment_store_code }}</el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="平台分账接收方名称:">{{ receiver_name }}</el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>合作协议附件名称:
                            </span>
                            <el-input clearable v-model="searchForm.entrust_file_name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>*合作协议附件:
                            </span>
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :action="path+'/fileUploadAndDownload/uploadFileUnrestricted'"
                                :headers="{'x-token':token}"
                                :on-success="handleMainImgSuccess"
                                :before-upload="beforeAvatarUpload"
                                accept=".jpg, .png, .pdf"
                            >
                                <img
                                    v-if="searchForm.entrust_file && tag !== 'pdf'"
                                    :src="searchForm.entrust_file"
                                    class="avatar"
                                />
                                <div v-else-if="searchForm.entrust_file && tag === 'pdf'">
                                    <div class="mt_20">PDF文件</div>
                                    <div class="pdf-uploader">点击重新上传</div>
                                    <i class="el-icon-error pdf-icon" @click.stop="closePDF"></i>
                                </div>
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                            <el-button v-if="tag === 'pdf'" type="text" @click="checkPDf">预览PDF文件</el-button>
                            <p class="tip">支持5M以内jpg/png/pdf格式</p>
                        </el-form-item>
                    </el-col>
                    <el-col v-if="bindSplit.separate_bind_status !== 0" :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>解绑说明:
                            </span>
                            <el-input clearable v-model="searchForm.remark"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="5">
                        <el-form-item>
                            <el-button type="primary" @click="bindSubMerSplitSettlement">确定</el-button>
                            <el-button @click="cancel">取消</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-dialog>
    </m-card>
</template>

<script>
import { mapGetters } from 'vuex';
import {
    bindSubMerSplitSettlement,
    getCommunicationPaymentInfoByPaymentStoreCode,
} from '@/api/aggregatePayment';
export default {
    name: 'bindSplit',
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    data() {
        return {
            path: this.$path,
            dialogVisible: false,
            active: 0,
            searchForm: {
                entrust_file_name: '', // 合作协议附件名称:
                entrust_file: '', // 合作协议附件:
                remark: '', // 解绑说明
            },
            bindSplit: {},
            tag: '', // 上传文件类型
            receiver_name: '', // 平台分账接收方名称
        };
    },
    methods: {
        // 通过子商户获取主商户的信息
        async getCommunicationPaymentInfoByPaymentStoreCode(row) {
            const data = {
                payment_store_code: row.parent_store_code,
            };
            const res = await getCommunicationPaymentInfoByPaymentStoreCode(
                data,
            );
            if (res.code === 0) {
                this.receiver_name = res.data.receiver_name;
            }
        },
        bindSplitList(row) {
            this.bindSplit = row;
            if (row.separate_bind_status === 0) {
                this.active = 0;
            } else {
                this.active =
                    row.bind_or_unbind_sub_mer_split_settlement_log.audit_status;
                this.searchForm.remark =
                    row.bind_or_unbind_sub_mer_split_settlement_log.audit_remark;
            }
        },
        // 上传校验大小
        beforeAvatarUpload(file) {
            const isLt5M = file.size / 1024 / 1024 < 5;
            if (!isLt5M) {
                this.$message.error('上传图片大小不能超过 5MB!');
            }
            return isLt5M;
        },
        // 上传主图
        handleMainImgSuccess(res) {
            if (res.code === 0) {
                this.tag = res.data.file.tag;
                this.searchForm.entrust_file = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 预览PDF文件
        checkPDf() {
            window.open(this.searchForm.entrust_file);
        },
        // 删除PDF文件
        closePDF() {
            this.searchForm.entrust_file = '';
            this.tag = '';
        },
        // 绑定分账方
        async bindSubMerSplitSettlement() {
            if (!this.searchForm.entrust_file_name) {
                this.$message.error('请填写合作协议附件名称');
                return;
            }
            if (!this.searchForm.entrust_file) {
                this.$message.error('请填写合作协议附件');
                return;
            }
            if (
                this.bindSplit.separate_bind_status !== 0 &&
                !this.searchForm.remark
            ) {
                this.$message.error('请填写解绑说明');
                return;
            }
            const data = {
                type: this.bindSplit.separate_bind_status == 0 ? 0 : 1,
                payment_store_code: this.bindSplit.payment_store_code,
                entrust_file: this.searchForm.entrust_file,
                entrust_file_name: this.searchForm.entrust_file_name,
                remark: this.searchForm.remark,
            };
            const res = await bindSubMerSplitSettlement(data);
            if (res.code === 0) {
                this.$message.suceess(res.msg);
                this.cancel;
            }
        },
        // 取消
        cancel() {
            this.dialogVisible = false;
            this.searchForm = {
                entrust_file_name: '',
                entrust_file: '',
                remark: '',
            };
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-step__title.is-process {
    font-weight: 0;
    color: rgba(16, 16, 16, 0.8);
    font-size: 14px;
}
::v-deep .avatar-uploader {
    .el-upload-list {
        li {
            width: 178px;
            height: 178px;
        }
    }

    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 178px;
        height: 178px;

        img {
            width: 178px;
            height: 178px;
        }

        .pdf-uploader {
            width: 178px !important;
            height: 78px !important;
            background-color: #4c4c4c !important;
            color: #ffffff;
            border-radius: 0px 0px 6px 6px;
            margin-top: 44px;
            padding-top: 20px;
            box-sizing: border-box;
        }
        .pdf-icon {
            font-size: 24px;
            position: absolute;
            top: -10px;
            left: 165px;
        }
    }
}

::v-deep .sku_avatar-uploader {
    .el-upload-list {
        li {
            width: 75px;
            height: 75px;
            min-width: 75px;
            min-height: 75px;
        }
    }

    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 75px;
        height: 75px;
        min-width: 75px;
        min-height: 75px;

        .avatar-uploader-icon {
            line-height: 75px;
        }

        img {
            width: 75px;
            height: 75px;
            min-width: 75px;
            min-height: 75px;
        }
    }
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;

    line-height: 178px;
    text-align: center;
}
</style>