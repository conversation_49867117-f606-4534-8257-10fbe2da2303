<template>
    <m-card>
        <el-dialog title="分账设置" :visible.sync="dialogVisible" width="40%" @close="cancel">
            <el-form :model="searchForm" label-width="180px">
                <el-row :gutter="10">
                    <el-col :span="16">
                        <el-form-item label="审核状态:">
                            <span
                                v-if="splitDetail.sub_mer_split_settlement_log_id !== 0 && splitDetail.sub_mer_split_settlement_logs.audit_status === 0"
                            >待审核</span>
                            <span
                                v-else-if="splitDetail.sub_mer_split_settlement_log_id !== 0 && splitDetail.sub_mer_split_settlement_logs.audit_status === 1"
                            >审核通过</span>
                            <span
                                v-else-if="splitDetail.sub_mer_split_settlement_log_id !== 0 && splitDetail.sub_mer_split_settlement_logs.audit_status === 2"
                            >审核驳回</span>
                            <span v-else></span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item
                            label="支付商户号:"
                        >{{ splitDetail.sub_mer_split_settlement_log_id == 0 ? splitDetail.payment_store_code : splitDetail.sub_mer_split_settlement_logs.payment_store_code }}</el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>联系手机号:
                            </span>
                            <el-input clearable v-model="searchForm.contact_mobile"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>最低分账比例:
                            </span>
                            <div class="f fac">
                                <el-input-number
                                    :controls="false"
                                    v-model="searchForm.split_lowest_ratio"
                                    :precision="2"
                                    :max="999.99"
                                ></el-input-number>
                                <div class="ml265">%</div>
                            </div>
                            <p class="tip">百分比，最大支持输入三位整数两位小数，xxx.xx</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>分账结算委托书文件名称:
                            </span>
                            <el-input clearable v-model="searchForm.entrust_file_name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">
                                <span class="color-red">*</span>分账结算委托书文件:
                            </span>
                            <el-upload
                                class="avatar-uploader"
                                :show-file-list="false"
                                :action="path+'/fileUploadAndDownload/uploadFileUnrestricted'"
                                :headers="{'x-token':token}"
                                :on-success="handleMainImgSuccess"
                                :before-upload="beforeAvatarUpload"
                                accept=".jpg, .png, .pdf"
                            >
                                <img
                                    v-if="searchForm.entrust_file"
                                    :src="searchForm.entrust_file"
                                    class="avatar"
                                />
                                <i v-else class="el-icon-plus avatar-uploader-icon"></i>
                            </el-upload>
                            <p class="tip">变更比例必传，支持5M以内jpg/png/pdf格式</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12" :offset="5">
                        <el-form-item>
                            <el-button type="primary" @click="addSplit">确定</el-button>
                            <el-button @click="cancel">取消</el-button>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-dialog>
    </m-card>
</template>

<script>
import { mapGetters } from 'vuex';
import {
    applySubMerSplitSettlement,
    saveSubMerSplitSettlement,
} from '@/api/aggregatePayment';
export default {
    name: 'splitSetting',
    computed: {
        ...mapGetters('user', ['userInfo', 'token']),
    },
    data() {
        return {
            path: this.$path,
            dialogVisible: false,
            searchForm: {
                contact_mobile: '', // 联系手机号
                split_lowest_ratio: 0, // 最低分账比例
                entrust_file_name: '', // 分账结算委托书文件名称
                entrust_file: '', // 分账结算委托书文件
            },
            splitDetail: {},
        };
    },
    methods: {
        splitDetailList(row) {
            this.splitDetail = row;
            if (row.sub_mer_split_settlement_log_id === 0) {
                this.searchForm.contact_mobile = row.mer_contact_mobile;
            } else {
                this.searchForm.contact_mobile =
                    row.sub_mer_split_settlement_logs.contact_mobile;
                this.searchForm.split_lowest_ratio =
                    row.sub_mer_split_settlement_logs.split_lowest_ratio;
            }
        },
        // 上传校验大小
        beforeAvatarUpload(file) {
            const isLt5M = file.size / 1024 / 1024 < 5;
            if (!isLt5M) {
                this.$message.error('上传图片大小不能超过 5MB!');
            }
            return isLt5M;
        },
        // 上传主图
        handleMainImgSuccess(res) {
            if (res.code === 0) {
                this.searchForm.entrust_file = res.data.file.url;
            } else {
                this.$message.error(res.msg);
            }
        },
        // 添加分账
        async addSplit() {
            if (!this.searchForm.contact_mobile) {
                this.$message.error('请提交联系手机号');
                return;
            }
            if (!this.searchForm.split_lowest_ratio) {
                this.$message.error('请提交最低分账比例');
                return;
            }
            if (!this.searchForm.entrust_file_name) {
                this.$message.error('请提交分账结算委托书文件名称');
                return;
            }
            if (!this.searchForm.entrust_file) {
                this.$message.error('请提交分账结算委托书文件');
                return;
            }
            const data = {
                payment_store_code:
                    this.splitDetail.sub_mer_split_settlement_log_id == 0
                        ? this.splitDetail.payment_store_code
                        : this.splitDetail.sub_mer_split_settlement_logs
                              .payment_store_code,
                contact_mobile: this.searchForm.contact_mobile,
                split_lowest_ratio: this.searchForm.split_lowest_ratio.toString(),
                entrust_file_name: this.searchForm.entrust_file_name,
                entrust_file: this.searchForm.entrust_file,
            };
            let res = '';
            if (this.splitDetail.sub_mer_split_settlement_logs.type == 0) {
                res = await applySubMerSplitSettlement(data);
            } else {
                res = await saveSubMerSplitSettlement(data);
            }
            if (res.code === 0) {
                this.$message.success(res.msg);
                this.cancel();
            }
        },
        // 取消
        cancel() {
            this.dialogVisible = false;
            this.searchForm = {
                contact_mobile: '', // 联系手机号
                split_lowest_ratio: 0, // 最低分账比例
                entrust_file_name: '', // 分账结算委托书文件名称
                entrust_file: '', // 分账结算委托书文件
            };
        },
    },
};
</script>

<style lang="scss" scoped>
.tip {
    color: rgba(16, 16, 16, 0.5);
    font-size: 14px;
}
::v-deep .el-input-number.is-without-controls .el-input__inner {
    text-align: justify;
    width: 453px;
}
::v-deep .avatar-uploader {
    .el-upload-list {
        li {
            width: 178px;
            height: 178px;
        }
    }

    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 178px;
        height: 178px;

        img {
            width: 178px;
            height: 178px;
        }
    }
}

::v-deep .sku_avatar-uploader {
    .el-upload-list {
        li {
            width: 75px;
            height: 75px;
            min-width: 75px;
            min-height: 75px;
        }
    }

    .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        width: 75px;
        height: 75px;
        min-width: 75px;
        min-height: 75px;

        .avatar-uploader-icon {
            line-height: 75px;
        }

        img {
            width: 75px;
            height: 75px;
            min-width: 75px;
            min-height: 75px;
        }
    }
}
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;

    line-height: 178px;
    text-align: center;
}
.ml265 {
    margin-left: 265px;
}
</style>