<template>
    <m-card>
        <el-form :model="searchInfo" label-width="90px" class="search-term mt_20" inline>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.mer_opt_name"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商户名称</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.payment_store_code"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商户编号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.contact_mobile"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">联系手机号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>审核状态</span>
                    </div>
                    <el-select v-model="searchInfo.audit_status" class="w100" clearable>
                        <el-option :value="0" label="审核中"></el-option>
                        <el-option :value="1" label="审核成功"></el-option>
                        <el-option :value="2" label="审核拒绝"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input" style="width: 500px">
                    <div class="line-box">
                        <span>申请时间</span>
                    </div>
                    <m-daterange v-model="orderDate"></m-daterange>
                </div>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="dataTable">
            <el-table-column label="申请时间" align="center">
                <template slot-scope="scope">
                    <p>{{ scope.row.created_at | formatDate }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>商户名称</p>
                </template>
                <template slot-scope="scope">
                    <p
                        v-if="scope.row.communication_sub_mer_detail.mer_opt_name"
                    >{{ scope.row.communication_sub_mer_detail.mer_opt_name }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>商户编号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.payment_store_code }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>联系手机号</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.contact_mobile != ''">{{ scope.row.contact_mobile }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>最低分账比例</p>
                </template>
                <template slot-scope="scope">
                    <p
                        v-if="scope.row.communication_sub_mer_detail.split_lowest_ratio != ''"
                    >{{ scope.row.communication_sub_mer_detail.split_lowest_ratio }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>审核状态</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.audit_status == 0">审核中</p>
                    <p v-else-if="scope.row.audit_status == 1">审核成功</p>
                    <p v-else-if="scope.row.audit_status == 2">审核拒绝</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="jumpDetail(scope.row)"
                    >查看详情</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
          display: 'flex',
          justifyContent: 'flex-end',
          marginRight: '20px',
        }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <el-dialog title="查看详情" :visible.sync="dialogVisible" width="40%">
            <el-form :model="searchForm" label-width="180px">
                <el-row :gutter="10">
                    <el-col :span="16">
                        <el-form-item label="审核状态:">
                            <span v-if="searchForm.audit_status === 0">待审核</span>
                            <span v-else-if="searchForm.audit_status === 1">审核通过</span>
                            <span v-else-if="searchForm.audit_status === 2">审核驳回</span>
                            <span v-else></span>
                        </el-form-item>
                    </el-col>
                    <el-col :span="16">
                        <el-form-item label="支付商户号:">{{ searchForm.payment_store_code }}</el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">联系手机号:</span>
                            <el-input clearable v-model="searchForm.contact_mobile"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">最低分账比例:</span>
                            <div class="f fac">
                                <el-input-number
                                    :controls="false"
                                    v-model="searchForm.split_lowest_ratio"
                                    :precision="2"
                                    :max="999.99"
                                ></el-input-number>
                                <div class="ml265">%</div>
                            </div>
                            <p class="tip">百分比，最大支持输入三位整数两位小数，xxx.xx</p>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">分账结算委托书文件名称:</span>
                            <el-input clearable v-model="searchForm.entrust_file_name"></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="21">
                        <el-form-item>
                            <span slot="label">分账结算委托书文件:</span>
                            <img :src="searchForm.entrust_file" class="avatar" />
                            <p class="tip">变更比例必传，支持5M以内jpg/png/pdf格式</p>
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
        </el-dialog>
    </m-card>
</template>

<script>
import mDaterange from '@/components/mDate/daterange';
import { getSubMerSplitSettlementLogList } from '@/api/aggregatePayment';
export default {
    name: 'splitAuditIndex',
    components: { mDaterange },
    data() {
        return {
            dialogVisible: false,
            searchInfo: {
                mer_opt_name: '',
                payment_store_code: '',
                contact_mobile: '', //
                audit_status: null,
                start_at: '',
                end_at: '',
            },
            orderDate: [],
            dataTable: [],
            page: 1,
            pageSize: 10,
            total: null,
            searchForm: {},
        };
    },
    mounted() {
        this.getSubMerSplitSettlementLogList();
    },
    methods: {
        // 分账开户审核列表
        async getSubMerSplitSettlementLogList() {
            if (this.searchInfo.audit_status == '') {
                this.searchInfo.audit_status = null;
            }
            const data = {
                ...this.searchInfo,
                page: this.page,
                pageSize: this.pageSize,
            };
            const res = await getSubMerSplitSettlementLogList(data);
            if (res.code === 0) {
                this.dataTable = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        async search() {
            if (this.orderDate) {
                this.searchInfo.start_at =
                    this.orderDate.length > 0 ? this.orderDate[0] : '';
                this.searchInfo.end_at =
                    this.orderDate.length > 0 ? this.orderDate[1] : '';
            } else {
                this.searchInfo.start_at = '';
                this.searchInfo.end_at = '';
            }
            this.getSubMerSplitSettlementLogList();
        },
        // 重置搜索条件
        reSearch() {
            this.searchInfo = {
                mer_opt_name: '',
                payment_store_code: '',
                contact_mobile: '',
                audit_status: null,
                start_at: '',
                end_at: '',
            };
            this.orderDate = [];
        },
        // 查看详情
        jumpDetail(item) {
            this.dialogVisible = true;
            this.searchForm = item;
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getSubMerSplitSettlementLogList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getSubMerSplitSettlementLogList();
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-input-number.is-without-controls .el-input__inner {
    text-align: justify;
    width: 453px;
}
.ml265 {
    margin-left: 265px;
}
</style>