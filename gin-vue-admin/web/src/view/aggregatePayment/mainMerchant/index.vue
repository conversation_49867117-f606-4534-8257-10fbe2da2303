<template>
    <m-card>
        <el-button type="primary" @click="paymentMerchantSync">同步主商户</el-button>
        <el-form :model="searchInfo" label-width="90px" class="search-term mt_20" inline>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.store_code"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">商户编号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <el-input
                    placeholder="请输入"
                    v-model="searchInfo.payment_store_code"
                    class="line-input"
                    clearable
                >
                    <span slot="prepend">支付商户号</span>
                </el-input>
            </el-form-item>
            <el-form-item>
                <div class="line-input">
                    <div class="line-box">
                        <span>所属通道</span>
                    </div>
                    <el-select v-model="searchInfo.channel_type" class="w100" clearable>
                        <el-option :value="1" label="拉卡拉"></el-option>
                        <el-option :value="2" label="汇付支付"></el-option>
                        <!-- <el-option :value="3" label="乐刷支付"></el-option>
                        <el-option :value="4" label="富友支付"></el-option>-->
                        <el-option :value="5" label="汇聚支付"></el-option>
                    </el-select>
                </div>
            </el-form-item>
            <el-form-item>
                <div class="line-input" style="width: 500px">
                    <div class="line-box">
                        <span>开通时间</span>
                    </div>
                    <m-daterange v-model="orderDate"></m-daterange>
                </div>
            </el-form-item>
            <br />
            <el-form-item>
                <el-button type="primary" @click="search">搜索</el-button>
            </el-form-item>
            <el-form-item>
                <el-button type="text" @click="reSearch">重置搜索条件</el-button>
            </el-form-item>
        </el-form>
        <el-table :data="dataTable">
            <el-table-column label="开通时间" align="center">
                <template slot-scope="scope">
                    <p
                        v-if="scope.row.communication_merchant_detail.create_time != ''"
                    >{{ scope.row.communication_merchant_detail.create_time }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>商户编号</p>
                    <p>支付商户号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.store_code }}</p>
                    <p>{{ scope.row.payment_store_code }}</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>所属通道</p>
                    <p>终端号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.channel_type_desc }}</p>
                    <p v-if="scope.row.term_no != ''">{{ scope.row.term_no }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>appid</p>
                    <p>机构号</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.appid }}</p>
                    <p v-if="scope.row.org_code != ''">{{ scope.row.org_code }}</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>钱包ID</p>
                    <p>提款模式</p>
                </template>
                <template slot-scope="scope">
                    <p v-if="scope.row.wallet_id != ''">{{ scope.row.wallet_id }}</p>
                    <p v-else>--</p>
                    <p v-if="scope.row.settle_type == '01'">主动提款</p>
                    <p v-else-if="scope.row.settle_type == '02'">余额自动结算</p>
                    <p v-else-if="scope.row.settle_type == '03'">交易自动结算</p>
                    <p v-else>--</p>
                </template>
            </el-table-column>
            <el-table-column align="center">
                <template slot="header">
                    <p>子商户数量</p>
                    <p>分账方数量</p>
                </template>
                <template slot-scope="scope">
                    <p>{{ scope.row.sub_mer_count }}</p>
                    <p>{{ scope.row.apply_count }}</p>
                </template>
            </el-table-column>
            <el-table-column label="操作" align="center">
                <template slot-scope="scope">
                    <el-button
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="jumpDetail(scope.row)"
                    >详情</el-button>
                    <el-button
                        type="text"
                        style="padding: 0 !important;margin-left: 10px !important;"
                        @click="jumpChildMerchant(scope.row)"
                    >子商户</el-button>
                </template>
            </el-table-column>
        </el-table>
        <el-pagination
            background
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="[10, 30, 50, 100]"
            :style="{
          display: 'flex',
          justifyContent: 'flex-end',
          marginRight: '20px',
        }"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
            layout="total, sizes, prev, pager, next, jumper"
        ></el-pagination>
        <detailDrawer ref="detailDrawer"></detailDrawer>
    </m-card>
</template>

<script>
import mDaterange from '@/components/mDate/daterange';
import { getCommunicationPaymentInfoList } from '@/api/aggregatePayment';
import { paymentMerchantSync } from '@/api/system';
import { confirm } from '@/decorators/decorators';
import detailDrawer from './detailDrawer.vue';
export default {
    name: 'mainMerchantIndex',
    components: { mDaterange, detailDrawer },
    data() {
        return {
            searchInfo: {
                store_code: '', // 商户编号
                payment_store_code: '', // 支付商户号
                channel_type: null, // 所属通道
                start_at: '',
                end_at: '',
            },
            orderDate: [], // 开通时间
            dataTable: [],
            page: 1,
            pageSize: 10,
            total: null,
        };
    },
    mounted() {
        this.getCommunicationPaymentInfoList();
    },
    methods: {
        @confirm('提示', '确定同步商户?')
        async paymentMerchantSync() {
            const data = {};
            const res = await paymentMerchantSync(data);
            if (res.code === 0) {
                this.page = 1;
                this.$message.success(res.msg);
                this.getCommunicationPaymentInfoList();
            }
        },
        // 获取主商户列表
        async getCommunicationPaymentInfoList() {
            const params = {
                page: this.page,
                pageSize: this.pageSize,
                ...this.searchInfo,
            };
            const res = await getCommunicationPaymentInfoList(params);
            if (res.code === 0) {
                this.dataTable = res.data.list;
                this.total = res.data.total;
            }
        },
        // 搜索
        async search() {
            if (this.orderDate) {
                this.searchInfo.start_at =
                    this.orderDate.length > 0 ? this.orderDate[0] : '';
                this.searchInfo.end_at =
                    this.orderDate.length > 0 ? this.orderDate[1] : '';
            } else {
                this.searchInfo.start_at = '';
                this.searchInfo.end_at = '';
            }
            this.getCommunicationPaymentInfoList();
        },
        // 重置搜索条件
        reSearch() {
            this.searchInfo = {
                store_code: '', // 商户编号
                payment_store_code: '', // 支付商户号
                channel_type: null, // 所属通道
                start_at: '',
                end_at: '',
            };
            this.orderDate = [];
        },
        // 跳转详情
        jumpDetail(item) {
            this.$refs.detailDrawer.drawer = true;
            this.$refs.detailDrawer.id = item.id;
            this.$refs.detailDrawer.getCommunicationPaymentInfoById();
        },
        // 跳转子商户
        jumpChildMerchant(item) {
            this.$router.push({
                path: '/layout/aggregatePaymentIndex/childMerchantIndex',
                query: {
                    parent_store_code: item.payment_store_code,
                },
            });
        },
        // 每页条数
        handleSizeChange(val) {
            this.pageSize = val;
            this.page = 1;
            this.getCommunicationPaymentInfoList();
        },
        // 当前页数
        handleCurrentChange(val) {
            this.page = val;
            this.getCommunicationPaymentInfoList();
        },
    },
};
</script>

<style>
</style>