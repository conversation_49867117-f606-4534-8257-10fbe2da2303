<template>
    <m-card>
        <el-drawer title="详情" :visible.sync="drawer" direction="rtl" size="80%">
            <div class="detail-box">
                <div class="detail-title f fac">
                    <div class="detail-title-box"></div>
                    <div>基本信息</div>
                </div>
                <div class="detail-msg">
                    <div>商户状态：{{ detail.communication_merchant_detail.status }}</div>
                    <div class="mt30">商户号：{{ detail.communication_merchant_detail.mer_inner_no }}</div>
                    <div class="mt30">商户注册名称：{{ detail.communication_merchant_detail.mer_reg_name }}</div>
                    <div class="mt30">商户经营名称：{{ detail.communication_merchant_detail.mer_opt_name }}</div>
                    <div class="mt30">商户英文名称：{{ detail.communication_merchant_detail.mer_ename }}</div>
                    <div class="mt30">营业执照：{{ detail.communication_merchant_detail.mer_blis }}</div>
                    <div
                        class="mt30"
                    >营业执照有效期：{{ detail.communication_merchant_detail.mer_blis_exp_dt }}</div>
                    <div
                        class="mt30"
                    >商户注册地址区划：{{ detail.communication_merchant_detail.mer_reg_dist_code }}</div>
                    <div class="mt30">商户注册地址：{{ detail.communication_merchant_detail.mer_reg_addr }}</div>
                    <div
                        class="mt30"
                    >商户经营内容：{{ detail.communication_merchant_detail.mer_busi_content }}</div>
                    <div class="mt30">法人姓名：{{ detail.communication_merchant_detail.lar_name }}</div>
                    <div class="mt30">法人身份证号：{{ detail.communication_merchant_detail.lar_idcard }}</div>
                    <div class="mt30">
                        法人证件类型：
                        <span
                            v-if="detail.communication_merchant_detail.lar_certs_type_code == 17"
                        >身份证</span>
                        <span
                            v-if="detail.communication_merchant_detail.lar_certs_type_code == 18"
                        >护照</span>
                        <span
                            v-if="detail.communication_merchant_detail.lar_certs_type_code == 19"
                        >港澳通行证</span>
                        <span
                            v-if="detail.communication_merchant_detail.lar_certs_type_code == 20"
                        >台胞证</span>
                    </div>
                    <div
                        class="mt30"
                    >法人身份证有效期：{{ detail.communication_merchant_detail.lar_idcard_exp_dt }}</div>
                    <div
                        class="mt30"
                    >商户联系人：{{ detail.communication_merchant_detail.mer_contact_name }}</div>
                    <div
                        class="mt30"
                    >商户联系电话：{{ detail.communication_merchant_detail.mer_contact_tel }}</div>
                    <div
                        class="mt30"
                    >商户联系人手机号：{{ detail.communication_merchant_detail.mer_contact_mobile }}</div>
                    <div class="mt30">商户等级：{{ detail.communication_merchant_detail.mer_grade_code }}</div>
                    <div class="mt30">城市编码：{{ detail.communication_merchant_detail.mer_city_code }}</div>
                    <div class="mt30">商户类型码：{{ detail.communication_merchant_detail.mcc_code }}</div>
                    <div
                        class="mt30"
                    >银联区域码：{{ detail.communication_merchant_detail.mer_cup_dist_code }}</div>
                    <div
                        class="mt30"
                    >银联区域码（县乡）：{{ detail.communication_merchant_detail.mer_cup_county_code }}</div>
                    <div class="mt30">银联优惠标识：{{ detail.communication_merchant_detail.discount_id }}</div>
                    <div
                        class="mt30"
                    >商户拓展机构：{{ detail.communication_merchant_detail.mer_developer_org_id }}</div>
                    <div class="mt30">银联商户号：{{ detail.communication_merchant_detail.mer_cup_no }}</div>
                    <div class="mt30">创建时间：{{ detail.communication_merchant_detail.create_time }}</div>
                    <div
                        class="mt30"
                    >最后修改时间：{{ detail.communication_merchant_detail.modify_lst_tm }}</div>
                    <div class="mt30">版本号：{{ detail.communication_merchant_detail.ver_no }}</div>
                </div>
            </div>
            <div class="mt_20 detail-box">
                <div class="detail-title f fac">
                    <div class="detail-title-box"></div>
                    <div>分账信息</div>
                </div>
                <div class="detail-msg">
                    <div>商户分账状态：{{ detail.communication_merchant_detail.split_status }}</div>
                    <div class="mt30">分账商户机构号：{{ detail.communication_merchant_detail.org_id }}</div>
                    <div class="mt30">分账商户机构名称：{{ detail.communication_merchant_detail.org_name }}</div>
                    <div
                        class="mt30"
                    >最低分账比例：{{ detail.communication_merchant_detail.split_lowest_ratio }}</div>
                    <div class="mt30">分账范围：{{ detail.communication_merchant_detail.split_range }}</div>
                    <div
                        class="mt30"
                    >分账依据：{{ detail.communication_merchant_detail.sep_fund_source }}</div>
                </div>
            </div>
            <div v-if="detail.channel_type == 1" class="mt_20 detail-box">
                <div class="detail-title f fac">
                    <div class="detail-title-box"></div>
                    <div>分账管家</div>
                </div>
                <div class="detail-msg">
                    <div>钱包ID：{{ detail.communication_merchant_detail.ewallet_id }}</div>
                    <div
                        class="mt30"
                    >收款账号ID：{{ detail.communication_merchant_detail.collect_account_id }}</div>
                    <div
                        class="mt30"
                    >提现模式：{{ detail.communication_merchant_detail.settle_type_desc }}</div>
                    <div
                        class="mt30"
                    >结算时间(小时)：{{ detail.communication_merchant_detail.settle_time }}</div>
                </div>
            </div>
        </el-drawer>
    </m-card>
</template>

<script>
import { getCommunicationPaymentInfoById } from '@/api/aggregatePayment';
export default {
    name: 'detailDrawer',
    data() {
        return {
            drawer: false,
            id: null,
            detail: {},
        };
    },
    methods: {
        async getCommunicationPaymentInfoById() {
            const params = {
                id: parseInt(this.id),
            };
            const res = await getCommunicationPaymentInfoById(params);
            if (res.code === 0) {
                this.detail = res.data;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
::v-deep .el-drawer {
    background-color: rgba(240, 242, 245, 1);
}
::v-deep .el-drawer__header {
    background-color: #ffffff;
    padding-bottom: 20px;
    margin-bottom: 19px;
}
.detail-box {
    background-color: #ffffff;
    width: 1490px;
    padding-bottom: 33px;
    .detail-title {
        padding: 21px 24px;
        .detail-title-box {
            width: 4px;
            height: 16px;
            background-color: #155bd4;
            margin-right: 11px;
        }
    }
    .detail-msg {
        padding-left: 24px;
    }
}
.mt30 {
    margin-top: 30px;
}
</style>