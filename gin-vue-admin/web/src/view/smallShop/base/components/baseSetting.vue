<template>
  <el-form ref="formData" :model="formData" label-width="180px">
    <el-form-item label="开启小商店">
      <el-switch v-model="formData.switch"
                 :active-value="1"
                 :inactive-value="0">
      </el-switch>
      <p class="color-grap">开启后，供应链中台小程序具备小商店管理入口，小商店小程序可以正常访问。</p>
      <p class="color-grap">关闭后，小商店小程序访问提示，小商店已经关闭，请联系客服！</p>
    </el-form-item>
    <el-form-item label="等级限制">
      <el-checkbox-group v-model="formData.level_limit">
        <el-checkbox :label="item.id" :key="item.name" v-for="item in levelLimit">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
      <p class="color-grap">勾选会员等级，只有对应等级的会员才能申请成为小商店店主。</p>
    </el-form-item>

    <el-form-item label="开启免审核">
      <el-switch v-model="formData.apply_auto_pass"
                 :active-value="1"
                 :inactive-value="0">
      </el-switch>
      <p class="color-grap">开启后，用户申请无需审核，直接成为店长。</p>
    </el-form-item>
    <el-form-item label="开启付费">
      <el-switch v-model="formData.open_pay"
                 :active-value="1"
                 :inactive-value="0">
      </el-switch>
      <p class="color-grap">开启后，如果勾选的等级限制会员等级支持付费购买的，直接跳转会员权益页面，购买后自动成为店主，无需审核！</p>
      <p class="color-grap">如果会员所在等级已经满足了限制要求，则不跳转，直接成为店主，进入小店。</p>
    </el-form-item>
    <el-form-item label="同步会员到中台">
      <el-switch v-model="formData.sync_users"
                 :active-value="1"
                 :inactive-value="0">
      </el-switch>
      <p class="color-grap">开启后，小商店会员具备微信开放平台unionid并且在中台未注册的，会自动注册为中台会员，会员推荐人为小商店会员推荐人！</p>
      <p class="color-grap" style="color: red;">
        开启该功能前，请务必确认您已经将小商店公众号、小程序绑定到和中台公众号、小程序同一个微信开放平台！
        <el-button type="text" @click="syncUserFun">手动同步！！</el-button>
      </p>
    </el-form-item>
    <el-form-item label="小商店订单支付方式">
      <el-radio-group v-model="formData.order_pay_type">
        <el-radio :label="1">店主余额支付</el-radio>
        <el-radio :label="2">小商店分账支付</el-radio>
      </el-radio-group>
      <p class="color-grap">小商店订单自动同步生成供应链中台订单，以店主身份进行下单采购。</p>
      <p class="color-grap">店主余额支付：优先使用汇聚余额，其次使用站内余额；店主结算金额=小商店订单金额；</p>
      <p class="color-grap">小商店分账支付：供应链中台采购订单直接完成支付；店主结算金额=小商店订单金额-供应链中台采购订单金额；</p>
      <p class="color-grap">在使用聚合支付分账支付的场景下：支付的钱给到了小商店商户号，结合上方两种设置；</p>
      <p class="color-grap">1、选择店主余额支付：已经在店主的余额中扣除了货款了，分账支付的金额100%结算给门店，平台分成金额为0；订单完成满足结算期后结算释放冻结金额给店主。</p>
      <p class="color-grap">2、选择小商店分账支付：平台分成金额=供货价+运费+技术服务费；（1）实际支付金额大于平台分成金额的，订单支付的时候直接把平台分成金额分给平台分账方；剩余的部分冻结，订单完成满足结算期后完结分账给小商店结算；</p>
    </el-form-item>
    <el-form-item label="结算天数">
      <el-input-number class="number-text-left" :controls="false" :precision="0" :min="0"
                       v-model="formData.settle_days"
                       placeholder="请输入结算天数供应商结算天数" clearable>
      </el-input-number>
      <span class="ml10">天</span>
    </el-form-item>
    <el-form-item label="支付方式">
      <el-checkbox-group v-model="formData.pay_types">
        <el-checkbox :label="item.id" :key="item.id" v-for="item in payType">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
      <p class="color-grap">微信官方小程序支付使用小商店--基础设置--小程序设置中的支付方式；</p>
      <p class="color-grap">汇聚微信小程序支付调用系统--支付设置中的汇聚支付设置。</p>
    </el-form-item>
    <el-form-item label="H5支付方式">
      <el-checkbox-group v-model="formData.wechat_pay_types">
        <el-checkbox :label="item.id" :key="item.id" v-for="item in getPayTypesByH5List">{{ item.name }}</el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item label="自主定价">
      <el-switch v-model="formData.independent"
                 :active-value="1"
                 :inactive-value="0">
      </el-switch>
      <p class="color-grap">关闭后店主不能自主定价，小商店商品销售价格优先取供应链中台商品营销价、指导价、建议零售价。</p>
      <p class="color-grap">修改设置将一键更新小商店商品价格。</p>
    </el-form-item>
    <el-form-item label="默认小商店">
      <el-select v-model="formData.default_shop_id" clearable filterable
                 remote
                 reserve-keyword :remote-method="remoteMethod">
        <el-option v-for="item in defaultShopData.options" :label="item.title" :value="item.id"></el-option>
        <div class="text-center">
          <el-pagination background small class="pagination"
                         style="padding-top:10px !important;padding-bottom: 0 !important;"
                         :current-page="defaultShopData.page"
                         :page-size="defaultShopData.pageSize"
                         :total="defaultShopData.total"
                         @current-change="handleOptionPage"
                         layout="prev,pager, next"/>
        </div>
      </el-select>
    </el-form-item>
    <el-form-item label="选品中心数据">
      <el-radio-group v-model="formData.pick_product_limit">
        <el-radio :label="0">不限制</el-radio>
        <el-radio :label="1">选品池</el-radio>
      </el-radio-group>
      <p class="color-grap">选择商品池，小商店店主在移动端-店铺管理-在线选品中只返回选品池的商品数据；通过pc、专辑、短视频、直播等方式同步的商品不受限制！</p>
      <p class="color-grap">小商店店主在移动端-店铺管理-专辑管理-专辑库中只返回商品池中指定专辑的数据！</p>
    </el-form-item>
    <!-- <el-form-item label="专辑库">
      <el-radio-group v-model="formData.album_limit">
        <el-radio :label="0">不限制</el-radio>
        <el-radio :label="1">指定专辑</el-radio>
      </el-radio-group>
      <p class="color-grap">选择指定专辑，小商店店主在移动端-店铺管理-专辑管理-专辑库中只返回指定专辑的数据！</p>
    </el-form-item> -->
    <el-form-item label="指定选品">
      <el-switch v-model="formData.enable_selected_items"
                 :active-value="1"
                 :inactive-value="0">
      </el-switch>
      <p class="color-grap">开启后，用户成为店主后，会自动将指定选品中的商品一键同步到小商店，销售价按平台统一指导价，不走小商店定价策略！</p>
      <p class="color-grap">已存在的小商店不会同步，需要在指定选品中操作同步；</p>
      <p class="color-grap">同步时，如果小商店存在该商品的，不做任何操作！</p>
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="save">保 存</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
import {
  findSetting,
  getPayTypes,
  getUserLevelListNotPage,
  updateSetting,
  getSmallShopListBySetting,
  syncUser,
  getPayTypesByH5
} from "@/api/smallShop"

export default {
  name: "baseSetting",
  data() {
    return {
      payType: [],
      getPayTypesByH5List: [],
      levelLimit: [],
      defaultShopData: {
        options: [],
        total: 0,
        page: 1,
        pageSize: 10,
        title: ""
      },
      formData: {
        id: 0,
        order_pay_type: "",//小商店订单支付方式
        pay_types: [],//支付方式
        pay_types1: [],//支付方式
        wechat_pay_types: [], // 微信支付方式
        settle_days: "",//结算天数
        level_limit: [],//等级限制
        level_limit1: [],//等级限制
        switch: null,//开启小商店
        independent: null,//自主定价
        enable_selected_items: 0, // 指定选品
        apply_auto_pass: null,//开启免审核
        open_pay: null,//开启付费
        default_shop_id: null,
        // album_limit:null, // 专辑数据 0:不限制, 1:专辑池
        pick_product_limit:null,// 选品中心数据 0:不限制, 1:选品池
        sync_users:null // 同步会员到中台
      }
    }
  },
  mounted() {
    this.getDefaultShopOptions()
    this.findSetting()
    this.getPayTypes()
    this.getPayTypesByH5()
    this.getUserLevelListNotPage()
  },
  methods: {
    // 手动同步
    async syncUserFun() {
      let res = await syncUser()
      if (res.code === 0) {
        this.$message.success(res.msg)
      }
    },
    handleOptionPage(val) {
      this.defaultShopData.page = val
      this.getDefaultShopOptions()
    },
    remoteMethod(query) {
      this.defaultShopData.page = 1
      this.defaultShopData.title = query
      this.getDefaultShopOptions()
    },
    async getDefaultShopOptions(page = this.defaultShopData.page, pageSize = this.defaultShopData.pageSize) {
      let params = {}
      if (this.defaultShopData.title) params.title = this.defaultShopData.title
      const {code,data} = await getSmallShopListBySetting({page,pageSize,...params})
      if(code === 0){
        this.defaultShopData.options = data.list
        this.defaultShopData.total = data.total
        console.log(data,'???');
      }
    },
    //会员等级
    getUserLevelListNotPage() {
      getUserLevelListNotPage().then(res => {
        this.levelLimit = res.data.list
      })
    },
    //支付方式
    getPayTypes() {
      getPayTypes().then(res => {
        this.payType = res.data.pay_types
      })
    },
    // h5支付方式
    getPayTypesByH5() {
        getPayTypesByH5().then(res => {
          this.getPayTypesByH5List = res.data.pay_types;
        })
    },
    //获取基础设置
    findSetting() {
      findSetting().then(res => {
        if (res.code === 0) {
          this.formData.id = res.data.setting.id
          this.formData.order_pay_type = res.data.setting.value.order_pay_type
          this.formData.settle_days = res.data.setting.value.settle_days
          this.formData.switch = res.data.setting.value.switch
          this.formData.independent = res.data.setting.value.independent
          this.formData.enable_selected_items = res.data.setting.value.enable_selected_items
          this.formData.apply_auto_pass = res.data.setting.value.apply_auto_pass
          this.formData.open_pay = res.data.setting.value.open_pay
          this.formData.default_shop_id = res.data.setting.value.default_shop_id
          this.formData.pick_product_limit = res.data.setting.value.pick_product_limit
          // this.formData.album_limit = res.data.setting.value.album_limit
          this.formData.level_limit = []
          this.formData.sync_users = res.data.setting.value.sync_users
          
          res.data.setting.value.level_limit.forEach(item => {
            this.formData.level_limit.push(item.id)
          })
          this.formData.pay_types = []
          res.data.setting.value.pay_types.forEach(item => {
            this.formData.pay_types.push(item.id)
          })
          this.formData.wechat_pay_types = [];
          res.data.setting.value.wechat_pay_types.forEach(item => {
            this.formData.wechat_pay_types.push(item.id)
          })
        }
      })
    },
    //保存
    save() {
      this.formData.level_limit1 = []
      this.formData.level_limit.forEach(item => {
        this.formData.level_limit1.push({
          "id": item
        })
      })
      this.formData.pay_types1 = []
      this.formData.pay_types.forEach(item => {
        if (item) {
          this.formData.pay_types1.push(this.payType.find(item1 => item1.id === item))
        }
      })
      let wechat_pay_types = []
      this.formData.wechat_pay_types.forEach(item => {
        if (item) {
          wechat_pay_types.push(this.getPayTypesByH5List.find(item1 => item1.id === item))
        }
      })
      let param = {
        id: this.formData.id,
        value: {
          order_pay_type: this.formData.order_pay_type,
          settle_days: this.formData.settle_days,
          level_limit: this.formData.level_limit1,
          pay_types: this.formData.pay_types1,
          wechat_pay_types: wechat_pay_types,
          switch: this.formData.switch,
          independent: this.formData.independent,
          enable_selected_items: this.formData.enable_selected_items,
          apply_auto_pass: this.formData.apply_auto_pass,
          open_pay: this.formData.open_pay,
          default_shop_id:this.formData.default_shop_id,
          pick_product_limit:parseInt(this.formData.pick_product_limit), 
          // album_limit:parseInt(this.formData.album_limit),
          sync_users:parseInt(this.formData.sync_users)
        }
      }
      updateSetting(param).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
          this.findSetting()
        }
      })
      this.formData.level_limit1 = []
      this.formData.pay_types1 = []
    }
  }
}
</script>

<style lang="scss" scoped>

::v-deep .el-button--text {
  padding: 0 !important;
}

</style>