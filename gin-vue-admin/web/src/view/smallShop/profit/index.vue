<template>
  <m-card>
    <el-table
        :data="tableData"
        style="width: 100%"
        class="mt25">
      <el-table-column align="center" prop="id" label="ID">
      </el-table-column>
      <el-table-column align="center" width="180px">
        <template slot="header">
          <p>支付时间</p>
          <p>结算时间</p>
        </template>
        <template slot-scope="scope">
          <p v-if="scope.row.paid_at">{{ scope.row.paid_at | formatDate }}</p>
          <p v-else>--</p>
          <p v-if="scope.row.settle_at">{{scope.row.settle_at | formatDate}}</p>
          <p v-else>--</p>
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip>
        <template slot="header">
          <p>会员ID</p>
          <p>手机号</p>
        </template>
        <template slot-scope="scope">
          <p>{{scope.row.uid}}</p>
          <p>{{scope.row.shop_user_info.username}}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" label="店主姓名">
        <template slot-scope="scope">
          <p v-if="scope.row.user.nickname">{{scope.row.user.nickname}}</p>
          <p v-else>{{scope.row.user.username}}</p>
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip>
        <template slot="header">
          <p>订单号</p>
        </template>
        <template slot-scope="scope">
          <p>{{scope.row.small_shop_order.order_sn}}</p>
          <p></p>
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip>
        <template slot="header">
          <p>中台订单号</p>
        </template>
        <template slot-scope="scope">
          <p>{{scope.row.shop_order_info.order_sn}}</p>
          <p></p>
        </template>
      </el-table-column>
      <el-table-column align="center" show-overflow-tooltip>
        <template slot="header">
          <p>中台支付方式</p>
          <p>订单是否完成</p>
        </template>
        <template slot-scope="scope">
          <p>{{scope.row.shop_order_info.pay_type}}</p>
          <p>{{scope.row.order_completed |ordertype}}</p>
        </template>
      </el-table-column>
      <el-table-column label="结算天数" align="center">
        <template slot-scope="scope">
          <p>{{scope.row.settle_days}} 天 </p>
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header">
          <p>实付金额</p>
          <p>分成金额</p>
        </template>
        <template slot-scope="scope">
          <p>{{ scope.row.small_shop_order.amount | formatF2Y }}元</p>
          <p>{{scope.row.amount | formatF2Y}}元</p>
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header">
          <p>分红状态</p>
          <p>是否分账</p>
        </template>
        <template slot-scope="scope">
          <p>{{scope.row.status |chargetype}}</p>
          <p v-if="scope.row.is_split === 1">是</p>
          <p v-else>否</p>
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header">
          <p>分账金额</p>
          <p>收入金额</p>
        </template>
        <template slot-scope="scope">
          <p v-if="scope.row.status === 2 && scope.row.split_status === 1" >{{scope.row.amount | formatF2Y}}元</p>
          <p v-else>0.00</p>
          <p v-if="scope.row.status === 1" >{{scope.row.amount | formatF2Y}}元</p>
          <p v-else>0.00</p>
        </template>
      </el-table-column>
      <el-table-column align="center">
        <template slot="header">
          <p>分账状态</p>
          <p>收入状态</p>
        </template>
        <template slot-scope="scope">
          <p v-if="scope.row.split_status === 0 && scope.row.is_split === 1">待结算</p>
          <p v-else-if="scope.row.is_split === 0">--</p>
          <p v-else>成功</p>
          <p v-if="scope.row.status === 1">成功</p>
          <p v-else>--</p>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
        background
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]"
        :style="{
          display: 'flex',
          justifyContent: 'flex-end',
          marginRight: '20px',
        }"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper"
    ></el-pagination>
  </m-card>
</template>

<script>
import {getAwardsList} from "@/api/smallShop"
export default {
  name: "shopProfitIndex",
  data(){
    return{
      page: 1,
      pageSize: 10,
      total: 0,
      tableData:[]
    }
  },
  filters:{
    chargetype(type){
      let s=""
      switch (type){
        case 0 :
          s="未结算"
          break
        case 1 :
          s="已结算"
          break
        case-1 :
          s="失效"
        case 2 :
          s="子商户"
         break
      }
      return s;
    },
    ordertype(type){
      let s=""
      switch (type){
        case 0 :
          s="未完成"
          break
        case 1 :
          s="已完成"
          break
      }
      return s;
    }
  },
  mounted() {
    this.getAwardsList()
  },
  methods:{
    getAwardsList(){
      let params = {
        page:this.page,
        pageSize:this.pageSize
      }
      if(this.$route.query.sid){
        params.sid = parseInt(this.$route.query.sid)
      }
      getAwardsList(params).then(res=>{
        if(res.code===0){
          this.tableData=res.data.list
          this.total=res.data.total
        }else {
          this.tableData=[]
          this.total=0
        }
      })
    },
    // 下一页
    handleCurrentChange(page) {
      this.page = page
      this.getAwardsList();
    },

    // 每页显示条数
    handleSizeChange(size) {
      this.pageSize = size
      this.getAwardsList();
    },
  }
}
</script>

<style scoped>

</style>