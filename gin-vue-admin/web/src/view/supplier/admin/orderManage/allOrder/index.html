<m-card class="search-box">
    <el-form :model="formData" class="search-term" label-width="80px" inline>
        <el-form-item>
            <el-input placeholder="请输入" v-model="orderSearchCondition" class="line-input-width" clearable>
                <el-select v-model="orderSearchConditionTag" slot="prepend">
                    <el-option :label="item.name" :value="item.value" v-for="item in orderSearchConditions">
                    </el-option>
                </el-select>
            </el-input>
        </el-form-item>
        <el-form-item>
            <el-input placeholder="请输入" v-model="orderGoodsNameCondition" class="line-input" clearable>
                <span slot="prepend">商品名称</span>
            </el-input>
        </el-form-item>
        <el-form-item>
            <div class="line-input" >
                <div class="line-box ">
                    <span >支付方式</span>
                </div>
                <el-select class="w100" filterable clearable v-model="orderPaymentTypeConditionTag">
                    <el-option :key="item.id" :label="item.name" :value="item.code"
                                v-for="item in orderPaymentTypeConditions">
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="line-input" >
                <div class="line-box ">
                    <span >订单状态</span>
                </div>
                <el-select class="w100" filterable clearable v-model="orderStatusConditionsTag">
                    <el-option :key="item.id" :label="item.name" :value="item.value"
                        v-for="item in orderStatusConditions">
                    </el-option>
                </el-select>
            </div>
        </el-form-item>
        <el-form-item>
            <div class="line-input" >
                <div class="line-box ">
                    <span >售后状态</span>
                </div>
                <el-select class="w100" filterable clearable v-model="afterSaleStatus">
                    <el-option label="退换货" :value="1"></el-option>
                    <el-option label="已退款" :value="2"></el-option>
                </el-select>
            </div>
        </el-form-item>
        <!-- <el-form-item>
            <el-input placeholder="请输入" v-model="note" class="line-input" clearable>
                <span slot="prepend">订单备注</span>
            </el-input>
        </el-form-item> -->
        <br/>
        <el-form-item>
            <div class="line-input-date line-input">
                <div class="line-box">
                    <el-select class="w100" v-model="dateType">
                        <el-option :key="item.id" :label="item.label" :value="item.value"
                                    v-for="item in dateTypeOptios"></el-option>
                    </el-select>
                </div>
                <div class="f fac">
                        <el-date-picker class="w100" placeholder="开始日期" type="datetime"
                                        v-model="formData.d1">
                        </el-date-picker>
                    <p class="title-3">至</p>
                        <el-date-picker class="w100" placeholder="结束日期" type="datetime"
                                        v-model="formData.d2">
                        </el-date-picker>
                </div>
            </div>
        </el-form-item>
        <el-form-item label-width="0px">
            <div class="f fac dateBtnBox">
                <span :class="dateActive === item.value?'is_active':''" :key="item.id"
                    @click="handleDateTab(item)"
                    v-for="item in dateList">{{ item.name }}</span>
            </div>
        </el-form-item>
        <br/>
        <el-form-item>
            <el-button type="primary" @click="searchOrder()">搜索</el-button>
            <el-button @click="orderExport">导出</el-button>
            <el-button type="text" @click="clearSearchCondition()">重置搜索条件</el-button>
        </el-form-item>
    </el-form>
    <el-tabs v-model="orderStatus" type="card" class="mt25 order-tabs" @tab-click="handleTabsClick">
        <el-tab-pane v-for="item in orderStatusConditions" :key="item.id"
                     :label="`${item.name} ${item.total !== null?item.total:''}`" :name="item.value">
        </el-tab-pane>
    </el-tabs>

    <div class="table-box">
        <el-table :data="[{}]" class="table-head">
            <el-table-column label="商品" width="300"></el-table-column>
            <el-table-column label="单件(元)/数量" width="200" align="center"></el-table-column>
            <el-table-column label="手机号(ID)/会员昵称" width="200" align="center"></el-table-column>
            <el-table-column label="付款方式/配送方式" width="200" align="center"></el-table-column>
            <el-table-column label="小计/运费/应付款" width="250" align="center"></el-table-column>
            <el-table-column label="订单状态" width="200" align="center"></el-table-column>
            <el-table-column label="操作" align="center"></el-table-column>
        </el-table>
        <div v-for="item in orderList" :key="item.id">
            <el-table :data="item.order_items" class="table-cont" :span-method="objectSpanMethod">
                <el-table-column>
                    <template slot="header">
                        <div class="w100 f fac fjsb">
                            <div class="f fac fw">
                                <p>订单ID: {{ item.id }}</p>
                                <p>订单编号: {{ item.order_sn }}</p>
                                <p v-if="item.third_order_sn">第三方订单编号: {{ item.third_order_sn }}</p>
                                <p v-if="item.pay_info.pay_sn">支付单号: {{ item.pay_info.pay_sn }}</p>
                                <p v-if="item.gather_supply_id > 0">供应链单号:
                                    <span v-if="item.gather_supply_sn">
                                        {{ item.gather_supply_sn }}
                                        <span v-if="item.gather_supply.category_id === 2 || item.gather_supply.category_id === 6" class="color-red">
                                            {{ item.gather_supply_msg }}
                                        </span>
                                    </span>
                                    <span v-else-if="!item.gather_supply_sn && (item.gather_supply.category_id === 4 || item.gather_supply.category_id === 1 || item.gather_supply.category_id === 2 || item.gather_supply.category_id === 6 || item.gather_supply.category_id === 7)"
                                          class="color-red">
                                        订单异常<span v-if="item.gather_supply_msg"> {{ item.gather_supply_msg }}</span>
                                    </span>
                                </p>
                                <p v-if="orderStatus === '2' || orderStatus === '3'">发货时间: {{
                                        item.sent_at | formatDate
                                    }}</p>
                                <p v-else>下单时间: {{ item.created_at | formatDate }}</p>
                                <!-- <p class="supplier-p" v-if="item.supplier.name">{{ item.supplier.name }}</p> -->
                                <!-- <el-button type="text" v-if="item.supplier.name">
                                  {{ item.supplier.name }}
                                </el-button> -->
                            </div>

                            <!--                            <a href="javascript:;" slot="reference" class="primary close-order"-->
                            <!--                               @click="openDetail(item)">查看详情</a>-->

                        </div>
                    </template>
                    <el-table-column width="300">
                        <template slot-scope="scope">
                            <div class="f fac goods-box">
                                <m-image style="width: 60px;height:60px" :src="scope.row.image_url">
                                </m-image>
                                <div class="f1">
                                    <!--题目两行缩略class： <p class="hiddenText2"> -->
                                    <p>
                                        <!--                                    <el-button type="text"
                                                                                       @click="$_blank('/layout/productManage/productManageCreate',{id:scope.row.product_id})">
                                                                                {{ scope.row.title }} {{ scope.row.sku_title }}
                                                                            </el-button>-->
                                        <a href="javascript:;" style="color: #155bd4;"
                                           @click="$_blank('/layout/productManage/productManageCreate',{id:scope.row.product_id})">
                                            {{ scope.row.title }}
                                        </a>
                                    </p>
                                    <p style="color: #a7a7a7;">
                                        规格: {{ scope.row.sku_title }}
                                    </p>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200">
                        <template slot-scope="scope">
                            <div class="comm-box" style="width: 85%;">
                                <p>供货单价: {{  scope.row.amount / scope.row.qty  | formatF2Y }}</p>
                                <p>数量: {{ scope.row.qty }}</p>
                                <p>金额小计: {{ scope.row.amount | formatF2Y }}</p>
                                <p>子订单号: {{ scope.row.id }}</p>
                                <p v-if="item.gather_supply_type === 119">供应链子单号: {{ scope.row.gather_supply_sn }}</p>
                                <p v-if="scope.row.refund_status === 0" class="color-red">售后完成</p>
                                <el-button style="padding: 0;margin:0;"
                                           v-if="scope.row.after_sales.id"
                                           class="color-red" type="text" @click="openAfterDetail(scope.row)">查看售后
                                </el-button>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200">
                        <template slot-scope="scope">
                            <div class="comm-box">
                                <p class="title-3">
                                    <el-button type="text">
                                        <span v-if="order_field_hide.mobile">{{ item.user.username }}</span>
                                        <span v-if="order_field_hide.id">({{ item.user.id }})</span>
                                    </el-button>
                                </p>
                                <p class="title-3" v-if="order_field_hide.name">{{ item.user ? item.user.nickname : '' }}</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200">
                        <template slot-scope="scope">
                            <div class="comm-box">
                                <p class="title-3">{{ item.pay_type }}</p>
                                <p class="title-3">快递</p>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="250">
                        <template slot-scope="scope">
                            <div class="comm-box" style="width: 85%;">
                                <div>
                                    <p>供货金额: ￥{{ item.supply_amount | formatF2Y }}</p>
                                </div>
                                <div>
                                    <p>运费: ￥{{ item.freight | formatF2Y }}</p>
                                </div>
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column width="200">
                        <template slot-scope="scope">
                            <div class="comm-box text-center">
                                <p class="title-3">
                                    {{ item.status | formatStatus }}
                                </p>
                                <!-- <el-button type="text" v-show="item.status === -1 || item.status >= 2" @click="openPackageLogistics(item)">
                                    查看物流
                                </el-button> -->
                            </div>
                        </template>
                    </el-table-column>
                    <el-table-column>
                        <template slot-scope="scope">
                            <div class="title-3">
                                <el-button type="text" v-for=" btn in item.button"
                                           @click="orderOperationDialog(item.id,btn)">{{
                                        btn.title
                                    }}
                                </el-button>
<!--                                <el-button v-if="item.da_hang_erp_order.id && item.da_hang_erp_order.status !== -2 " @click="pushDachanghangOrder(item)" class="color-red"-->
<!--                                           type="text">推送至大昌行-->
<!--                                </el-button>-->
                            </div>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table>
            <div class="table-foot-box">
                <div class="f fac fjsb">
                    <p> {{ assemblyAddress(item.shipping_address) }}</p>
                    <div class="f fac">
                        <p @click="dialogIsShow(item)" class="color-red shou mr10">
                            <span style="display: inline-block;width: 150px; overflow:hidden; white-space:nowrap;text-overflow:ellipsis;"
                                  v-if="item.note">备注 : {{
                                    item.note
                                }}</span>
                            <span v-else>备注</span>
                        </p>
                        <template v-if="item.print_button">
                            <span class="cgray mr10">{{ item.print_button[0].title }}</span>
                            <el-dropdown trigger="click" class="hauto">
                                <a href="javascript:;" class="primary close-order">打印电子面单</a>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item v-for="(btn,bindex) in item.print_button">
                                        <p v-if="bindex !== 0" @click="handlePrintClick(btn,item.id)">{{ btn.title }}</p>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </template>
                        <a href="javascript:;" class="primary close-order" :class="item.print_button ? 'ml10' : ''"
                        @click="openDetail(item)">查看详情</a>
                        <el-tag type="danger" class="ml10" v-if="item.da_hang_erp_order.status === -1">{{item.da_hang_erp_order.push_error_msg }}</el-tag>
                    </div>
                    <!-- <p>{{item.shipping_address.mobile}}</p>
                      <p class="addr-p">
                          {{assemblyAddress(item.shipping_address)}}
                          {{item.province}} {{item.city}} {{item.county}} {{item.detail}}
                      </p> -->
                    <p v-if="item.remark" style="margin-left: 0;color: red">买家留言: {{item.remark}}</p>

                </div>
            </div>
        </div>
        <el-pagination background :current-page="page" :page-size="pageSize" :page-sizes="[10, 30, 50, 100]"
                       :style="{display: 'flex',justifyContent: 'flex-end',marginRight: '20px'}" :total="total"
                       @current-change="handleCurrentChange" @size-change="handleSizeChange"
                       layout="total, sizes, prev, pager, next, jumper"></el-pagination>
        <order-detail ref="orderDetail"></order-detail>
        <user-detail ref="userDetail"></user-detail>
        <order-shipments-dialog ref="orderShipmentsDialog" @reload="initOrder"></order-shipments-dialog>
        <edit-logistics-dialog ref="editLogisticsDialog"></edit-logistics-dialog>
        <detail-after-sale ref="detailAfterSale" @reload="initOrder"></detail-after-sale>
        <package-logistics-dialog ref="packageLogisticsDialog"></package-logistics-dialog>
        <print-surface-single-dialog ref="PrintSurfaceSingleDialog" @reload="initOrder"></print-surface-single-dialog>
        <multiple-packages-dialog ref="MultiplePackagesDialog" @getData="printPackages"></multiple-packages-dialog>
        <el-dialog :before-close="handleDialogClose" :visible="isShow" title="备注" top="40vh" width="600px">
            <el-input
                    :rows="6"
                    placeholder="请输入内容"
                    type="textarea"
                    v-model="dialogForm.note">
            </el-input>
            <div class="dialog-footer" slot="footer">
                <el-button @click="confirmNote" type="primary">确 定</el-button>
                <el-button @click="handleDialogClose">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</m-card>